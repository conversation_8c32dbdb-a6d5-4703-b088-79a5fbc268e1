<template>
	<div class="animate__animated animate__fadeIn pa">
		<!-- <div class="cot">
			<div class="ic">
				<van-icon name="description" size="0.5rem" color="#333" />
			</div>
			<div class="t">{{ $t("new").t37 }}</div>
		</div> -->
		<div class="flex-column-item" style="padding: 0.5rem 0;">
			<img src="../../assets/v5/nodata.png" style="width: 1.28rem;height: 1.28rem;" alt="" />
			<div class="t">暫無數據</div>
		</div>
		<!-- <van-empty description="暫無數據" /> -->
	</div>
</template>

<script>
	export default {
		name: "noData",
		props: {},
		data() {
			return {};
		},
		components: {},
		methods: {},
		created() {},
		computed: {},
	};
</script>

<style scoped lang="less">
	.pa{
		background: #232429;
		border-radius: 0.19rem;
	}
	.cot {
		text-align: center;
		padding: 0.5rem 0;
		margin: 0 auto;

		.t {
			font-size: 0.12rem;
			margin-top: 0.1rem;
			font-family: Inter, Inter;
			font-weight: 400;
			font-size: 0.12rem;
			color: #8E8E93;
		}
	}

	.t {
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		font-size: 0.14rem;
		color: #999;
	}
</style>