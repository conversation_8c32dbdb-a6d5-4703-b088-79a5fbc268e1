<template>
  <div class="page ">
    <div class="logo"><img :src="$cfg.logo" /></div>

    <div class="form">
      <div class="title">
        {{ $t("login").title3 }}
      </div>

      <div class="center">
        <div class="icon success animate__animated animate__fadeIn"></div>
        <div class="t t-center">
          {{ $t("new").a49 }}
        </div>
      </div>

      <div class="btn animate__animated animate__fadeIn">
        {{ $t("new").a50 }}
      </div>
    </div>
    <loading ref="loading" />
  </div>
</template>
<script>
import { mapMutations } from "vuex";
export default {
  name: "login",
  data() {
    return {
      cfg: {},
    };
  },
  components: {},
  mounted() {
    // this.getConfig();
    setTimeout(() => {
      this.login();
    }, 3000);
  },
  methods: {
    ...mapMutations(["saveToken", "saveAccount"]),
    // 获取配置logo
    async getConfig() {
      const res = await this.$server.post("/common/config", { type: 'twd' });
      let val = {};
      res.data.forEach((vo) => {
        val[vo.name] = vo.value;
      });
      let imgurl = this.$server.url.imgUrl.replace("/api", "");
      val.logo = imgurl + val.logo;

      if (process.env.NODE_ENV === "development") {
        val.logo = require("../../assets/login/logo.png");
      }
      this.cfg = val;
    },

    async login() {
      let data = {
        account: localStorage.getItem("AC"),
        password: localStorage.getItem("PS"),
      };
      this.$refs.loading.open(); //开启加载

      const res = await this.$server.post("/user/login", data);
      this.$refs.loading.close();

      if (res.status == 1) {
        this.$toast(this.$t(res.msg));

        // 登录时首次，赋值token
        this.$server.defaults.headers.token = res.data.token;
        this.$server.defaults.headers.account = res.data.account;

        this.saveToken(res.data.token);
        this.saveAccount(res.data.account);

        setTimeout(() => {
          this.$router.push({ path: "/home/<USER>" });
        }, 1000);
      } else {
        Toast({
          message: res.msg,
          duration: 2000,
        });
      }
    },
  },
};
</script>
<style lang="less" scoped="scoped">
.page {
  width: 100vw;
  min-height: 100vh;
  background-color: #144856;
  padding: 1rem 0.2rem 0.5rem;
  .logo {
    width: 1.06rem;
    height: 1.06rem;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto 0.45rem;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .form {
    background: #ffffff;
    padding: 0 0 0.1rem;
    .title {
      background: #48a070;
      font-weight: 500;
      font-size: 0.17rem;
      color: #000000;
      padding: 0.12rem 0;
      text-align: center;
      margin-bottom: 0.12rem;
    }

    .center {
      padding: 0.5rem 0;
      .icon {
        margin: 0 auto 0.2rem;
      }
      .t {
        font-weight: 500;
        font-size: 0.14rem;
        color: #000000;
      }
    }

    .btn {
      padding: 0.15rem;
      background: #48a070;
      border-radius: 0.22rem 0.22rem 0.22rem 0.22rem;
      margin: 0.1rem auto;
      font-weight: 500;
      font-size: 0.16rem;
      color: #000000;
      text-align: center;
      width: 75%;
    }
  }
}
</style>
