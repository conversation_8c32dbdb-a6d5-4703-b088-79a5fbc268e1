<template>
	<div class="page ">
		<top-back :title="$t('ETF募集')"></top-back>
		<!-- 图 -->
		<!--   <div class="chart"></div> -->
		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<van-skeleton title :row="25" :loading="loading">
				<no-data v-if="isShow"></no-data>
				<div class="list" v-if="chooseList.length">
					<div class="item" v-for="(item, index) in chooseList" :key="index">
						<div class="info">
							<div class="name">{{ item.name }}</div>
							<div class="code">{{ item.sender }}</div>
						</div>
						<div class="flex flex-c">
							<div class="icon ic animate__animated animate__fadeIn" :class="(index + 1) % 2 == 0 ? 'gd1' : 'gd0'"></div>
						</div>
						<div class="mid flex">
							<div class="flex-column-item flex-1">
								<div class="t4">{{ $formatMoney(item.capital) }}</div>
								<div class="t3">募集規模</div>
							</div>
							<div class="flex-column-item flex-1">
								<div class="t4">{{ item.yprofit }}</div>
								<!-- <div class="t4">{{ item.dprofit }}-{{ item.yprofit }}</div> -->
								<div class="t3">發行預期收益</div>
								<!-- <div class="icon animate__animated animate__fadeIn" :class="item.dprofit.indexOf('-') > -1 ? 'down02' : 'up02'" style="margin-left: 0.05rem;"></div> -->
							</div>
						</div>
						<div class="flex flex-b bot flex-wrap">
							<div class="flex flex-b">
								<div class="t3">募集價格</div>
								<div class="t4">{{$formatMoney(item.mjjg)||'-'}}元</div>
							</div>
							<div class="flex flex-b">
								<div class="t3">總發行量</div>
								<div class="t4">{{$formatMoney(item.mjzs)||'-'}}張</div>
							</div>
							<div class="flex flex-b">
								<div class="t3">上市價格（暫定）</div>
								<div class="t4">{{$formatMoney(item.ssjg)||'-'}}元</div>
							</div>
							<div class="flex flex-b">
								<div class="t3">募集時間</div>
								<div class="t4">{{$formatDate('YYYY-MM-DD',item.end*1000)}}</div>
								<!-- <div class="t4">{{item.scday}}天</div> -->
							</div>
							<div class="flex flex-b">
								<div class="t3">募集到期股利</div>
								<div class="t4">{{item.xmjs}}</div>
							</div>
						</div>
						<div class="">
							<div v-if="item.state == -1" class="s-btn flex flex-c">已終止</div>
							<div v-else class="s-btn flex flex-c" @click="stockDetails(item)">參與募集</div>
						</div>

					</div>
				</div>
			</van-skeleton>
		</van-pull-refresh>
		<van-popup v-model="show" round position="center" :style="{ width: '90%' }">
			<div class="pop">
				<div class="pop-title">{{ stockObj.name }}</div>
				<div class="pop-price t-c">
					<div class="t">募集規模：<span>{{ $formatMoney(stockObj.capital) }}</span></div>
				</div>
				<div class="pad">
					<div class="ipt ">
						<div class="tt">募集金額</div>
						<input class="flex-1" v-model="buyObj.handle" type="number" placeholder="請輸入募集金額" />
						<div class="flex">
							<div class="t1">最低募集金額</div>
							<div class="t2">{{ $formatMoney(stockObj.money) }}</div>
						</div>
					</div>
					<div class="flex flex-b">
						<div class="b-btn" @click="show = false">
							{{ $t("取消") }}
						</div>
						<div @click="buyFn" class="defbtn">參與募集</div>
					</div>
				</div>
			</div>
			<!-- <div class="icon close animate__animated animate__fadeIn" @click="show = false"></div> -->
		</van-popup>

		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "fllow",
		data() {
			return {
				loading: true,
				isShow: false,
				isLoading: false,
				currmentIndex: 0,
				stockObj: {},
				chooseList: [
					// {
					// 	name: "名称",
					// 	sender: "发单人",
					// 	capital: 100000,
					// 	dprofit: "-1%",
					// 	yprofit: "10%",
					// 	money: 1000,
					// },
					// {
					// 	name: "名称",
					// 	sender: "发单人",
					// 	capital: 100000,
					// 	dprofit: "1%",
					// 	yprofit: "10%",
					// 	money: 1000,
					// },
				],
				show: false,
				buyObj: {
					handle: "",
				},
			};
		},
		computed: {},
		mounted() {
			this.getNew();
		},
		methods: {
			// 下拉刷新
			onRefresh() {
				this.isShow = false;
				this.getNew();
			},
			getNew() {
				this.$server.post("/trade/productlist", {
					type: "twd"
				}).then((res) => {
					this.isLoading = false;
					this.loading = false;

					res.data.forEach((vo) => {
						let now = Date.now() * 0.001;
						// vo.state = now > vo.locktime ? -1 : now > vo.end ? 1 : 0;
						vo.state = now > vo.end ? -1 : 1
					});
					this.chooseList = res.data;
					if (!this.chooseList.length) {
						this.isShow = true;
					}
				});
			},
			stockDetails(stock) {
				this.stockObj = stock;
				this.show = true;
			},
			buyFn() {
				if (this.buyObj.handle < Number(this.stockObj.money)) {
					this.$toast("最低募集金額" + this.stockObj.money);
					return;
				}
				this.$refs.loading.open(); //开启加载

				this.$server
					.post("/trade/buy_product ", {
						id: this.stockObj.id,
						money: this.buyObj.handle,
						type: "twd",
					})
					.then((res) => {
						this.$refs.loading.close(); //关闭加载

						this.show = false;
						if (res.status == 1) {
							this.$toast(res.msg.replace('跟单成功', '申請募集'));
						}
					});
			},
		},
	};
</script>
<style scoped lang="less">
	.page {
		padding: 0.5rem 0.12rem 0.1rem;
		min-height: 100vh;
		position: relative;
	}

	::v-deep .van-skeleton__row {
		background-color: transparent !important;
	}

	::v-deep .van-skeleton__title {
		background-color: transparent !important;
	}

	.list {
		padding: 0.12rem 0;

		.item {
			background: #FFFFFF;
			border-radius: 0.06rem 0.06rem 0.06rem 0.06rem;
			margin-bottom: 0.15rem;
			overflow: hidden;
			.info {
				padding: 0.12rem;
				.name {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 0.16rem;
					color: #000000;
				}

				.code {
					margin-top: 0.05rem;
					font-family: PingFang TC, PingFang TC;
					font-weight: 400;
					font-size: 0.12rem;
					color: #989898;
				}
			}
		}
		.ic {
			margin: 0 0 0.1rem;
			width: 3.35rem;
			height: 1.1rem;
			border-radius: 0.09rem;
		}
		.s-btn {
			margin: 0.12rem;
			height: 0.34rem;
			background: linear-gradient( 90deg, #1B167A 0%, #4383C7 100%);
			border-radius: 0.06rem 0.06rem 0.06rem 0.06rem;
			padding: 0 0.1rem;
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			font-size: 0.14rem;
			color: #fff;
			line-height: 0.34rem;
		}
		.mid{
			margin: 0 0.12rem;
			height: 0.4rem;
			background: linear-gradient( 90deg, #F74F28 0%, #FF7757 98%);
			border-radius: 0.04rem;
			.flex-1{
				height: 0.4rem;
				padding: 0.05rem 0;
				&:last-child{
					padding: 0.05rem 0;
					background: linear-gradient( 90deg, #1B167A 0%, #4383C7 100%);
					border-radius: 0.04rem;
					clip-path: polygon(10% 0%, 100% 0%, 100% 100%, 0% 100%);
				}
			}
			.t3 {
				font-family: DIN Next LT Pro, DIN Next LT Pro;
				font-weight: 400;
				font-size: 0.11rem;
				color: #FFFFFF;
			}
			.t4 {
				font-family: DIN Next LT Pro, DIN Next LT Pro;
				font-weight: 500;
				font-size: 0.14rem;
				color: #FFFFFF;
			}
		}
		.bot {
			padding: 0.12rem 0.12rem 0;
			.flex-b{
				width: 48%;
				padding: 0.05rem 0;
			}
			.t4 {
				font-family: DIN Next LT Pro, DIN Next LT Pro;
				font-weight: 500;
				font-size: 0.16rem;
				color: #232323;
			}

			.t3 {
				font-family: DIN Next LT Pro, DIN Next LT Pro;
				font-weight: 500;
				font-size: 0.12rem;
				color: #979797;
			}
		}

	}

	.van-popup {
		background-color: transparent;
	}

	.close {
		margin: 0.1rem auto 0;
	}

	.pop {
		background: #fff;
		border-radius: 0.12rem;
		position: relative;

		.pad {
			padding: 0.1rem 0.15rem;
		}

		.btips {
			padding: 0.15rem 0;
			line-height: 0.24rem;

			.t2 {
				color: #a91111;
			}
		}

		.pop-title {
			padding: 0.12rem;
			background: linear-gradient( 90deg, #1B167A 0%, #4383C7 100%);
			padding: 0.12rem;
			font-size: 0.16rem;
			text-align: center;
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			font-size: 0.18rem;
			color: #fff;
		}

		.pop-price {
			padding: 0.15rem 0;

			.t {
				font-size: 0.16rem;
				color: #9a9fa5;

				span {
					color: #dc183b;
				}
			}

			.t1 {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.12rem;
				color: #666666;
				margin-bottom: 0.05rem;
			}
		}

		.ipt {
			.tt {
				padding: 0.1rem 0;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.12rem;
				color: #B6B2AD;
			}

			input {
				line-height: 0.38rem;
				line-height: 0.44rem;
				height: 0.44rem;
				background: rgba(27,22,122,0);
				border-radius: 0.04rem 0.04rem 0.04rem 0.04rem;
				border: 0.01rem solid #CECECE;
				padding: 0 0.1rem;
				margin: 0.1rem 0;
				width: 100%;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.12rem;
				color: #000;

				&::placeholder {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.12rem;
					color: #999;
				}
			}

			.t1 {
				font-size: 0.12rem;
				color: #9a9fa5;
			}

			.t2 {
				margin-left: 0.1rem;
				font-size: 0.12rem;
				color: #dc183b;
			}

			.mt10 {
				margin-top: 0.05rem;
			}
		}

		.pop-num {
			margin-top: 0.15rem;

			input {
				margin: 0.05rem 0;
				width: 100%;
				background: #f8f8f8;
				border-radius: 0.06rem;
				border: 0.01rem solid #b8b8b8;
				height: 0.4rem;
				line-height: 0.4rem;
				padding: 0 0.1rem;

				&::placeholder {
					font-size: 0.12rem;
					color: #606060;
				}
			}
		}

		.txt {
			font-size: 0.12rem;
			color: #9a9fa5;

			span {
				font-size: 0.12rem;
				color: #c5585e;
			}
		}

		.b-btn {
			width: 48%;
			margin: 0;
			font-size: 0.14rem;
			height: 0.4rem;
			background: #BCBCBC;
			border-radius: 0.06rem;
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			font-size: 0.14rem;
			color: #FFFFFF;
			line-height: 0.4rem;
		}

		.defbtn {
			width: 48%;
			height: 0.4rem;
		}
	}
</style>