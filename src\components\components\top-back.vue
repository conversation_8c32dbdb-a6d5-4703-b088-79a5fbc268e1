<template>
	<div class="">
		<div class="header flex">
			<div class="icon back animate__animated animate__fadeIn" @click="goBack" v-if="!back"></div>
			<div class="t">{{ title }}</div>
		</div>
	</div>
</template>

<script>
	export default {
		name: "topBack",
		props: {
			title: {
				type: String,
				default: "",
			},
			back:{
				type:Boolean,
				default:false
			}
		},
		data() {
			return {};
		},
		components: {},
		created() {},
		computed: {},
		methods: {
			goBack() {
        this.$router.go(-1)
			},
		},
	};
</script>

<style scoped lang="less">
	.header {
		width: 100%;
		position: fixed;
		height: 0.5rem;
		top: 0;
		left: 0;
		z-index: 999;
		padding: 0.12rem;
		background: #18191B;
		.t {
			position: absolute;
			left: 50%;
			transform: translateX(-50%);
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			font-size: 0.16rem;
			color: #FFFFFF;
		}
	}
</style>