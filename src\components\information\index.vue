<template>
	<!-- 个人 -->
	<div class="page">
		<!-- <top-back title="我的帳戶"></top-back> -->
		<!-- <top-menu title="我的"></top-menu> -->
		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<div class="pg">
				<div class="top">
					<div class="flex flex-b user-info" @click="$toPage('/information/setting')">
						<div class="flex">
							<div class="icon user animate__animated animate__fadeIn"></div>
							<div style="margin-left: 0.1rem;">
								<div class="name">{{ userInfo.realname }},你好</div>
								<div class="account">{{ userInfo.account }}</div>
								<div class="rz-btn" @click="$toPage('/information/authInfo')">
									{{ userInfo.is_true == 1 ? "已實名" : "未實名" }}
								</div>
							</div>
						</div>
						<div class="flex">
							<div class="xy">信用評定</div>
							<van-circle v-model="userInfo.xyf" layer-color="#fff" color="#91E051" :rate="30" :speed="100" :text="userInfo.xyf+'分'" stroke-width='150' size="50" />
						</div>
						<!-- <div class="exit" @click="exit">登出</div> -->
					</div>
					<div class="money">
						<div class="flex flex-c tops" v-if="false">
							<!-- 图 -->
							<!-- <div class="animate__animated animate__fadeIn" style="width: 40%;">
								<div class="" id="main"></div>
							</div> -->
							<div class="flex flex-c">
								<div class="t">總資產</div>
								<!-- <img src="../../assets/v5/bageye.png" @click="show=!show" v-if="show" style="width:.15rem;height: .15rem;">
								<img src="../../assets/v5/bagby.png" @click="show=!show" v-else style="width:.15rem;height: .15rem;"> -->
							</div>
							<div class="num">{{ show ? $formatMoney(totalAssets) || 0 : "****" }}</div>
						</div>
						<div class="nums flex flex-b">
							<div class="item">
								<div class="flex">
									<div class="t2">總資產</div>
									<img src="../../assets/v5/bageye.png" @click="show=!show" v-if="show" style="width:.15rem;height: .15rem;">
									<img src="../../assets/v5/bagby.png" @click="show=!show" v-else style="width:.15rem;height: .15rem;">
								</div>
								<div class="t1">{{ show ? $formatMoney(totalAssets) || 0 : "****" }}</div>
							</div>
							<div class="item">
								<div class="t2">可用資金</div>
								<div class="t1">{{ show ? $formatMoney(userInfo.twd) || 0 : "****" }}</div>
							</div>
							<!-- <div class="item">
								<div class="t2">使用中資金</div>
								<div class="t1">{{ show ? $formatMoney(freezeAssets) || 0 : "****" }}</div>
							</div> -->
							<div class="item">
								<div class="t2">當前盈虧</div>
								<div class="t1">{{ show ? $formatMoney(totalProfit) || 0 : "****" }}</div>
							</div>
							<!-- <div class="item">
								<div class="t1">{{ $formatMoney(userInfo.dollar) }}</div>
								<div class="t2">{{ $t("new").a36 }} (USD)</div>
							</div> -->
						</div>
						<div class="btns flex flex-b">
							<div class="btn" v-for="(item, i) in tabList" :key="i" @click="goUrl(item.url)" v-if="i<4">
								<div class="icon" :class="item.icon"></div>
								<div class="t">{{item.name}}</div>
							</div>
						</div>
					</div>
				</div>
				<div class="func">
					<div class="tab-item flex flex-b" v-for="(item, i) in tabList" :key="i" v-if="i>3" @click="goUrl(item.url)">
						<div class="flex">
							<div class="icon" :class="item.icon"></div>
							<div class="t">{{ item.name }}</div>
						</div>
						<div class="icon jtr animate__animated animate__fadeIn"></div>
					</div>
				</div>
			</div>
		</van-pull-refresh>
		<loading ref="loading" />
		<tab-bar :current="4"></tab-bar>
	</div>
</template>

<script>
	import moneyCom from '../components/money.vue'

	export default {
		name: "information",
		props: {},
		data() {
			return {
				show: true,
				loading: true,
				isLoading: false,
				userInfo: {},
				tabList: [
					{
						icon: 'ico1',
						name: '資金提現',
						url: '/information/cashOut'
					},
					{
						name: '交易明細',
						icon: 'ico2',
						url: '/information/fundRecord'
					},
					{
						name: '銀行卡管理',
						icon: 'ico3',
						url: '/information/bankList'
					},
					{
						name: "實名認證",
						icon: "ico4",
						url: "/information/authInfo",
					},
					{
						name: "我的檔案",
						icon: "ico5",
						url: "/information/setting",
					},
					// {
					//   icon:'cz',
					//   name:'儲值',
					//   url:'/information/recharge'
					// },
					// {
					// 	name: "通知中心",
					// 	icon: "m1",
					// 	url: "/information/userInfo",
					// },
					{
						name: "資金密碼",
						icon: "ico6",
						url: "/information/fundPass",
					},
					{
						name: "登入密碼",
						icon: "ico7",
						url: "/information/loginPass",
					},
					{
						name: "聯絡客服",
						icon: "ico8",
						// url: "/information/kef",
						url: "kefu",
					},
					{
						name: "交易規則",
						icon: "ico9",
						url: "/information/tradeRule",
					},
					{
						name: "登出登入",
						icon: "ico10",
						url: "exit",
					},
					// {
					// 	name: "關於我們",
					// 	icon: "ico7",
					// 	url: "/information/aboutUs",
					// },
					// {
					// 	name: "我的資產",
					// 	icon: "ico2",
					// 	url: "/information/payBag",
					// },
					// {
					// 	name: "我的持倉",
					// 	icon: "ico3",
					// 	url: "/trade/index",
					// },
					// {
					//   name: this.$t("mine").menu9,
					//   icon: "m6",
					//   url: "/information/changeLang",
					// },
				],
				freezeAssets: 0,
				totalAssets: 0,
				totalProfit: 0,
				szAssets: 0,
			};
		},
		components: {
			moneyCom
		},
		computed: {},
		mounted() {
			this.getTotalProfit();
			this.getTotalAssets();
		},
		methods: {
			exit() {
				let obj = localStorage.getItem('rembInfo')
				localStorage.clear();
				localStorage.setItem("rembInfo", JSON.stringify(obj));
				this.$toPage("/login/login");
			},
			//盈利資金
			async getTotalProfit() {
				const res = await this.$server.post("/trade/userstocklist", {
					type: "twd",
				});
				this.isLoading = false;
				let fdyk = 0;
				let arr = [];
				if (res.status == 1) {
					res.data.forEach((item) => {
						arr.push(Number(item.yingkui));
					});

					fdyk = arr.reduce((a, b) => a + b, 0);
				}
				this.totalProfit = fdyk;
			},
			// 获取总资产
			async getTotalAssets() {
				// this.$refs.loading.open(); //开启加载

				const res = await this.$server.post("/user/getUserinfo", {
					type: "twd"
				});
				if (res.status == 1) {
					this.userInfo = res.data;
				}
				let abalance = Number(res.data.twd || 0); //用户可用余额
				// 获取跟单盈亏
				const res1 = await this.$server.post("/trade/userproductlist", {
					type: "twd",
				});
				let followingFreeze = 0; //量化跟单的认缴冻结
				if (res1.status == 1 && res1.data && Array.isArray(res1.data)) {
					let arr = [];
					res1.data.forEach((item) => {
						if (item.status == 0) {
							arr.push(Number(item.money)); //跟单冻结的资金
						}
					});
					let total = arr.reduce((a, b) => a + b, 0);
					followingFreeze = total;
				}

				// 申購列表的投資
				const res2 = await this.$server.post("/trade/usernewstocklist", {
					type: "twd",
				});
				let subscriptionProfit = 0; //新股申请的认缴冻结
				if (res2.status == 1 && res2.data && Array.isArray(res2.data)) {
					let arr = [];
					let arr1 = [];
					res2.data.forEach((item) => {
						if (item.status == 1) {
							arr.push(Number(item.rjmoney == "-" ? 0 : item.rjmoney)); //認繳的资金
						}
						if (item.status == 0) {
							arr1.push(Number(item.rjmoney == "-" ? 0 : item.rjmoney)); //计算申购认缴等于0的
						}
					});
					let total = arr.reduce((a, b) => a + b, 0);
					let total1 = arr1.reduce((a, b) => a + b, 0);

					subscriptionProfit = total + total1;
				}

				// 競拍列表的投資 ??? 還沒有對接口
				// const res3 = await this.$server.post("/trade/newstocklists", {type: "twd"});
				// let subscriptionProfit1 = 0; //新股申请的认缴冻结
				// if (res3.status == 1 && res3.data && Array.isArray(res3.data)) {
				//   let arr = [];
				//   let arr1 = [];
				//   res3.data.forEach((item) => {
				//     if (item.status == 1) {
				//       arr.push(Number(item.rjmoney == "-" ? 0 : item.rjmoney)); //認繳的资金
				//     }
				//     if (item.status == 0) {
				//       arr1.push(Number(item.rjmoney == "-" ? 0 : item.rjmoney)); //计算申购认缴等于0的
				//     }
				//   });
				//   let total = arr.reduce((a, b) => a + b, 0);
				//   let total1 = arr1.reduce((a, b) => a + b, 0);

				//   subscriptionProfit1 = total + total1;
				// }

				// 持仓中的申请冻结
				const res4 = await this.$server.post("/trade/userstocklist", {
					type: "twd",
				});
				let positionFreeze = 0;
				delete res4.data.ccsz;
				delete res4.data.fdyk;
				let dataArr = Object.values(res4.data);
				if (dataArr.length) {
					let arr = [];
					dataArr.forEach((item) => {
						if (item.status == 0) {
							// arr.push(Number(item.credit)); //認繳的资金
							arr.push(
								Number(item.buy_price) * Number(item.stock_num) + item.yingkui
							); //認繳的资金  买入本金+盈利
						}
					});

					let total = arr.reduce((a, b) => a + b, 0);
					positionFreeze = total;
				}

				// 大宗交易申请冻结
				const res5 = await this.$server.post("/trade/ustockslist", {
					type: "twd",
				});
				let bigDealFreeze = 0;
				if (res5.status == 1 && res5.data && Array.isArray(res5.data)) {
					let arr = [];
					res5.data.forEach((item) => {
						if (item.status == 0) {
							arr.push(Number(item.credit)); //認繳的资金
						}
					});
					let total = arr.reduce((a, b) => a + b, 0);
					bigDealFreeze = total;
				}

				// 冻结资产
				this.freezeAssets =
					subscriptionProfit + followingFreeze + bigDealFreeze + positionFreeze;
				//  +subscriptionProfit1;
				// 总资产
				this.totalAssets = abalance + this.freezeAssets;
				this.$refs.loading.close();
			},
			exit() {
				let obj = localStorage.getItem('rembInfo')
				localStorage.clear();
				localStorage.setItem("rembInfo", JSON.stringify(obj));
				this.$toPage("/login/login");
			},
			goUrl(url) {
				if (url == "kefu") {
					this.getConfig();
				}else if(url == "exit"){
					this.exit();
				} else {
					this.$toPage(url);
				}
			},
			// 下拉刷新
			onRefresh() {
				this.getTotalProfit();
				this.getTotalAssets();
			},
			async getConfig() {
				this.$refs.loading.open();
				const res = await this.$server.post("/common/config", {
					type: "twd"
				});
				let val = {};
				res.data.forEach((vo) => {
					val[vo.name] = vo.value;
				});
				this.$refs.loading.close();
				this.$openUrl(val.kefu); //重新获取
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		min-height: 100vh;
		padding: 0.2rem 0.12rem 1rem;
	}

	.top {
		padding: 0.12rem;
		margin-bottom: 0.12rem;
		background: linear-gradient(45deg, #B2E56E, #EFFDB0);
		border-radius: 0.13rem;
		.user-info{
			position: relative;
		}
		.user{
			width: 0.42rem;
		}
		.name {
			font-family: PingFang SC;
			font-weight: 600;
			font-size: 0.18rem;
			color: #000000;
		}

		.account {
			margin: 0.05rem 0 0;
			font-family: PingFang SC;
			font-weight: 400;
			font-size: 0.13rem;
			color: #000000;
		}

		.rz-btn {
			position: absolute;
			left: 0;
			bottom: 0.05rem;
			height: 0.19rem;
			line-height: 0.19rem;
			padding: 0 0.1rem;
			background: rgba(255, 255, 255, 0.8);
			border-radius: 0.09rem;
			font-family: PingFang SC;
			font-weight: 500;
			font-size: 0.09rem;
			color: #3E9126;
		}
		.xy{
			margin-right: 0.1rem;
			font-family: PingFang SC;
			font-weight: 400;
			font-size: 0.11rem;
			color: #3F5C1E;
		}
		.exit {
			height: 0.36rem;
			padding: 0 0.1rem;
			border-radius: 0.04rem;
			border: 0.01rem solid #01655D;
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			font-size: 0.16rem;
			color: #01655D;
			line-height: 0.36rem;
		}
	}
	.money {
		margin: 0.1rem 0 0;
		padding: 0.12rem 0;
		.tops {
			border-bottom: 0.01rem solid #F3F3F3;
			.t {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 0.12rem;
				color: #000;
			}

			.num {
				text-align: center;
				padding: 0.1rem 0;
				font-family: PingFang SC, PingFang SC;
				font-weight: 600;
				font-size: 0.2rem;
				color: #2C2C2C;
			}
		}

		.nums {
			.item {
				padding: 0.12rem 0;
				.t1 {
					margin-top: 0.1rem;
					font-family: PingFang SC;
					font-weight: 600;
					font-size: 0.13rem;
					color: #000000;
				}

				.t2 {
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 0.13rem;
					color: #666666;
				}
			}
		}
		.btns{
			background: rgba(255, 255, 255, 0.3);
			border-radius: 0.09rem;
			padding: 0.12rem;
			.btn{
				width: 46%;
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 0.16rem;
				color: #FFFFFF;
				.icon{
					margin: 0 auto;
				}
				.t{
					text-align: center;
					margin-top: 0.05rem;
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 0.11rem;
					color: #000000;
				}
			}
		}
	}

	.func {
		background: #262729;
		border-radius: 0.13rem;
		padding: 0.06rem 0;
		.tab-item {
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			font-size: 0.16rem;
			color: #333333;
			padding: 0.15rem 0.12rem;
			border-bottom: 0.01rem solid #444444;
			&:last-child{
				border-bottom: none;
			}
			.t {
				margin-left: 0.1rem;
				font-family: PingFang SC;
				font-weight: 400;
				font-size: 0.14rem;
				color: #999999;
			}
		}
	}
</style>