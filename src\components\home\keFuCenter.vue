<template>
	<div class="pages">
		<top-back title="雲端助手"></top-back>
		<!-- <div class="flex-column-item">
			<img src="../../assets/v3/kfCenter.png" style="width: 3.45rem;height: 1.96rem;" alt="" />
		</div> -->
		<div class="flex flex-b box" @click="goUrl('kefu')">
			<div class="flex">
				<div class="icon kf2"></div>
				<div class="t">雲端助手1</div>
			</div>
			<div class="icon arrow"></div>
		</div>
		<div class="flex flex-b box" @click="goUrl('kefu1')">
			<div class="flex">
				<div class="icon kf2"></div>
				<div class="t">雲端助手2</div>
			</div>
			<div class="icon arrow"></div>
		</div>
	</div>
</template>

<script>
	export default {
		name: "favorite",
		props: {},
		components: {},
		data() {
			return {};
		},
		computed: {},
		created() {

		},
		mounted() {},
		methods: {
			async getConfig(type) {
				this.$server.post('/user/getUserinfo', {}).then((ras) => {
					if (ras.status == 1) {
						this.$server.post('/common/config', {
							type: "all"
						}).then((res) => {
							let list = res.data;
							let listLength = list.length;
							let a;
							for (a = 0; a < listLength; a++) {
								if (type == 'kefu') {
									if (list[a].name === 'kefu') {
										this.$openUrl(list[a].value);
									}
								}
								if (type == 'down') {
									if (list[a].name === 'down') {
										this.$openUrl(list[a].value);
									}
								}
							}
						});
					}
				});
				return;
			},
			goUrl(url) {
				if (url == 'kefu') {
					this.getConfig('kefu')
				} else if (url == 'kefu1') {
					this.getConfig('down')
				}
			},
		},
	};
</script>

<style lang="less" scoped>
	.pages {
		padding: 0.6rem 0.15rem 1rem;

		.box {
			margin: 0.2rem 0;
			height: 0.56rem;
			background: #151C2C;
			border-radius: 0.09rem;
			padding: 0 0.1rem;

			.t {
				margin-left: 0.1rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
				color: #fff;
			}
		}
	}
</style>