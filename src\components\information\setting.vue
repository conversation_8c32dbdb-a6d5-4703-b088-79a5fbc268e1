<template>
	<div class="page ">
		<top-back title="個人資料"></top-back>
		<div class="list">
			<div class="flex-column-item">
				<div class="icon user animate__animated animate__fadeIn"></div>
				<div class="name">{{ userInfo.realname }}</div>
				<div class="account">{{ userInfo.account }}</div>
			</div>
			<!-- <div class="exit" @click="exit">登出</div> -->
			<!-- <div class="t">頭像</div> -->
			<!-- <div class="item flex flex-b">
				<div class="t">姓名</div>
				<div class="t1">{{ userInfo.realname }}</div>
			</div> -->
			<div class="info">
				<div class="item flex flex-b">
					<div class="flex">
						<img src="../../assets/v6/set1.png" style="width: 0.22rem;height: 0.22rem;" alt="" />
						<div class="t">真實姓名</div>
					</div>
					<div class="t1">{{ userInfo.realname }}</div>
				</div>
				<div class="item flex flex-b">
					<div class="flex">
						<img src="../../assets/v6/set2.png" style="width: 0.22rem;height: 0.22rem;" alt="" />
						<div class="t">身分證號</div>
					</div>
					<div class="t1">{{ userInfo.account }}</div>
				</div>
				<div class="item flex flex-b" @click="$toPage('/information/authInfo')">
					<div class="flex">
						<img src="../../assets/v6/set3.png" style="width: 0.22rem;height: 0.22rem;" alt="" />
						<div class="t">實名認證</div>
					</div>
					<div class="flex">
						<div class="rz">{{ userInfo.is_true == 1 ? "已實名" : "審核中" }}</div>
						<div class="icon jtr"></div>
					</div>
				</div>
			</div>
			
		</div>
		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "setting",
		props: {},
		data() {
			return {
				userInfo: {},
			};
		},
		components: {},
		created() {
			this.initData();
		},
		computed: {},
		methods: {
			exit() {
				let obj = localStorage.getItem('rembInfo')
				localStorage.clear();
				localStorage.setItem("rembInfo", JSON.stringify(obj));
				this.$toPage("/login/login");
			},
			initData() {
				this.$server.post("/user/getUserinfo", {
					type: "twd"
				}).then((res) => {
					if (res.status == 1) {
						this.userInfo = res.data;
					}
				});
			},
		},
	};
</script>

<style scoped lang="less">
	.exit {
		height: 0.28rem;
		background: linear-gradient( 90deg, #1B167A 0%, #4383C7 100%);
		border-radius: 0.04rem 0.04rem 0.04rem 0.04rem;
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 0.14rem;
		color: #fff;
		line-height: 0.28rem;
		text-align: center;
		padding: 0 0.12rem;
	}

	.page {
		padding: 0.5rem 0.14rem 0.2rem;
		min-height: 100vh;

		.list {
			padding: 0.12rem;
			background: linear-gradient(45deg, #B2E56E, #EFFDB0);
			border-radius: 0.13rem;
			.name{
				font-family: PingFang SC;
				font-weight: 500;
				font-size: 0.14rem;
				color: #000000;
			}
			.account{
				font-family: PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
				color: #000000;
			}
			.info{
				margin-top: .1rem;
				background: rgba(255, 255, 255, 0.3);
				border-radius: 0.09rem;
				padding: 0.05rem 0.12rem;
			}
			.item {
				padding: 0.1rem 0;
				.t {
					margin-left: 0.1rem;
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 0.13rem;
					color: #000000;
				}
				.t1 {
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 0.13rem;
					color: #000000;
				}
				.rz{
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 0.13rem;
					color: #3F5C1E;
				}
			}
		}
	}
</style>