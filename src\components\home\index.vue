<template>
	<div class="page">
		<top-menu :home="true"></top-menu>
		<!-- <div class="info">
			<div class="t">Hi,{{ userInfo.realname || userInfo.account }}</div>
		</div> -->
		<div class="money" v-if="false">
			<div class="tops">
				<!-- 图 -->
				<!-- <div class="animate__animated animate__fadeIn" style="width: 40%;">
					<div class="" id="main"></div>
				</div> -->
				<div class="" @click="show = !show">
					<div class="t flex">{{ $t('總資產') }}
						<div class="icon animate__animated animate__fadeIn" :class="show ? 'bageye' : 'bagby'"
							style="margin-left: 0.12rem;">
						</div>
					</div>
					<div class="num">{{ show ? $formatMoney(totalAssets) || 0 : "****" }}</div>
				</div>
			</div>
			<div class="nums flex flex-b">
				<div class="item">
					<div class="t2">{{ $t('可用資金') }}</div>
					<div class="t1">{{ show ? $formatMoney(userInfo.zar) || 0 : "****" }}</div>
				</div>
				<div class="item">
					<div class="t2">{{ $t('盈虧資金') }}</div>
					<div class="t1">{{ show ? $formatMoney(ykAssets) || 0 : "****" }}</div>
				</div>
				<div class="item">
					<div class="t2">{{ $t('使用中資金') }}</div>
					<div class="t1">{{ show ? $formatMoney(freezeAssets) || 0 : "****" }}</div>
				</div>
				<!-- <div class="item">
					<div class="t1">{{ $formatMoney(userInfo.dollar) }}</div>
					<div class="t2">{{ $t("new").a36 }} (USD)</div>
				</div> -->
			</div>
		</div>
		<!-- <div class="navs flex flex-b">
			<div class="navItem flex flex-c" :class="{active:navIndex==0}" @click="navIndex=0">市場概況</div>
			<div class="navItem flex flex-c" :class="{active:navIndex==1}" @click="navIndex=1">常用功能</div>
		</div> -->
		<!-- <div class="banner">
			<img src="../../assets/v5/banner.png" style="width: 100%;height: 1.21rem;" alt="" />
		</div> -->
		<!-- <div class="banner" @click="goUrl({url: '/market/Plate1'})">
			<img src="../../assets/v5/banner.png" alt="" style="width: 100%;border-radius: .1rem;" />
			<div class="t1">隨時隨地</div>
			<div class="t2">進行仿皮快速的證券交易</div>
		</div> -->
		<!-- 功能列表 -->
		<div class="func">
			<!-- <div class="title">我的功能</div> -->
			<div class="tab flex flex-b">
				<div class="tab-item flex-column-item" v-for="(item, i) in tabList" :key="i" @click="goUrl(item)">
					<div class="icon animate__animated animate__fadeIn" :class="item.icon"></div>
					<div class="t">{{ item.name }}</div>
				</div>
			</div>
		</div>
		<div class="zx-cot">
			<div class="tit">{{ $t('指數') }}</div>
			<div class="zx-list flex">
				<div class="zx-item" v-for="(item, i) in indexList" :key="i">
					<div class="name">{{ item.symbolName }}</div>
					<div class="price" :class="item.change > 0 ? 'red' : 'green'">
						{{ $formatMoney(item.price) }}
					</div>
					<div class="per flex flex-b" :class="item.change > 0 ? 'red' : 'green'">
						<div>{{ $formatMoney(item.change) }}</div>
						<!-- <div class="icon" :class="item.change > 0 ? 'up' : 'down'" style="margin-left: 0.1rem;"></div> -->
						<div>{{ item.changePercent }}</div>
					</div>
				</div>
			</div>
		</div>
		<div style="margin: 0 0.12rem;" class="newsbg" @click="goUrl({url: '/home/<USER>'})" v-if="false">
			<img src="../../assets/3.jpg" alt="" style="width: 100%;margin-top: .1rem;border-radius: .1rem;" />
			<div class="news">全球航海王一次買！00960航運ETF發行價15元、30日起申購 短線價差報酬537%</div>
		</div>
		<div class="rm" v-if="false">
			<div class="tt flex flex-b">
				<div class="t">{{ $t('熱門股票') }}</div>
				<div class="icon gpIcon"></div>
			</div>
			<!-- 切換 -->
			<!-- <div class="change flex">
				<div class="change-item" v-for="(item, index) in sort"
					:class="{ active: item.id === sortIndex }" :key="index" @click="changeSort(item.id)">
					{{ item.name }}
				</div>
			</div> -->
			<div class="rm-list">
				<div class="titles flex flex-b">
					<div class="flex-2">{{ $t('名稱') }}</div>
					<div class="flex-1">{{ $t('價格') }}</div>
					<div class="flex-2 t-c flex flex-e">
						{{ $t('漲跌') }}
						<!-- <div class="icon" :class="show ? 'zq' : 'dq'" @click="changeList(0)"></div> -->
					</div>
					<div class="flex-2 t-r flex flex-e">
						{{ $t('漲跌幅') }}（%）
						<!-- <div class="icon" :class="show1 ? 'zq' : 'dq'" @click="changeList(1)"></div> -->
					</div>
				</div>
				<div class="rm-item flex" v-for="(item, i) in list" :key="i"
					@click="$toDetail(`/market/stockDetail?symbol=${item.symbol}`, item)" v-if="i < 6">
					<div class="flex flex-2">
						<!-- <img :src="item.logo" style="width: 0.32rem;border-radius: 50%;margin-right: 0.05rem;" alt="" /> -->
						<div>
							<div class="name">{{ item.local_name }}</div>
							<div class="code">{{ item.symbol }}</div>
						</div>
					</div>
					<div class="price flex flex-c flex-1" :class="item.gain > 0 ? 'red-bg' : 'green-bg'">
						{{ $formatMoney(item.price) }}
					</div>
					<!-- <div class="price">{{ $formatMoney(item.volume / 1000000) }} M</div> -->
					<div class="per flex-2 flex flex-e" :class="item.gain > 0 ? 'red' : 'green'">
						{{ $formatMoney(item.gainValue) }}
					</div>
					<div class="per flex-2 flex flex-e" :class="item.gain > 0 ? 'red' : 'green'">
						<!-- <div class="icon animate__animated animate__fadeIn" :class="item.gain > 0 ? 'up' : 'down'"></div> -->
						{{ item.gain }}%
					</div>
				</div>
				<div class="moreBtn flex flex-c" @click="$toPage('/favorite/moreList')">{{ $t('查看更多') }}</div>
			</div>
		</div>
		<!-- <div class="news">
			<div class="tt flex flex-b" @click="$toPage('/home/<USER>')">
				<div class="t">熱門資訊</div>
				<div class="t1 flex">更多<div class="icon arrow"></div>
				</div>
			</div>
			<div class="newsList02 flex">
				<div class="newItem" v-for="(item, i) in newList" :key="i" v-if="i<5" @click="toNewsDetail(item)">
					<img :src="item.img" alt="" style="width: 2.15rem;height: 1.21rem;border-radius: 0.1rem;margin-right: 0.1rem;" />
				</div>
			</div>
		</div> -->
		<div class="news">
			<div class="tit">{{ $t('熱門資訊') }}</div>
			<div class="newsList">
				<div class="newItem flex flex-b" v-for="(item, i) in newList" :key="i"  @click="toNewsDetail(item)">
					<div>
						<div class="title">{{item.title}}</div>
						<div class="time">{{ $formatDate("YYYY-MM-DD", item.created * 1000) }}</div>
					</div>
					<!-- <img :src="item.img" alt="" style="width: 0.89rem;height: 0.64rem;border-radius: 0.1rem;margin-left: 0.1rem;" /> -->
				</div>
			</div>
			<!-- <div class="moreBtn flex flex-c" @click="$toPage('/home/<USER>')">查看更多</div> -->
		</div>
		<!--点击按钮加载效果组件 -->
		<loading ref="loading" />

		<van-popup v-model="zqShow" round mode="center" :style="{ width: '80%' }" border-radius="14">
			<van-swipe class="my-swipe" :autoplay="3000" indicator-color="white">
				<van-swipe-item v-for="item in zhongList">
					<div class="zqbg">
						<img src="../../assets/yh.gif"
							style="width: 100%;height: 100%;position: absolute;top: 0;left: 0;" />
						<div class="title">{{ $t('page.home.congratulations') }}{{item.buy_type==0?$t('申購'):$t('page.home.auctionWon')}}</div>
						<div class="title" v-if="item.buy_type==0">{{ $t('page.home.newStockWon') }}</div>

						<div class="flex flex-b wd-100" style="margin-top: 50rpx;">
							<div class="item">{{ $t('page.home.stockName') }}</div>
							<div class="item1">{{item.stock_name}}</div>
						</div>
						<div class="flex flex-b wd-100">
							<div class="item">{{ $t('page.home.stockCode') }}</div>
							<div class="item1">{{item.stock_code}}</div>
						</div>
						<div class="flex flex-b wd-100">
							<div class="item">{{item.buy_type==0?$t('page.home.winningShares'):$t('page.home.successfulShares')}}</div>
							<div class="item1">{{item.lucky_total/1000}}</div>
						</div>
						<div class="flex flex-b wd-100">
							<div class="item">{{item.buy_type==0?$t('page.home.paymentAmount'):$t('page.home.commitmentAmount')}}</div>
							<div class="item1">{{$formatMoney(item.lucky_total*item.apply_price)}}</div>
						</div>
						<div class="btns" @click="tozqDetail">{{ $t('page.home.applicationDetails') }}</div>
					</div>
				</van-swipe-item>
			</van-swipe>
		</van-popup>
		<tab-bar :current="0"></tab-bar>
	</div>
</template>

<script>
	import Vue from 'vue';
	import { Swipe, SwipeItem } from 'vant';

	Vue.use(Swipe);
	Vue.use(SwipeItem);
	export default {
		name: "home",
		props: {},
		data() {
			return {
				zqShow: false,
				zhong: '',
				zhongList: [],
				readNum: 0,
				kfUrl: "",
				indexList: [],
				userInfo: {},
				show: false,
				freezeAssets: 0,
				totalAssets: 0,
				ykAssets: 0,
				totalProfit: 0,
				currentNews: '',
				newList: [],

				sortIndex: 0,
				list: [],
				navIndex: 0,
			};
		},
		computed: {
			sort() {
				return [{
						name: this.$t("page.home.declining"),
						id: 1
					},
					{
						name: this.$t("page.home.rising"),
						id: 0
					},
					{
						name: this.$t("page.home.tradingVolume"),
						id: 2
					},
					// { name: this.$t("創高榜"), id: 3 },
					// { name: this.$t("創低榜"), id: 4 },
				];
			},
			tabList() {
				return [
					{
						name: this.$t("鉅額交易"),
						icon: "h1",
						url: "/market/Plate",
					},
					{
						name: this.$t("存股借券"),
						icon: "h2",
						url: "/home/<USER>",
					},
					{
						name: this.$t("主力庫存"),
						icon: "h3",
						url: "/market/Plate1",
					},
					{
						name: this.$t("競價拍賣"),
						icon: "h3",
						url: "/home/<USER>",
					},
					{
						name: this.$t("大宗交易"),
						icon: "h5",
						url: "/home/<USER>",
					},
					{
						name: this.$t("儲值"),
						icon: "h6",
						url: "/information/recharge",
					},
					{
						name: this.$t("提領"),
						icon: "h7",
						url: "/information/cashOut",
					},
					{
						name: this.$t("自選"),
						icon: "h8",
						url: "/favorite/index?type=2",
					},
				];
			}
		},
		created() {
			this.readData()
			this.getIndexList();
			this.getList();
			this.getNews()
			this.getClickZhong()
		},
		mounted() {
			this.getTotalAssets();
			this.getNews()
		},
		methods: {
			getClickZhong() {
				let _this = this;
				let now = new Date();
				let unixEpoch = new Date(0);
				let diff = now - unixEpoch;
				let fullDay = Math.floor(diff / (1000 * 60 * 60 * 24))
				if (window.localStorage.getItem('zhongDayNew')) {
					if (parseInt(window.localStorage.getItem('zhongDayNew')) < fullDay) {
						_this.getZhong(fullDay)
					}
				} else {
					_this.getZhong(fullDay)
				}

			},
			getZhong(fullDay) {
				let _this = this;
				this.$server
					.post("/transaction/is_zhongqian", {
						type: 'zar'
					}).then((str) => {
						if (str) {
							if (str.data.length > 0) {
								_this.zqShow = true
								_this.zhong = str.data[0]
								_this.zhongList = str.data
								window.localStorage.setItem('zhongDayNew', fullDay.toString())
							}
						}
					});
			},
			toNewsDetail(item) {
				this.$storage.save("newsDetail", item);
				this.$refs.loading.open(); //开启加载

				setTimeout(() => {
					this.$refs.loading.close(); //关闭加载

					this.$toPage("/home/<USER>");
				}, 1000);
			},
			readData() {
				this.$server.post("/user/notice", {
					type: "zar"
				}).then((res) => {
					if (res.status == 1) {
						let list = res.data;
						let length = list.length
						let a
						for (a = 0; a < length; a++) {
							let read = localStorage.getItem("readMsg")
							let oldRead = JSON.parse(read)
							let hasValue = oldRead.id.includes(list[a].id.toString())
							if (!hasValue) {
								this.readNum += 1
							}
						}
					}
				});
			},
			goUrl(item) {
				if (item.url == 'kefu') {
					this.getConfig()
				} else if (item.url == 'kefu1') {
					this.getConfig1()
				} else {
					this.$toPage(item.url)
				}
			},
			toNewsDetail(item) {
				this.$storage.save("newsDetail", item);
				this.$refs.loading.open(); //开启加载

				setTimeout(() => {
					this.$refs.loading.close(); //关闭加载

					this.$toPage("/home/<USER>");
				}, 1000);
			},
			getNews() {
				this.$server
					.post("/common/newss", {
						exchange: "za",
						lang: "cn",
					})
					.then((res) => {
						let arr = res.data.result;
						this.newList = arr
						arr.forEach(item => {
							if (item.img) {
								this.currentNews = item
								return
							}
						})
					})
			},
			// 获取总资产
			async getTotalAssets() {
				let url = "/trade/userstocklist";


				this.$server.post(url, {
					type: "zar"
				}).then(async (res) => {
					this.isLoading = false; //下拉刷新状态
					this.loading3 = false;
					if (res.status == 1) {
						let arr = res.data;

						let szArr = []; //列表市值
						let ykArr = []; //列表盈亏
						let arr1 = []; //認繳的资金  买入本金+盈利(持仓冻结)
						// let arr2 = [];

						arr.forEach((item) => {
							szArr.push(Number(item.market_value) + Number(item.yingkui));
							ykArr.push(Number(item.yingkui));
							arr1.push(
								Number(item.buy_price) * Number(item.stock_num) +
								Number(item.yingkui)
							);
							// arr2.push(Number(item.buy_price) * Number(item.stock_num));

						});

						this.szAssets = szArr.reduce((a, b) => a + b, 0);
						this.ykAssets = ykArr.reduce((a, b) => a + b, 0);
						this.freezeAssets = arr1.reduce((a, b) => a + b, 0);

						// let total2 = arr2.reduce((a, b) => a + b, 0);
						// this.percent = ((this.ykAssets / (total2 || 1)) * 100).toFixed(2); //总盈亏比例

						const res1 = await this.$server.post("/user/getUserinfo", {
							type: "zar",
						});
						if (res.status == 1) {
							this.userInfo = res1.data;
						}

						// 总资产 可用+持仓资金
						this.totalAssets = Number(this.userInfo.zar) + this.freezeAssets;


					}
					this.$refs.loading.close(); //关闭加载
				});
			},
			getIndexList() {
				this.$server.post("/parameter/zhishulist", {
					type: "zar"
				}).then((res) => {
					this.loading1 = false;
					this.indexList = res.data;
					this.indexList.forEach(item => {
						this.$nextTick(() => {
							// this.getKdata(item)
						})
					})
				});
			},
			getKdata(item) {
				if (!item.kline) {
					return
				}
				let chartDom = document.getElementById("chart" + item.symbol);
				let resArr = []
				resArr = item.kline.splice(item.kline.length - 30, item.kline.length - 1)

				let xdata = []
				for (let i = 0; i < resArr.length; i++) {
					if (resArr[i].t) {
						xdata.push(resArr[i].t)
					}
				}
				xdata = xdata.map(item => (isNaN(item) ? null : item));


				let sdata = []
				for (let i = 0; i < resArr.length; i++) {
					if (resArr[i].c) {
						sdata.push(resArr[i].c)
					}
				}

				const max = Math.max(...sdata);

				// 找出最小值
				const min = Math.min(...sdata);

				sdata = sdata.map(item => (isNaN(item) ? null : item));
				var myChart = echarts.init(chartDom);
				let areaStyle = []
				if (Number(item.gain) >= 0) {
					areaStyle = [{
							offset: 0,
							color: 'rgba(235, 51, 59, 0.3)'
						}, // 0% 处的颜色（透明）
						{
							offset: 1,
							color: 'rgba(220, 0, 35, .1)'
						} // 100% 处的颜色（不透明）
					]
				} else {
					areaStyle = [{
							offset: 0,
							color: 'rgba(26, 174, 82, 0.3)'
						}, // 0% 处的颜色（透明）
						{
							offset: 1,
							color: 'rgba(26, 174, 82, 0.1)'
						} // 100% 处的颜色（不透明）
					]
				}
				var option = {
					grid: {
						top: 5,
						left: 5,
						right: 5,
						bottom: 5
					},
					tooltip: {
						show: false // 不显示提示框
					},
					legend: {
						show: false // 不显示图例
					},
					xAxis: {
						show: false,
						type: 'category',
						data: xdata
					},
					yAxis: {
						scale: true,
						show: false,
						type: 'value'
					},
					series: [{
						data: sdata,
						type: 'line',
						symbol: 'none',
						smooth: true,
						itemStyle: {
							normal: {
								lineStyle: {
									color: Number(item.gain) >= 0 ? '#eb333b' : '#1aae52'
								}
							}
						},
						areaStyle: {
							color: new echarts.graphic.LinearGradient(
								0, 0, 0, 1, // 渐变的起点与终点
								areaStyle
							)
						}
					}]
				};

				myChart.setOption(option);
			},
			async getConfig() {
				this.$refs.loading.open();
				const res = await this.$server.post("/common/config", {
					type: "all"
				});
				let val = {};
				res.data.forEach((vo) => {
					val[vo.name] = vo.value;
				});
				this.$refs.loading.close();
				this.$openUrl(val.kefu); //重新获取
			},
			async getConfig1() {
				this.$refs.loading.open();
				const res = await this.$server.post("/common/config", {
					type: "all"
				});
				let val = {};
				res.data.forEach((vo) => {
					val[vo.name] = vo.value;
				});
				this.$refs.loading.close();
				this.$openUrl(val.down); //重新获取
			},
			async getConfigwappic() {
				this.$refs.loading.open();
				const res = await this.$server.post("/common/config", {
					type: "all"
				});
				let val = {};
				res.data.forEach((vo) => {
					val[vo.name] = vo.value;
				});
				this.$refs.loading.close();
				this.$openUrl(val.wappic); //重新获取
			},
			changeSort(type) {
				// this.$refs.loading.open();
				this.sortIndex = type;
				// this.list = [];
				// if (type == 0) {
				// 	this.type = "zhangfb";
				// } else if (type == 1) {
				// 	this.type = "diefb";
				// } else {
				// 	this.type = "chengje";
				// }
				// this.getList();
				if (type == 2) {
					this.show = !this.show;

					// 成交額 排序
					if (this.show) {
						this.list = this.list.sort(
							(a, b) => Number(b.volume) - Number(a.volume)
						);
					} else {
						this.list = this.list.sort(
							(a, b) => Number(a.volume) - Number(b.volume)
						);
					}
				} else if (type == 1) {
					this.list = this.list.sort(
						(a, b) => Number(b.gainValue) - Number(a.gainValue)
					);
				} else {
					this.list = this.list.sort(
						(a, b) => Number(a.gainValue) - Number(b.gainValue)
					);
				}
				//  else {
				// 	this.show1 = !this.show1;
				// 	// 漲跌排序
				// 	if (this.show1) {
				// 		this.list = this.list.sort(
				// 			(a, b) => Number(b.gainValue) - Number(a.gainValue)
				// 		);
				// 	} else {
				// 		this.list = this.list.sort(
				// 			(a, b) => Number(a.gainValue) - Number(b.gainValue)
				// 		);
				// 	}
				// }

			},
			getList() {
				this.$server
					.post("/parameter/top", {
						type: "zar",
					})
					.then((res) => {
						this.$refs.loading && this.$refs.loading.close();
						this.loading = false;
						this.isLoading = false;
						this.list = res.data;
						// console.log("res", this.list);
					});
			},
		},
	};
</script>

<style scoped lang="less">
	::-webkit-scrollbar {
		display: none;
	}
	.newsbg {
		position: relative;
		.news {
			width: 100%;
			height: 58px;
			position: absolute;
			top: 50%;
			background: rgba(8, 14, 18, 0.44);
			border-radius: 0px 0px 0px 0px;
			display: flex;
			align-content: center;
			justify-content: center;
			padding: 10px 0;

			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 14px;
			color: #FFFFFF;
			text-align: center;
		}
	}

	.wdzh {
		width: .26rem !important;
		height: .26rem !important;
	}

	.banner {
		margin: 0.12rem;
		position: relative;

		.t1 {
			position: absolute;
			left: 0.2rem;
			top: 0.3rem;
			font-family: PingFangSC, PingFang SC;
			font-weight: 600;
			font-size: 0.18rem;
			color: #8C5709;
		}

		.t2 {
			position: absolute;
			left: 0.2rem;
			top: 0.6rem;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 0.16rem;
			color: #8C5709;
		}

		.my-swipe .van-swipe-item {
			width: 23.4% !important;
			margin-right: 0.24rem;
			color: #fff;
			font-size: 20px;
			line-height: 150px;
			text-align: center;
			border-radius: 0.08rem;
			background-color: #00aaff;
		}
	}

	::v-deep .van-swipe__indicator {
		width: 8px;
		height: 8px;
		color: #949494;
	}

	.page {
		padding: 0.6rem 0 1rem;
	}

	.topTit {
		padding: 0 0 0.1rem;

		.title {
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			font-size: 0.14rem;
			color: #01655D;
		}
	}

	.zx-cot {
		padding: 0.12rem;
		.tit{
			padding: 0 .1rem;
			margin-bottom: .1rem;
			font-family: PingFang SC;
			font-weight: 500;
			font-size: 0.14rem;
			color: #888888;
		}
		.zx-list {
			overflow-x: scroll;
			.zx-item {
				min-width: 32%;
				margin-right: 0.1rem;
				padding: 0.12rem;
				background: linear-gradient(270deg, #35463E, #23262B);
				border-radius: 0.06rem;

				.name {
					text-align: left;
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 0.11rem;
					color: #888888;
				}

				.price {
					padding: 0.05rem 0;
					font-family: PingFang SC;
					font-weight: 600;
					font-size: 0.14rem;
				}

				.per {
					width: 100%;
					div {
						font-family: PingFang SC;
						font-weight: 400;
						font-size: 0.1rem;
					}
				}
			}
		}
	}

	.info {
		padding: 0.2rem 0.12rem;
		background: #002F7C;

		.t {
			font-family: PingFangSC, PingFang SC;
			font-weight: 600;
			font-size: 0.19rem;
			color: #FFFFFF;
		}
	}

	.money {
		background: #002f7c;
		padding: 0.12rem 0.12rem 0.6rem;

		.tops {
			.t {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
				color: #FFFFFF;
			}

			.num {
				padding: 0.05rem 0;
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 0.39rem;
				color: #FFFFFF;
			}
		}

		.nums {
			background: #000000;
			border-radius: 0.09rem;
			padding: 0.1rem 0;

			.item {
				border-right: 0.01rem solid rgba(67, 137, 218, 0.5);
				flex: 1;
				padding: 0 0.12rem;

				&:last-child {
					border-right: none;
				}

				.t1 {
					font-family: PingFangSC, PingFang SC;
					font-weight: 600;
					font-size: 0.18rem;
					color: #FFFFFF;
				}

				.t2 {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.13rem;
					color: #4389DA;
					margin-bottom: 0.05rem;
				}
			}
		}
	}

	.navs {
		margin-top: -0.41rem;
		padding: 0 0.1rem;

		.navItem {
			width: 49%;
			height: 0.41rem;
			background: #D8D8D8;
			border-radius: 0.09rem 0.09rem 0rem 0rem;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 0.16rem;
			color: #333333;
			line-height: 0.41rem;
		}

		.active {
			color: #002F7C;
			background: #FFFFFF;
		}
	}

	.func {
		margin: 0 0.12rem;

		.title {
			padding-left: 0.12rem;
			font-family: PingFangSC, PingFang SC;
			font-weight: 600;
			font-size: 0.14rem;
			color: #01655D;
		}

		.tab {
			flex-wrap: wrap;
			.tab-item {
				width: 25%;
				text-align: center;
				padding: 0.12rem 0;
				.t {
					margin-top: 0.1rem;
					font-family: PingFang SC;
					font-weight: 500;
					font-size: 0.12rem;
					color: #888888;
				}
			}
		}
	}

	.rm {
		margin: 0 0.12rem;
		background: #FFFFFF;
		border-radius: 0.05rem;

		.tt {
			padding: 0.12rem;

			.t {
				font-family: PingFang SC;
				font-weight: bold;
				font-size: 0.18rem;
				color: #000000;
			}

			.t1 {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
				color: #8E98A3;
			}
		}

		.change {
			padding: 0.05rem 0.12rem 0.2rem;

			.change-item {
				margin-right: 0.12rem;
				padding: 0 0.2rem;
				height: 0.26rem;
				background: rgba(255, 255, 255, 0.17);
				border-radius: 0.04rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.12rem;
				color: #E1E5ED;
				line-height: 0.26rem;
				text-align: center;

				&.active {
					background: #D6B371;
					border-radius: 0.04rem;
					color: #3C2500;
				}
			}
		}

		.rm-list {
			padding: 0.12rem;

			.titles {
				padding-bottom: 0.12rem;

				div {
					font-family: PingFang SC;
					font-weight: 500;
					font-size: 0.12rem;
					color: #8B8B8B;
				}

				.icon {
					margin-left: 0.05rem;
				}
			}

			.rm-item {
				padding: 0.12rem 0;
				border-bottom: 0.01rem solid #E0E0E0;

				.name {
					font-family: PingFang SC;
					font-weight: 500;
					font-size: 0.12rem;
					color: #333333;
				}

				.code {
					font-family: PingFang SC;
					font-weight: 500;
					font-size: 0.11rem;
					color: #B4B4B4;
				}

				.price {
					height: 0.23rem;
					border-radius: 0.03rem;
					padding: 0 0.05rem;
					font-family: PingFang SC;
					font-weight: 500;
					font-size: 0.12rem;
					color: #FFFFFF;
				}

				.icon {
					margin-right: 0.05rem;
				}

				.per {
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.14rem;
					color: #FFFFFF;
				}
			}
		}
	}

	.moreBtn {
		width: 30%;
		margin: 0.1rem auto 0;
		height: 0.37rem;
		background: #FFFFFF;
		border-radius: 0.19rem;
		border: 0.01rem solid #1A1479;
		font-family: PingFang SC;
		font-weight: 500;
		font-size: 0.16rem;
		color: #333333;
		line-height: 0.37rem;
	}

	.news {
		margin: 0 0.12rem;
		padding-bottom: 0.1rem;
		.tit {
			padding: 0.12rem;
			font-family: PingFang SC;
			font-weight: 500;
			font-size: 0.14rem;
			color: #888888;
		}

		.newsList {
			.newItem {
				padding: 0.12rem;
				background: #232429;
				border-radius: 0.06rem;
				margin-bottom: 0.1rem;
				.time {
					padding-top: 0.05rem;
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 0.11rem;
					color: #95959E;
				}

				.title {
					display: -webkit-box;
					-webkit-line-clamp: 2;
					-webkit-box-orient: vertical;
					overflow: hidden;
					text-overflow: ellipsis;
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 0.13rem;
					color: #95959E;
				}
			}
		}

		.newsList02 {
			margin: 0 0.12rem;
			overflow-x: scroll;

			.newItem {}
		}
	}

	.zqbg {
		padding: .2rem;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
		position: relative;

		.title {
			font-size: .18rem;
			color: #333;
			font-weight: bold;
			text-align: center;
		}

		.item,
		.item1 {
			margin-top: .1rem;
			font-size: .14rem;
			color: #999;
		}

		.item1 {
			color: #333;
		}
	}

	.wd-100 {
		width: 100%;
	}

	.btns {
		margin-top: .3rem;
		font-size: .16rem;
		color: #FF2415;
		font-weight: bold;
		position: relative;
		z-index: 99999;
	}
</style>