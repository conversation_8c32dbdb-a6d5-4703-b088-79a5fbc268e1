<template>
	<!-- 个人 -->
	<div class="page ">
		<top-menu></top-menu>
		<!-- <top-back title="交易帳務" :back="true"></top-back> -->
		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<div class="cot">
				<div class="money">
					<div class="flex flex-b tops ">
						<div class="flex-1">
							<div class="t flex" @click="show = !show">
								總資產<div class="icon animate__animated animate__fadeIn"
									:class="show ? 'bageye' : 'bagby'" style="margin-left: 0.12rem;"></div>
							</div>
							<div class="flex">
								<div class="num">{{ show ? $formatMoney(totalAssets) || 0 : "****" }}</div>
								<div class="txt">
									<!-- <span class="">{{ $t("今日盈亏") }} </span> -->
									<span
										:class="percent > 0 ? 'red' : 'green'">({{ percent > 0 ? "+" : "" }}{{ percent }}%)</span>
								</div>
							</div>
						</div>
						<!-- 图 -->
						<!-- <div class=" animate__animated animate__fadeIn">
							<div class="" id="main"></div>
							<div class="icon lines"></div>
						</div> -->
					</div>
					<div class="nums flex flex-b">
						<div class="item">
							<div class="t2">可用資金</div>
							<div class="t1">
								{{ show ? $formatMoney(userInfo.zar) || 0 : "****" }}
							</div>
						</div>
						<div class="item center">
							<div class="t2">獲利資金</div>
							<div class="t1">
								{{ show ? $formatMoney(ykAssets) || 0 : "****" }}
							</div>
						</div>
						<div class="item">
							<div class="t2">凍結資金</div>
							<div class="t1">
								{{ show ? $formatMoney(freezeAssets) || 0 : "****" }}
							</div>
						</div>
						<!-- <div class="item">
							<div class="t1">{{ $formatMoney(userInfo.dollar) }}</div>
							<div class="t2">{{ $t("new").a36 }} (USD)</div>
						</div> -->
					</div>
				</div>
				<div class="btns">
					<div class="btn flex flex-b animate__animated animate__fadeIn" v-for="(item,index) in tabList" :key="index" @click="$toPage(item.url)">
						<div class="flex">
							<div class="icon" :class="item.icon"></div>
							<div class="name">{{item.name}}</div>
						</div>
						<img src="../../assets/v4/arrowR.png" style="width: 0.12rem;height: 0.14rem;" alt="" />
					</div>
				</div>
				<!-- <div class="title">帳戶</div>
				<div class="btns flex flex-b flex-wrap">
					<div class="btn animate__animated animate__fadeIn flex flex-c" v-for="(item,index) in tabList" :key="index" @click="$toPage(item.url)" v-if="index>5">
						{{item.name}}
					</div>
				</div> -->
				<!-- 记录合并展示 -->
				<template v-if="false">
					<div class="tt flex flex-b">
						<div>{{ $t("流水详情") }}</div>
						<div class="icon sx"></div>
					</div>
					<div class="list">
						<div class="list-item flex flex-b" v-for="(item, i) in logList" :key="i">
							<div>
								<div class="t">{{ $t(item.name) }}</div>
								<div class="t1">{{ $formatDate("YYYY-MM-DD hh:mm:ss", item.time) }}</div>
							</div>
							<div class="t2" :class="Number(item.money) > 0 ? 'red' : 'green'">
								{{ item.money }}
							</div>
						</div>
					</div>
				</template>
			</div>
		</van-pull-refresh>
		<loading ref="loading" />
		<tab-bar :current="4"></tab-bar>
	</div>
</template>

<script>
	import * as echarts from "echarts";
	export default {
		name: "payBag",
		props: {},
		data() {
			return {
				chartData: [],
				show: false,
				loading: true,
				isLoading: false,
				kfUrl: "",
				userInfo: {},
				currentIndex: 0,
				moneyType: ["baht", "hkd", "dollar"],
				tabList: [{
						name: '儲值',
						icon: "m1",
						url: "/information/recharge",
					},
					{
						name: '提領',
						icon: "m2",
						url: "/information/cashOut",
					},
					{
						name: '資金明細',
						icon: "m3",
						url: "/information/fundRecord",
					},
					{
						name: '銀行帳戶',
						icon: "m4",
						url: "/information/bankList",
					},
					{
						name: '借貸',
						icon: "m5",
						url: "/home/<USER>",
					},
					// {
					// 	name: '手續費說明',
					// 	icon: "m1",
					// 	url: "/information/shou",
					// },
					// {
					// 	name: '回報',
					// 	icon: "m1",
					// 	url: "/trade/index?idx=1",
					// },
					// {
					// 	name: '歷史',
					// 	icon: "m1",
					// 	url: "/trade/index?idx=2",
					// },
					// {
					// 	name: this.$t("mine").menu4,
					// 	icon: "m3",
					// 	// url: "/information/authInfo",
					// 	url: "authInfo",
					// },
					// {
					// 	name: this.$t("setting").txt3,
					// 	icon: "m4",
					// 	url: "/information/loginPass",
					// },
					// {
					// 	name: this.$t("setting").txt4,
					// 	icon: "m5",
					// 	url: "/information/fundPass",
					// },
					// {
					// 	name: this.$t("mine").menu10,
					// 	icon: "m6",
					// 	url: "kefu",
					// },
					// {
					// 	name: this.$t("mine").menu9,
					// 	icon: "m7",
					// 	url: "/information/changeLang",
					// },
					// {
					// 	name: this.$t("setting").btn,
					// 	icon: "m8",
					// 	url: "exit",
					// },
					// {
					//   name: this.$t("exchange").title,
					//   icon: "m4",
					//   url: "/information/exChange",
					// },
				],
				totalProfit: 0,
				totalAssets: 0,
				freezeAssets: 0,
				myChart: null,
				logList: [],
				logList1: [],
				logList2: [],
				percent: "",
				ykAssets:0
			};
		},
		computed: {},
		mounted() {
			this.getTotalProfit();
			this.getTotalAssets();
			this.getRecord();
		},
		methods: {
			getRecord() {
				this.$refs.loading.open(); //开启加载
				this.$server
					.post("/user/capitalloglist", {
						type: "dollar",
					})
					.then((res) => {
						if (res.status == 1) {
							for (let i = 0; i < res.data.length; i++) {
								var row = res.data[i];
								const chineseToEnglish = {
									购买股票: this.$t("购买股票"),
									返还资金: this.$t("返还资金"),
									股票代码: this.$t("股票代码"),
									股票名称: this.$t("股票名称"),
									订单编号: this.$t("订单编号"),
									撤單: this.$t("撤單"),
									返回本金和手續費: this.$t("返回本金和手續費"),
									購買股票: this.$t("購買股票"),
									買手續費: this.$t("買手續費"),
									平仓: this.$t("平仓"),
									卖手续费: this.$t("卖手续费"),
									訂單編號: this.$t("訂單編號"),
									股票名稱: this.$t("股票名稱"),
									股票代碼: this.$t("股票代碼"),
									跟單返利: this.$t("跟單返利"),
									發單人: this.$t("發單人"),
									产品名稱: this.$t("产品名稱"),
									訂單編號: this.$t("訂單編號"),
									跟單: this.$t("跟單"),
									借券返利: this.$t("借券返利"),
									借券返還: this.$t("借券返還"),
									跟单返利: this.$t("跟单返利"),
								};

								row.detailed = row.detailed.replace("DHZG", "SUNTON");
								if (row.detailed) {
									const text = row.detailed.replace(/！[\s\S]*?股票名称/, "");
									const regex = new RegExp(
										Object.keys(chineseToEnglish).join("|"),
										"g"
									);
									if (text) {
										const result = text.replace(
											regex,
											(matched) => chineseToEnglish[matched]
										);
										row.detailed = result;
									}
								}
							}
							this.logList1 = res.data;
							this.getRecharge();
						}
					});
			},
			getRecharge() {
				// this.$refs.loading.open(); //开启加载

				this.$server
					.post("/user/rechargelist", {
						type: "dollar",
					})
					.then((res) => {
						// this.$refs.loading.close();

						this.logList2 = res.data;
						this.getWithdrawal();
					});
			},
			getWithdrawal() {
				// this.$refs.loading.open(); //开启加载

				this.$server
					.post("/user/withdrawallist", {
						type: "dollar",
					})
					.then((res) => {
						this.$refs.loading.close();

						let arr = [...this.logList1, ...this.logList2, ...res.data];

						arr.forEach((item) => {
							// item.time = new Date(item.create_time * 1000).getTime();
							item.time = new Date(item.create_time).getTime();

						});
						let lastData = arr.sort((a, b) => b.time - a.time);

						this.logList = lastData;

						// console.log("this.logList", this.logList);
					});
			},
			// 下拉刷新
			onRefresh() {
				// this.getConfig();
				this.getTotalProfit();
				this.getTotalAssets();
			},
			goKefu() {
				this.getConfig();
			},
			//盈利資金
			async getTotalProfit() {
				const res = await this.$server.post("/trade/userstocklist", {
					is_type: 0,
					type: 'zar'
				});
				let fdyk = 0;
				if (res.status == 1) {
					fdyk = Number(res.data.fdyk);
				}
				this.totalProfit = fdyk;
			},
			// 获取总资产
			async getTotalAssets() {
				let url = "/trade/userstocklist";


				this.$server.post(url, {
					type: "zar"
				}).then(async (res) => {
					this.isLoading = false; //下拉刷新状态
					this.loading3 = false;
					if (res.status == 1) {
						let arr = res.data;

						let szArr = []; //列表市值
						let ykArr = []; //列表盈亏
						let arr1 = []; //認繳的资金  买入本金+盈利(持仓冻结)
						// let arr2 = [];

						arr.forEach((item) => {
							szArr.push(Number(item.market_value) + Number(item.yingkui));
							ykArr.push(Number(item.yingkui));
							arr1.push(
								Number(item.buy_price) * Number(item.stock_num) +
								Number(item.yingkui)
							);
							// arr2.push(Number(item.buy_price) * Number(item.stock_num));

						});

						this.szAssets = szArr.reduce((a, b) => a + b, 0);
						this.ykAssets = ykArr.reduce((a, b) => a + b, 0);
						this.freezeAssets = arr1.reduce((a, b) => a + b, 0);

						// let total2 = arr2.reduce((a, b) => a + b, 0);
						// this.percent = ((this.ykAssets / (total2 || 1)) * 100).toFixed(2); //总盈亏比例

						const res1 = await this.$server.post("/user/getUserinfo", {
							type: "zar",
						});
						if (res.status == 1) {
							this.userInfo = res1.data;
						}

						// 总资产 可用+持仓资金
						this.totalAssets = Number(this.userInfo.zar) + this.freezeAssets;


					}
					this.$refs.loading.close(); //关闭加载
				});
			},

			async getConfig() {
				this.$refs.loading.open();
				const res = await this.$server.post("/common/config", {
					type: 'zar'
				});
				let val = {};
				res.data.forEach((vo) => {
					val[vo.name] = vo.value;
				});
				// this.kfUrl = val.kefu;
				this.$refs.loading.close();
				this.$openUrl(val.kefu); //重新获取
			},
			getEcharts() {
				let that = this;
				if (that.myChart !== null) {
					echarts.dispose(that.myChart);
				}

				let chartDom = document.getElementById("main");
				that.myChart = echarts.init(chartDom);
				let option;
				option = {
					// color: ["#C5585E", "#6970AF", "#F9E5E6", "#ECFAFA"], // 顺时针
					color: ["#6970AF", "#F9E5E6", "#ECFAFA"], // 顺时针
					tooltip: {
						trigger: "item",
					},
					// 頂部圖例
					legend: {
						top: "5%",
						left: "center",
						show: false,
					},
					series: [{
						name: "",
						type: "pie",
						// radius: ['40%', '70%'], //圆环
						radius: "100%",
						center: ["50%", "50%"],
						avoidLabelOverlap: false,
						label: {
							show: false,
							position: "center",
						},
						emphasis: {
							label: {
								show: true,
								fontSize: "40",
								fontWeight: "bold",
							},
						},
						labelLine: {
							show: false,
						},
						data: this.chartData,
					}, ],
				};

				option && that.myChart.setOption(option);
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.5rem 0;
		min-height: 100vh;
		background-color: #E6F1F3;

		.cot {
			.title {
				background: #027D7D;
				box-shadow: 0rem 0rem 0rem 0rem rgba(255, 255, 255, 0.5);
				padding: 0.12rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 0.14rem;
				color: #FFFFFF;
			}

			.money {
				background: #002f7c;
				padding: 0.12rem 0.12rem 0.2rem;

				.tops {
					.t {
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 0.13rem;
						color: #FFFFFF;
					}

					.num {
						padding: 0.05rem 0;
						font-family: PingFangSC, PingFang SC;
						font-weight: 600;
						font-size: 0.39rem;
						color: #FFFFFF;
					}

					.txt {
						span {
							padding-left: 0.1rem;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							font-size: 0.14rem;
						}
					}
				}

				.nums {
					background: #000000;
					border-radius: 0.09rem;
					padding: 0.1rem 0;

					.item {
						border-right: 0.01rem solid rgba(67, 137, 218, 0.5);
						flex: 1;
						padding: 0 0.12rem;

						&:last-child {
							border-right: none;
						}

						.t1 {
							font-family: PingFangSC, PingFang SC;
							font-weight: 600;
							font-size: 0.18rem;
							color: #FFFFFF;
						}

						.t2 {
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							font-size: 0.13rem;
							color: #4389DA;
							margin-bottom: 0.05rem;
						}
					}
				}
			}

			.btns {
				margin: 0.12rem;
				.btn {
					margin: 0.05rem 0;
					padding: 0 0.12rem;
					height: 0.59rem;
					background: #F4F4F4;
					border-radius: 0.09rem;
					border: 0.01rem solid #DADADA;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.18rem;
					color: #01655D;
					.icon {
						margin-right: 0.08rem;
					}
					.name{
						font-family: PingFangSC, PingFang SC;
						font-weight: 500;
						font-size: 0.15rem;
						color: #333333;
					}
				}
			}

			.ts {
				font-weight: bold;
				font-size: 0.16rem;
				color: #333333;
				padding: 0.2rem 0;
			}
		}
	}

	.tt {
		padding: 0.1rem;

		div {
			font-size: 0.16rem;
		}
	}

	.list {
		padding: 0 0.1rem;

		.list-item {
			background: #f8f8f8;
			border-radius: 0.08rem;
			border: 0.01rem solid #b8b8b8;
			padding: 0.15rem 0.1rem;
			margin-bottom: 0.1rem;

			.t {
				font-weight: 500;
				font-size: 0.14rem;
			}

			.t1 {
				font-size: 0.12rem;
				color: #949494;
				margin-top: 0.1rem;
			}

			.t2 {
				font-weight: 600;
				font-size: 0.16rem;
			}

			.red {
				color: #a91111;
			}

			.green {
				color: #6bb831;
			}
		}
	}

	#main {
		width: 0.66rem;
		height: 0.66rem;
		border-radius: 50%;
	}



	.top {
		padding: 0.15rem;

		.set {
			margin-left: 0.05rem;
		}

		.pad10 {
			padding: 0 0.1rem 0.1rem;
		}

		.user-info {
			.user {
				margin: 0 0.1rem 0 0;
			}

			.account {
				font-size: 0.12rem;
				color: #8f8f8f;
			}

			.rz-btn {
				.bt {
					background: #eceefe;
					border-radius: 0.04rem;
					padding: 0.05rem 0.1rem;
					font-weight: 500;
					color: #777779;
					margin-right: 0.05rem;
				}
			}
		}
	}
</style>