<template>
	<div class="page ">
		<top-back :title="$t('大宗交易')"></top-back>

		<div class="nav-box flex">
			<div class="nav-item" v-for="(item, index) in navList" :key="index"
				:class="{ active: currmentIndex == item.type }" @click="changeNav(item.type)">
				{{ item.name }}
			</div>
		</div>

		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<van-skeleton title :row="26" :loading="loading">
				<div class="cot">
					<div class="list" v-if="currmentIndex == 0">
						<div class="titles flex flex-b">
							<div class="flex-1">{{ $t('股票名稱') }}</div>
							<div class="flex-1 t-c">{{ $t('買入價') }}</div>
							<div class="flex-1 t-r">{{ $t('最小買入股數') }}</div>
						</div>
						<div class="list-item flex flex-b" v-for="(item, index) in chooseList" :key="index" @click="stockDetails(item)">
							<div class="flex-1">{{ item.name }}</div>
							<div class="flex-1 t-c" style="color: #8DFD99;">{{ $formatMoney(item.price) }}</div>
							<div class="flex-1 t-r">{{ $formatMoney(item.stock_num) || "-" }}</div>
							<!-- <div class="flex flex-b">
								<div class="flex">
									<div class="name">{{ item.name }}</div>
									<div class="code">{{ item.symbol }}</div>
								</div>
								<div class="st">買入</div>
							</div>
							<div class="bg">
								<div class="flex flex-b">
									<div class="tt">買入價</div>
									<div class="price red">{{ $formatMoney(item.price) }}</div>
								</div>
								<div class="flex flex-b">
									<div class="tt">最小買入股數</div>
									<div class="price">
										{{ $formatMoney(item.stock_num) || "-" }}
									</div>
								</div>
							</div> -->
						</div>
						<no-data v-if="!chooseList.length"></no-data>
					</div>
					<!-- 買入記錄 -->
					<div class="list" v-else>
						<div class="titles flex flex-b">
							<div class="flex-1">{{ $t('股票名稱') }}</div>
							<div class="flex-1 t-c">{{ $t('價格') }}</div>
							<div class="flex-1 t-c">{{ $t('買進張數') }}</div>
							<div class="flex-1 t-c">{{ $t('成交量') }}</div>
							<div class="flex-1 t-r">{{ $t('狀態') }}</div>
						</div>
						<div class="list-items flex flex-b" v-for="(item, index) in myList" :key="index">
							<div class="flex-1">{{ item.stock_name }}</div>
							<div class="flex-1 t-c" style="color: #8DFD99;">{{ $formatMoney(item.buy_price) }}</div>
							<div class="flex-1 t-c">{{ $formatMoney(item.zhang, 0) }}</div>
							<div class="flex-1 t-c">{{ $formatMoney(item.cj_num) }}</div>
							<div class="flex-1 t-r" style="color: #8DFD99;" :class="item.state != '审核中' ? 'sc' : ''">{{ $t(item.state) }}</div>
							<!-- <div class="flex flex-b">
								<div class="flex">
									<div class="name">{{ item.stock_name }}</div>
									<div class="code">{{ item.stock_code }}</div>
								</div>
								<div class="st" :class="item.state != '审核中' ? 'sc' : ''">{{ $t(item.state) }}
								</div>
							</div>
							<div class="inner flex flex-b">
								<div class="inner-item flex flex-b">
									<div class="t">價格</div>
									<div class="t1 red">{{ $formatMoney(item.buy_price) }}</div>
								</div>
								<div class="inner-item flex flex-b">
									<div class="t">買進張數</div>
									<div class="t1">{{ $formatMoney(item.zhang, 0) }}</div>
								</div>
								<div class="inner-item flex flex-b">
									<div class="t">成交量</div>
									<div class="t1">{{ $formatMoney(item.cj_num) }}</div>
								</div>
							</div> -->
						</div>
						<no-data v-if="!myList.length"></no-data>
					</div>
				</div>
			</van-skeleton>
		</van-pull-refresh>

		<van-popup v-model="show" round position="center" :style="{ width: '90%' }">
			<div class="pop">
				<div class="pop-title ">{{ stockObj.name + "/" + stockObj.code }}</div>
				<div class="pop-price flex flex-b">
					<div class="t1">{{ $t('買入價格') }}</div>
					<div class="t">
						{{ $t('最低買入') }}：<span>{{ $formatMoney(stockObj.price) || 0 }}</span>
					</div>
				</div>
				<div class="pad">
					<div class="ipt ">
						<div class="tt">{{ $t('買入張數') }}</div>
						<input class="flex-1" v-model="buyObj.handle" type="number" :placeholder="$t('請輸入買入張數')"
							@input="TypeInput($event)" />
						<div class="flex flex-b">
							<div class="flex">
								<div class="t1">{{ $t('帳戶可用資金') }}</div>
								<div class="t2">{{ $formatMoney(userInfo.zar) || 0 }}</div>
							</div>
							<div class="flex mt10">
								<div class="t1">{{ $t('申請額') }}</div>
								<div class="t2">{{ $formatMoney(countMoney) || 0 }}</div>
							</div>
						</div>
					</div>
					<!--        <div class="pop-num">-->
					<!--          <div>{{ $t("new").b47 }}</div>-->
					<!--          <input-->
					<!--            :placeholder="$t('new').t1"-->
					<!--            type="password"-->
					<!--            v-model="password"-->
					<!--          />-->
					<!--        </div>-->

					<div class="flex flex-b">
						<!-- <div class="b-btn bt" @click="show = false">取消</div> -->
						<div @click="buyFn" class="defbtn">{{ $t('買入') }}</div>
					</div>
				</div>
			</div>
			<!-- <div class="icon close" @click="show = false"></div> -->
		</van-popup>
		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "blockTrade",
		data() {
			return {
				loading: true,
				isLoading: false,
				currmentIndex: 0,
				chooseList: [
					// {
					// 	name: "名称",
					// 	code: "007",
					// 	price: "1000",
					// 	stock_num: 1000,
					// },
				 ],
				myList: [
					// {
					// 	stock_name: "stock_name",
					// 	stock_code: "stock_code",
					// 	buy_price: "1000",
					// 	zhang: "1000",
					// 	cj_num: "1000",
					// 	status: 1,
					// 	state: "审核中",
					// },
				 ],
				show: false,
				stockObj: {},
				buyObj: {
					handle: null,
				},
				password: "",
				userInfo: {},
				currentItem: {},
			};
		},
		computed: {
			navList() {
				return [{
						name: this.$t("股票列表"),
						type: 0,
					},
					{
						name: this.$t("購買記錄"),
						type: 1,
					},
				];
			},
			countMoney() {
				return this.stockObj.price * this.buyObj.handle * 1000;
			},
		},
		created() {
			this.initData();
			this.getNew();
		},
		mounted() {},
		methods: {
			// 下拉刷新
			onRefresh() {
				this.currmentIndex = 0;
				this.initData();
				this.getNew();
			},
			initData() {
				this.$server.post("/user/getUserinfo", {
					type: "zar"
				}).then((res) => {
					if (res.status == 1) {
						this.userInfo = res.data;
					}
				});
			},
			TypeInput(e) {
				// 只能输入数字的验证;
				const inputType = /[^\d]/g; //想限制什么类型在这里换换正则就可以了
				this.$nextTick(function() {
					this.buyObj.handle = e.target.value.replace(inputType, "");
				});
			},
			getNew() {
				this.$server
					.post("/trade/nbhllist", {
						type: "zar",
					})
					.then((res) => {
						this.isLoading = false;
						this.loading = false;
						this.$refs.loading.close(); //关闭加载
						if (res.status == 1) {
							this.chooseList = res.data;
						}
					});
			},
			getMine() {
				this.$server
					.post("/trade/ustockslist", {
						type: "zar",
					})
					.then((res) => {
						this.$refs.loading.close(); //关闭加载
						if (res.status == 1) {
							this.myList = res.data;
						}
					});
			},
			stockDetails(stock) {
				this.show = true;
				this.currentItem = stock;
				this.$server
					.post("/transaction/stocksdetails", {
						symbol: stock.code,
					})
					.then((res) => {
						this.stockObj = res.data;
					});
			},
			changeNav(index) {
				this.currmentIndex = index;
				this.$refs.loading.open(); //开启加载

				if (index) this.getMine();
				else this.getNew();
			},
			buyFn() {
				if (!this.buyObj.handle) {
					this.$toast(this.$t("請輸入買入張數"));
					return;
				}
				// if (!this.password) {
				//   this.$toast(this.$t("new").t);
				//   return;
				// }
				this.$refs.loading.open(); //开启加载

				this.$server
					.post("/transaction/buy_stocks", {
						symbol: this.stockObj.symbol,
						zhang: this.buyObj.handle,
						//password: this.password,
						buyzd: 1,
						ganggan: 1,
						type: 0,
						// is_qc: 2,
						id: this.currentItem.id, //大宗增加传递参数，列表id
					})
					.then((res) => {
						this.$refs.loading.close();

						this.show = false;
						if (res.msg) {
							this.$toast(this.$translateServerText(res.msg));
						}
					});
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 1rem 0 0.1rem;
		min-height: 100vh;
		position: relative;
	}
  ::v-deep .van-skeleton__row{
    background-color:transparent !important;
  }
  ::v-deep .van-skeleton__title{
    background-color:transparent !important;
  }

	.nav-box {
		position: fixed;
		top: 0.5rem;
		left: 50%;
		transform: translateX(-50%);
		width: 92%;
		height: 0.38rem;
		background: #232429;
		border-radius: 0.19rem;
		z-index: 999;

		.nav-item {
			flex: 1;
			font-family: PingFang SC;
			font-weight: 500;
			font-size: 0.13rem;
			color: #999999;
			text-align: center;
			position: relative;
			// &::after {
			// 	content: "";
			// 	width: 100%;
			// 	height: 0.02rem;
			// 	position: absolute;
			// 	bottom: 0;
			// 	left: 50%;
			// 	transform: translateX(-50%);
			// 	background: transparent;
			// }

			&.active {
				line-height: 0.31rem;
				background: #8DFD99;
				border-radius: 0.16rem;
				color: #010101;
				&::after {
					background: #c94d5b;
				}
			}
		}
	}

	.titles {
		padding: 0.1rem 0.1rem 0;
		div {
			font-family: PingFang SC;
			font-weight: 400;
			font-size: 0.13rem;
			color: #999999;
		}
	}

	.van-popup {
		background-color: transparent;
	}

	.close {
		margin: 0.1rem auto 0;
	}

	.pop {
		background: #111111;
		border-radius: 0.02rem;
		position: relative;

		.pad {
			padding: 0 0.12rem 0;
		}

		.btips {
			padding: 0.15rem 0;
			line-height: 0.24rem;

			.t2 {
				color: #a91111;
			}
		}

		.pop-title {
			padding: 0.16rem 0;
			background: linear-gradient(90deg, #98EF86, #C7F377);
			font-size: 0.16rem;
			text-align: center;
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 0.18rem;
			color: #000;
		}
		.pop-price {
			padding: 0.12rem;
			.t {
				font-size: 0.14rem;
				color: #999;
				span {
					color: #60bb74;
				}
			}

			.t1 {
				font-family: PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
				color: #FFFFFF;
			}
		}

		.ipt {
			.tt {
				padding: 0.1rem 0;
				font-size: 0.14rem;
				color: #fff;
			}
			input {
				line-height: 0.44rem;
				height: 0.44rem;
				background: #434446;
				border-radius: 0.04rem 0.04rem 0.04rem 0.04rem;
				height: 0.42rem;
				line-height: 0.42rem;
				padding: 0 0.1rem;
				margin: 0.1rem 0;
				width: 100%;
				color: #fff;
				&::placeholder {
					font-size: 0.14rem;
					color: #9a9fa5;
				}
			}
			.t1 {
				font-size: 0.12rem;
				color: #9a9fa5;
			}
			.t2 {
				margin-left: 0.1rem;
				font-size: 0.14rem;
				color: #dc183b;
			}

			.mt10 {
				margin-top: 0.05rem;
			}
		}
		.pop-num {
			margin-top: 0.15rem;
			input {
				margin: 0.05rem 0;
				width: 100%;
				background: #f8f8f8;
				border-radius: 0.06rem;
				border: 0.01rem solid #b8b8b8;
				height: 0.4rem;
				line-height: 0.4rem;
				padding: 0 0.1rem;

				&::placeholder {
					font-size: 0.12rem;
					color: #606060;
				}
			}
		}
		.txt {
			font-size: 0.12rem;
			color: #9a9fa5;
			span {
				font-size: 0.12rem;
				color: #c5585e;
			}
		}
		.defbtn {
			width: 100%;
		}
	}

	.cot {
		margin: 0.12rem;
		.title {
			padding: 0.12rem;
			div {
				font-weight: 500;
				font-size: 0.12rem;
				color: #666666;
			}
		}
		.list {
			background: #232429;
			border-radius: 0.19rem;
			.list-item {
				padding: 0.12rem;
				font-family: PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
				color: #FFFFFF;
				.name {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 0.14rem;
					color: #FFFFFF;
				}

				.code {
					margin-left: 0.1rem;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 0.12rem;
					color: #FFFFFF;
				}

				.mtb10 {
					margin: 0.1rem 0;
				}

				.bg {
					margin-top: 0.16rem;
					line-height: 0.3rem;
				}

				.tt {
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 0.14rem;
					color: #B0B0B2;
				}

				.price {
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 0.14rem;
					color: #FFFFFF;
				}

				.red {
					color: #cf2829;
				}

				.st {
					background: #DC183B;
					border-radius: 0.02rem 0.02rem 0.02rem 0.02rem;
					padding: 0.05rem 0.15rem;
					font-size: 0.14rem;
					color: #ffffff;
				}
				.t {
					font-weight: bold;
				}
			}
		}

		.list-items {
			padding: 0.12rem;
			font-family: PingFang SC;
			font-weight: 400;
			font-size: 0.13rem;
			color: #FFFFFF;
			.name {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 0.14rem;
				color: #FFFFFF;
			}

			.code {
				margin-left: 0.1rem;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 0.12rem;
				color: #FFFFFF;
			}


			.st {
				background: #DC183B;
				border-radius: 0.02rem 0.02rem 0.02rem 0.02rem;
				padding: 0.05rem 0.15rem;
				font-size: 0.14rem;
				color: #ffffff;

				&.sc {
					background: #d5d5d5;
					color: #000000;
				}
			}

			.inner {
				flex-wrap: wrap;
				margin-top: 0.16rem;

				.inner-item {
					width: 100%;
					line-height: 0.3rem;

					.t {
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 0.14rem;
						color: #B0B0B2;
					}

					.t1 {
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 0.14rem;
						color: #FFFFFF;
					}

					.red {
						color: #cf2829;
					}
				}
			}
		}
	}
</style>
