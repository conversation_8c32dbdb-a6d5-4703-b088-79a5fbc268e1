<template>
	<div class="newsDetail">
		<div class="headf">
			<top :title="$t(text)"></top>
		</div>

		<div class="hjkl"></div>
		<div class="gbhnk" v-if="textd">
			<h6>
				{{ textd.title.split('u3000').join('') }}
			</h6>
			
			<p class="tyhk" v-html="textd.content.split('u3000').join('')"></p>
			<p class="tyhk" style="text-align: right;">
				{{ textd.showTime }}
			</p>
		</div>

		<div class="yghl"></div>
	</div>
</template>
<script type="text/javascript">
	import Vue from "vue";
	import qs from "qs";
	import axios from "axios";
	import {
		Toast
	} from "vant";
	Vue.use(Toast);
	import {
		Popup
	} from "vant";
	Vue.use(Popup);

	import top from "../bar/toper.vue";

	export default {
		name: "newsDetail",
		data() {
			return {
				text: this.$t('home').txt6,
				id: this.$route.query.id,
				textd: null,
			};
		},
		components: {
			top,
		},
		methods: {
			getDate() {
				var datas = qs.stringify({
					id: this.id,
				});
				this.$server.post("/agu/sysMsgDetails", datas).then((str) => {
					this.textd = JSON.parse(str.data.data).data;
				});
			},
		},
		destroyed() {},
		mounted() {
			this.textd = JSON.parse(window.localStorage.getItem("newsDetail"));
		},
	};
</script>

<style lang="less">
	body {
		max-width: 100%;
		overflow-x: hidden;
	}
	.newsDetail{
		background: #0F161C;
		min-height: 100vh;
		.headf {
			position: fixed;
			top: 0;
			left: 0;
			width: 100%;
			
		}
		
		.yghl {
			width: 3.47rem;
			margin: 0 auto;
			font-size: 0.14rem;
			line-height: 0.22rem;
		}
		
		.gbhnk {
			width: 3.47rem;
			margin: 0 auto;
			padding-bottom: 0.12rem;
		
			h6 {
				font-size: 0.23rem;
				line-height: 0.33rem;
				color: #fff;
				font-weight: 600;
				margin-top: 0.07rem;
			}
		
			.alkm {
				color: #999;
				font-size: 0.12rem;
				margin: 0.15rem auto;
			}
		
			.tyhk {
				color: #fff;
				font-size: 0.14rem;
				margin-top: 0.1rem;
				line-height: 0.22rem;
		
				span {
					display: inline-block;
					background: RGBA(26, 49, 92, 1);
					font-size: 0.11rem;
					padding: 0.02rem 0.03rem;
					border-radius: 0.02rem;
					color: #333;
				}
				img{max-width: 100%;height: auto;}
        .figure_image_sizer{
          display: none !important;
        }
				p {
					a {
						width: 3.47rem !important;
		
						img {
							width: 3.47rem !important;
						}
					}
				}
			}
		}
		
		
		
		.hjkl {
			height: 0.5rem;
		}
	}
	
</style>