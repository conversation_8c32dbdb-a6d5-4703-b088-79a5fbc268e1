<template>
	<div id="app">
		<keep-alive>
			<router-view v-if="$route.meta.keepAlive" />
		</keep-alive>
		<router-view v-if="!$route.meta.keepAlive" />
	</div>
</template>
<script>
	import "./assets/js/com";

	export default {
		data() {
			return {};
		},
		beforeCreate() {
			var _this = this;

			var first = null;

			document.addEventListener("plusready", function() {
				var webview = plus.webview.currentWebview();
				var ver = plus.runtime.version;
				// window.localStorage.setItem("pdver",ver)
				webview.setStyle({
					popGesture: "none"
				});
				plus.key.addEventListener("backbutton", function() {
					webview.canBack(function(e) {
						if (e.canBack) {
							webview.back();
						} else {
							if (!first) {
								first = new Date().getTime();
								setTimeout(function() {
									first = null;
								}, 1000);
							} else {
								if (new Date().getTime() - first < 1000) {
									plus.runtime.quit();
								}
							}
						}
					});
				});
				plus.navigator.setStatusBarBackground("#FFF");
				plus.navigator.setStatusBarStyle("dark");
			});
		},
		destroyed() {},
		methods: {},
	};
</script>
<style lang="less">
	@import "./assets/css/style.less";
	@import "./assets/css/animate.css";
	body {
		background: #18191B;
	}

	.t-c {
		text-align: center;
	}

	.t-r {
		text-align: right;
	}

	.nflex {
		display: flex;
		align-items: center;
	}

	.nflex-lc {
		justify-content: center;
	}

	.nflex-lb {
		justify-content: space-between;
	}

	.nflex-la {
		justify-content: space-around;
	}

	.nflex-le {
		justify-content: flex-end;
	}

	.nflex-ln {
		align-items: flex-end;
	}

	.nflex-lf {
		align-items: flex-start;
	}

	.ntext-eo {
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.ntext-to {
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.flex {
		display: flex;
		align-items: center;

		&.flex-a {
			justify-content: space-around;
		}

		&.flex-b {
			justify-content: space-between;
		}

		&.flex-c {
			justify-content: center;
		}

		&.flex-e {
			justify-content: flex-end;
		}

		&.flex-s {
			justify-content: flex-start;
		}
	}

	.flex-only {
		display: flex;
	}

	.flex-1 {
		flex: 1;
	}

	.flex-2 {
		flex: 2;
	}

	.flex-3 {
		flex: 3;
	}

	.flex-column-item {
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.flex-wrap {
		flex-wrap: wrap;
	}

	.up {
		color: #FF5683;
	}

	.down {
		color: #8EFE99;
	}
	.red {
		color:#FF5683 !important;
	}
	
	.green {
		color: #8EFE99 !important;
	}
	.red-bg {
		background-color: #FF5683 !important;
	}
	
	.green-bg {
		background-color: #8EFE99 !important;
	}
	.defbtn{
		margin: 0.2rem 0;
		height: 0.5rem;
		background: linear-gradient(90deg, #98EF86, #C7F377);
		border-radius: 0.25rem;
		line-height: 0.5rem;
		text-align: center;
		font-family: PingFang SC;
		font-weight: 500;
		font-size: 0.16rem;
		color: #000000;
	}
	.icon {
		&.start {
			width: .18rem;
			height: .18rem;
			background: url(assets/v2/start.png) no-repeat center/100%;
		}

		&.sfz {
			width: .18rem;
			height: .18rem;
			background: url(assets/v5/sfz.png) no-repeat center/100%;
		}

		&.mm {
			width: .18rem;
			height: .18rem;
			background: url(assets/v5/mm.png) no-repeat center/100%;
		}

		&.yqm {
			width: .24rem;
			height: .24rem;
			background: url(assets/v2/yqm.png) no-repeat center/100%;
		}
		&.set {
			width: 0.2rem;
			height: 0.2rem;
			background: url(assets/v2/set.png) no-repeat center/100%;
		}

		&.redIcon {
			width: 0.32rem;
			height: 0.32rem;
			background: url(assets/v2/red.png) no-repeat center/100%;
		}

		&.greenIcon {
			width: 0.32rem;
			height: 0.32rem;
			background: url(assets/v2/green.png) no-repeat center/100%;
		}

		&.wsc {
			width: 48px;
			height: 48px;
			background: url(assets/v2/wsc.png) no-repeat center/100%;
		}

		&.ysc {
			width: 48px;
			height: 48px;
			background: url(assets/v2/ysc.png) no-repeat center/100%;
		}

		&.redUp {
			width: 32px;
			height: 32px;
			background: url(assets/v2/redUp.png) no-repeat center/100%;
		}

		&.wdzh {
			width: 0.3rem;
			height: 0.3rem;
			background: url(assets/v2/wdzh.png) no-repeat center/100%;
		}
    &.wdcc {
      width: 0.3rem;
      height: 0.3rem;
      background: url(assets/v2/wdcc.png) no-repeat center/100%;
    }
		&.cw {
			width: 0.26rem;
			height: 0.26rem;
			background: url(assets/v2/cw.png) no-repeat center/100%;
		}
		&.sdxd {
			width: 0.26rem;
			height: 0.26rem;
			background: url(assets/v2/sdxd.png) no-repeat center/100%;
		}
		&.zhd {
			width: 0.26rem;
			height: 0.26rem;
			background: url(assets/v2/zdh.png) no-repeat center/100%;
		}
		&.hlgp {
			width: 0.26rem;
			height: 0.26rem;
			background: url(assets/v2/hlgp.png) no-repeat center/100%;
		}
		&.lxkf {
			width: 0.26rem;
			height: 0.26rem;
			background: url(assets/v2/lxkf.png) no-repeat center/100%;
		}
		&.zjkgl {
			width: 0.3rem;
			height: 0.3rem;
			background: url(assets/v2/zjkgl.png) no-repeat center/100%;
		}

		&.cz {
			width: 0.3rem;
			height: 0.3rem;
			background: url(assets/v2/cz.png) no-repeat center/100%;
		}

		&.tx {
			width: 0.3rem;
			height: 0.3rem;
			background: url(assets/v2/tx.png) no-repeat center/100%;
		}

		&.zjls {
			width: 0.3rem;
			height: 0.3rem;
			background: url(assets/v2/zjls.png) no-repeat center/100%;
		}

		&.smrz {
			width: 0.3rem;
			height: 0.3rem;
			background: url(assets/images/mine/smrz.png) no-repeat center/100%;
		}

		&.dlmm {
			width: 0.3rem;
			height: 0.3rem;
			background: url(assets/images/mine/dlmm.png) no-repeat center/100%;
		}

		&.zjmm {
			width: 0.3rem;
			height: 0.3rem;
			background: url(assets/images/mine/zjmm.png) no-repeat center/100%;
		}

		&.zxkf {
			width: 0.3rem;
			height: 0.3rem;
			background: url(assets/images/mine/zxkf.png) no-repeat center/100%;
		}
		&.back {
			width: 0.1rem;
			height: 0.18rem;
			background: url(assets/v2/back.png) no-repeat center/100%;
		}
		&.tx {
			width: 0.38rem;
			height: 0.38rem;
			background: url(assets/v2/tx.png) no-repeat center/100%;
		}
		&.down03 {
			width: 0.12rem;
			height: 0.08rem;
			background: url(assets/v2/down03.png) no-repeat center/100%;
		}
		&.up03 {
			width: 0.12rem;
			height: 0.08rem;
			background: url(assets/v2/up03.png) no-repeat center/100%;
		}
		&.arrow {
			width: 0.12rem;
			height: 0.12rem;
			background: url(assets/v5/arrow.png) no-repeat center/100%;
		}
	}

  .van-skeleton__row{
    background-color: transparent !important;
  }
  .van-skeleton__title{
    background-color: transparent !important;
  }
  div,span{
	  font-family: PingFang SC, PingFang SC !important;
  }
</style>