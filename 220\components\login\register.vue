<template>
	<div class="register">
		<div class="header" @click="clickNext('/login/logon2')">
			<img src="../../assets/v2/close.png" />
		</div>
<!--		<div class="flex-column-item">-->
<!--			<img class="img" src="../../assets/v2/loginIcon.png" />-->
<!--		</div>-->
		<div class="info" :class="inputShow?'margin-left-24':''">
			<div class="info-title">
				<div>{{$t('login').txt15}}</div>
				<div class="input-tips flex" @click="clickNext('/login/login')">
					<div class="t01">{{$t('other').txt1}}</div>
					<div class="t02">{{$t('other').txt2}}
						<img src="../../assets/v2/ljIcon.png" style="width: 0.11rem;height: 0.11rem;" alt="" />
					</div>
				</div>
				<!-- <span>{{$t('login').txt16}}</span> -->
			</div>
		</div>

		<div class="input" :class="inputShow?'margin-left-24':''">
			<div class="input-item">
				<div class="input-name flex">{{$t('register').txt2}}</div>
				<div class="input-box">
					<!-- <img src="../../assets/skin/login/ico2.png" /> -->
					<input :placeholder="$t('register').txt3" placeholder-style="color: #CCCCCC;" maxlength="11"
						v-model="form.account" />
				</div>
				<div class="input-des" v-if="false">{{$t('register').txt4}}</div>
			</div>
			<div class="input-item">
				<div class="input-name flex">{{$t('register').txt5}}</div>
				<div class="input-box" v-if="showPass01">
					<!-- <img src="../../assets/skin/login/ico3.png" /> -->
					<input class="sec" v-model="form.pwd" type="password" :placeholder="$t('register').txt6" placeholder-style="color: #CCCCCC;" />
					<div class="icon by" @click="showPass01=!showPass01"></div>
				</div>
				<div class="input-box" v-else>
					<!-- <img src="../../assets/skin/login/ico3.png" /> -->
					<input class="sec" v-model="form.pwd" type="text" :placeholder="$t('register').txt6" placeholder-style="color: #CCCCCC;" />
					<div class="icon zy" @click="showPass01=!showPass01"></div>
				</div>
				<div class="input-des" v-if="false">{{$t('register').txt7}}</div>
			</div>
			<div class="input-item">
				<div class="input-name flex">{{$t('register').txt8}}</div>
				<div class="input-box" v-if="showPass02">
					<!-- <img src="../../assets/skin/login/ico3.png" /> -->
					<input class="sec" v-model="form.pwd1" type="password" :placeholder="$t('register').txt9"
						placeholder-style="color: #CCCCCC;" />
					<div class="icon by" @click="showPass02=!showPass02"></div>
				</div>
				<div class="input-box" v-else>
					<!-- <img src="../../assets/skin/login/ico3.png" /> -->
					<input class="sec" v-model="form.pwd1" type="text" :placeholder="$t('register').txt9"
						placeholder-style="color: #CCCCCC;" />
					<div class="icon zy" @click="showPass02=!showPass02"></div>
				</div>
				
				<div class="input-des" v-if="false">{{$t('register').txt10}}</div>
			</div>
			<div class="input-item" v-if="flagInvite">
				<div class="input-name">{{$t('register').txt11}}</div>
				<div class="input-box">
					<!-- <img src="../../assets/skin/login/ico4.png" /> -->
					<input class="sec" v-model="form.inviter" type="text" :placeholder="$t('register').txt12"
						placeholder-style="color: #CCCCCC;" />
				</div>
				<div class="input-des" v-if="false">{{$t('register').txt13}}</div>
			</div>
			
		</div>
		<div class="Btn text-center" :class="{'show':btnShow}">
			<div class="btn-big" @click="registerSubmit">{{$t('register').txt14}}</div>
			<div class="kefu" @click="clickKf()">{{$t('login').txt1}}</div>
		</div>

	</div>
</template>

<script>
	import Vue from "vue";
	import {
		Toast
	} from "vant";
	Vue.use(Toast);
	export default {
		data() {
			return {
				flagInvite: true,
				reAccount: false,
				rePassword: false,
				agree: false,
				inputShow: false,
				checkboxShow: false,
				buttonShow: false,
				btnShow: false,
				form: {
					account: "",
					inviter: "",
					pwd: "",
					pwd1: "",
				},
				look1: false,
				look2: false,
				showPass01:'false',
				showPass02:'false',
			};
		},
		mounted() {
			let _this = this
			setTimeout(function() {
				_this.btnShow = true
				setTimeout(function() {
					_this.inputShow = true
					_this.checkboxShow = true
					setTimeout(function() {
						_this.buttonShow = true
						setTimeout(function() {
							_this.btnShow = true
						}, 100)
					}, 100)
				}, 100)
			}, 800)

			let paraStr = location.href;
			let para = paraStr.split('=');
			this.form.inviter = para[1];
			if (para[1]) {
				this.flagInvite = false;
			}
		},
		methods: {
			registerSubmit() {
				let _this = this;
				if (!this.form.account) {
					Toast(this.$t('register').tip1);
					return;
				}
				if (/[A-Z]/.test(this.form.account)) {
					Toast(this.$t('register').tip5);
					return;
				}
				if (!this.form.pwd) {
					Toast(this.$t('register').tip2);
					return;
				}
				if (!this.form.pwd1) {
					Toast(this.$t('register').tip2);
					return;
				}
				if (this.form.pwd1 !== this.form.pwd) {
					Toast(this.$t('register').tip3);
					return;
				}
				if (!this.form.inviter) {
					Toast(this.$t('register').tip4);
					return;
				}
				/* if (!this.agree) {
					Toast(this.$t('login').tip4);
					return;
				} */

				this.$server.post("/user/register", {
					...this.form
				}).then((res) => {
					if (res.data.status == 1) {
						Toast(this.$t(res.data.msg));
						setTimeout(() => {
							_this.clickNext('/login/login?account=' + this.form.account)
						}, 500);
					}
				});
			},
			clickKf() {
				this.$server.post("/common/config", {type:'all'}).then(res => {
					if (parseInt(res.status) === 200) {
						let list = res.data.data;
						let listLength = list.length;
						let a;
						for (a = 0; a < listLength; a++) {
							if (list[a].name === 'kefu') {
								this.openInBrowser(list[a].value)
							}
						}
					}
				});
			},
		},
	};
</script>

<style scoped lang="less">
	.register {
		//background: #000000;
		overflow-x: hidden;
		padding-bottom: .1rem;
		background: rgba(3, 15, 29, 1);
		min-height: 100vh;

		.header {
			height: .7rem;
			display: flex;
			align-items: center;
			padding-left: .15rem;

			img {
				width: .25rem;
			}
		}
		.img {
			width: 1.86rem;
			margin-bottom: .2rem;
		}
		.info {
			width: 100%;
			margin-left: -3rem;
			opacity: 0;
			-webkit-transition-duration: 1s;
			-moz-transition-duration: 1s;
			-o-transition-duration: 1s;

			.info-title {
				font-weight: 600;
				font-size: 0.17rem;
				color: #FFFFFF;

				span {
					font-weight: 400;
					font-size: .12rem;
					color: #BEBEBE;
				}
				
			}
			.t02{
				margin-left: 0.05rem;
				color: #5ED5A8;
			}
			.input-tips{
				font-weight: 400;
				font-size: 0.11rem;
				color: #FFFFFF;
			}
		}

		.input {
			padding: .15rem 0;
			width: 85%;
			font-size: .13rem;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			color: #212121;
			margin-left: -3rem;
			opacity: 0;
			-webkit-transition-duration: 1s;
			-moz-transition-duration: 1s;
			-o-transition-duration: 1s;

			.input-item {
				margin-bottom: .1rem;
				width: 100%;

				.input-name {
					width: 100%;
					height: .3rem;
					font-weight: 400;
					font-size: 0.12rem;
					color: #FFFFFF;
				}

				.input-box {
					//width: 100%;
					height: .46rem;
					display: flex;
					align-items: center;
					background: rgba(27, 35, 42, 1);
					border-radius: 0.1rem;
					padding: 0 .1rem;

					input {
						width: 70%;
						font-size: .16rem;
						color: #fff;
						background: none;
					}

					img {
						width: .14rem;
						margin: 0 .05rem 0 0;
					}

					.sec {
						width: 100%;
					}
				}

				.input-des {
					width: 100%;
					font-size: .14rem;
					color: #7A7C84;
					margin-top: .04rem;
				}
			}
			
		}

		.Btn {
			width: 100%;
			font-weight: 500;
			font-size: 0.14rem;
			color: #171D22;
			margin-left: -3rem;
			opacity: 0;
			-webkit-transition-duration: 1s;
			-moz-transition-duration: 1s;
			-o-transition-duration: 1s;

			&.show {
				margin-left: 0;
				opacity: 1;
			}

			.btn-big {
				margin: .08rem .26rem;
				background: #5ED5A8;
				border-radius: 0.1rem;
				font-weight: 500;
				font-size: 0.14rem;
				color: #171D22;
			}

			.kefu {
				font-weight: 500;
				font-size: .14rem;
				color: #fff;
			}
		}

		.margin-left-0 {
			margin-left: 0;
			opacity: 1;
		}

		.margin-left-24 {
			margin-left: .26rem;
			opacity: 1;
		}

		.margin-left-48 {
			margin-left: .24rem;
			opacity: 1;
		}
	}


	::v-deep .uni-input-input {
		font-size: .16rem;
		font-family: PingFang SC;
		font-weight: 500;
		color: #030319;
	}
</style>