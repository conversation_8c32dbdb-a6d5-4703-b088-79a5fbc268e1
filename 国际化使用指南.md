# 南非语言国际化实现指南

## 概述
本项目已成功实现南非地区的多语言国际化支持，包括：
- 英语 (English) - en
- 南非荷兰语 (Afrikaans) - af (默认语言)
- 祖鲁语 (Zulu) - zu
- 科萨语 (Xhosa) - xh

## 实现的功能

### 1. 语言切换功能
- 默认语言：南非荷兰语 (af)
- 支持本地持久化存储语言设置
- 在个人中心页面可以设置语言
- 语言切换后自动重新加载应用

### 2. 服务端返回文字国际化
- 支持服务端返回的中文和日文内容的国际化展示
- 自动翻译服务端错误信息和状态信息

## 使用方法

### 1. 前端文字翻译
```javascript
// 在Vue组件中使用
this.$t('查询成功')  // 会根据当前语言返回对应翻译

// 在模板中使用
<template>
  <div>{{ $t('登入成功') }}</div>
</template>
```

### 2. 服务端返回文字翻译
```javascript
// 处理服务端返回的中文/日文文字
this.$translateServerText(serverResponse.message)

// 示例：
const serverMessage = '查询成功';
const translatedMessage = this.$translateServerText(serverMessage);
// 根据当前语言返回对应翻译
```

### 3. 语言切换
```javascript
// 获取当前语言
const currentLang = this.$getCurrentLang();

// 切换语言
this.$setLang('af'); // 切换到南非荷兰语

// 获取语言列表
const langList = this.$getLangList();
```

### 4. 在API响应中使用
```javascript
// 在API响应处理中自动翻译服务端消息
axios.post('/api/login', data)
  .then(response => {
    if (response.data.message) {
      // 自动翻译服务端返回的消息
      const translatedMessage = this.$translateServerText(response.data.message);
      this.$toast(translatedMessage);
    }
  })
  .catch(error => {
    if (error.response && error.response.data.message) {
      const translatedError = this.$translateServerText(error.response.data.message);
      this.$toast(translatedError);
    }
  });
```

## 文件结构

### 语言包文件
- `src/assets/lang/af.js` - 南非荷兰语翻译
- `src/assets/lang/zu.js` - 祖鲁语翻译
- `src/assets/lang/xh.js` - 科萨语翻译
- `src/assets/lang/en.js` - 英语翻译（已更新）

### 核心文件
- `src/assets/local.js` - 国际化核心逻辑
- `src/components/information/changeLang.vue` - 语言切换组件

## 添加新翻译

### 1. 添加前端翻译
在对应的语言包文件中添加新的键值对：
```javascript
// src/assets/lang/af.js
module.exports = {
  '新的文字': 'Nuwe teks',
  // ...其他翻译
}
```

### 2. 添加服务端返回文字翻译
在语言包文件的服务端翻译部分添加：
```javascript
// 服务端返回文字翻译
'服务端返回的中文': 'Translated text in target language',
```

## 注意事项

1. **默认语言**：系统默认使用南非荷兰语 (af)
2. **后备机制**：如果当前语言没有对应翻译，会尝试使用英语作为后备
3. **持久化**：语言设置会保存在 localStorage 中
4. **重新加载**：切换语言后会自动重新加载页面以应用新语言
5. **服务端文字**：使用 `$translateServerText()` 方法处理服务端返回的文字

## 测试建议

1. 测试语言切换功能
2. 测试服务端返回文字的翻译
3. 测试默认语言设置
4. 测试语言持久化存储
5. 测试后备翻译机制

## 扩展性

如需添加更多语言：
1. 在 `src/assets/lang/` 目录下创建新的语言包文件
2. 在 `src/assets/local.js` 中的 `langList` 和 `getLangPack` 函数中添加新语言
3. 导入新的语言包文件

这样就完成了南非地区多语言国际化的实现，支持本地化存储和服务端返回文字的自动翻译。
