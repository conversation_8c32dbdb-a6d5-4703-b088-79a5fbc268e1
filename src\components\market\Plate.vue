<template>
	<div>
		<top-back title="鉅額交易"></top-back>
		<van-pull-refresh style="min-height: 100vh;" v-model="isLoading" loosing-text="釋放即可刷新" loading-text="載入中..."
			@refresh="onRefresh">
			<div class="nav-box flex flex-b">
				<div class="nav-item" :class="{ active: active === 0 }" @click="selTab(0)">
					股票列表
					<span></span>
				</div>
				<div class="nav-item" :class="{ active: active === 1 }" @click="selTab(1)">
					購買記錄
					<span></span>
				</div>
			</div>
			<div v-if="active == 0" style="margin-top: 1rem;">
				<div class="lists" v-show="stockList.length">
					<div class="titles flex flex-b">
						<div class="flex-1">股票名稱</div>
						<div class="flex-1 t-c">配對價</div>
						<div class="flex-1 t-r">類別</div>
					</div>
					<div class="lists-item flex flex-b" v-for="(item, index) in stockList" :key="index" @click="openBuy(item)">
						<div class="flex-1">{{ item.name }}</div>
						<div class="flex-1 t-c" style="color: #8DFD99;">{{ $formatMoney(item.price) }}</div>
						<div class="flex-1 t-r">現沖</div>
						<!-- <div class="lists-head flex flex-b">
							<div class="lists-left">
								<div class="lists-name">{{ item.name }}</div>
								<div class="lists-code">{{ item.symbol }}</div>
							</div>
							<div class="lists-right">立即買入</div>
						</div>
						<div class="lists-row">
							<div class="lists-col flex flex-b">
								<span class="lists-key">類別</span>
								<span class="lists-val">現沖</span>
							</div>
							<div class="lists-col flex flex-b">
								<span class="lists-key">配對價</span>
								<span class="lists-val sec">{{ $formatMoney(item.price) }}</span>
							</div>
						</div> -->
					</div>
				</div>
				<no-data v-show="!stockList.length" style="margin: 0 0.12rem;"></no-data>
			</div>

			<div v-else style="margin-top: 1rem;">
				<div class="lists" v-show="myList.length">
					<div class="titles flex flex-b">
						<div class="flex-1">股票名稱</div>
						<div class="flex-1 t-c">成交/張數</div>
						<div class="flex-1 t-c">均價</div>
						<div class="flex-1 t-r">狀態</div>
					</div>
					<div class="lists-item flex flex-b" v-for="(item, index) in myList" :key="index">
						<div class="flex-1">{{ item.stock_name }}</div>
						<div class="flex-1 t-c">{{ item.zhang }}</div>
						<div class="flex-1 t-c" style="color: #8DFD99;">{{ $formatMoney(item.buy_price) }}</div>
						<div class="flex-1 t-r" style="color: #8DFD99;">{{ item.state=='待审核'?'處理中':item.state=='已通过'?'已完成':$t(item.state) }}</div>
						<!-- <div class="lists-head nflex nflex-lb">
							<div class="lists-left">
								<div class="lists-name">{{ item.stock_name }}</div>
								<div class="lists-code">{{ item.stock_code }}</div>
							</div>
							<div class="lists-right">現股</div>
						</div>
						<div class="lists-row">
							<div class="lists-col flex flex-b">
								<span class="lists-key">狀態</span>
								<span
									class="lists-val">{{ item.state=='待审核'?'處理中':item.state=='已通过'?'已完成':$t(item.state) }}</span>
							</div>
							<div class="lists-col flex flex-b">
								<span class="lists-key">成交/張數</span>
								<span class="lists-val">{{ item.zhang }}</span>
							</div>
							<div class="lists-col flex flex-b">
								<span class="lists-key">均價</span>
								<span class="lists-val sec">{{ $formatMoney(item.buy_price) }}</span>
							</div>
						</div> -->
					</div>
				</div>
				<no-data v-show="!stockList.length" style="margin: 0 0.12rem;"></no-data>
			</div>
		</van-pull-refresh>

		<van-popup v-model="popupShow" closeable close-icon="close" position="center" :style="{ width: '90%' }" round>
			<div class="vpopup-body">
				<div class="vpopup-head">{{ current.name }}</div>
				<div class="vpopup-input">
					<span>申請額：{{ countMoney }}</span>
				</div>
				<div class="vpopup-input">
					<span>買入價格：</span>
					<van-field v-model="price" :disabled="true" type="number" :border="false" placeholder="請輸入金額"
						@input="priceInput" />
				</div>
				<div class="vpopup-input">
					<span>買入張數：</span>
					<van-field v-model="number" type="number" :border="false" placeholder="請輸入張數"
						@input="numberInput" />
				</div>
				<div class="defbtn" style="margin:0.12rem;" @click="submit">提交</div>
			</div>
		</van-popup>
	</div>
</template>

<script>
	export default {
		name: 'Plate',
		data() {
			return {
				active: 0,
				stockList: [
					// {
					// 	name: "名称",
					// 	code: "100000",
					// 	price: 1000,
					// },
					// {
					// 	name: "名称",
					// 	code: "100000",
					// 	price: 1000,
					// }
				],
				myList: [
					// {
					// 	stock_name: "名称",
					// 	stock_code: "100000",
					// 	buy_price: 1000,
					// 	zhang: 100,
					// }
				],
				current: {},
				isLoading: false,
				popupShow: false,
				price: null,
				number: null
			}
		},
		computed: {
			countMoney() {
				return this.price * this.number * 1000
				// return this.price * this.number * 1
			}
		},
		created() {
			this.getStockList()
		},
		methods: {
			selTab(idx) {
				this.active = idx
				if (idx) this.getMyList()
				else this.getStockList()
			},
			getStockList() {
				this.$server.post('/trade/nbhllist', {
					dz_type: 0,
					type: 'twd'
				}).then(res => {
					if (res && res.status == 1) {
						this.stockList = res.data
					}
				})
			},
			getMyList() {
				this.$server.post('/trade/ustockslist', {
					dz_type: 0,
					type: 'twd'
				}).then(res => {
					if (res && res.status == 1) {
						this.myList = res.data
					}
				})
			},
			onRefresh() {
				if (this.active) this.getMyList()
				else this.getStockList()
				setTimeout(() => {
					this.isLoading = false
				}, 1500)
			},
			openBuy(item) {
				this.current = item
				this.price = this.$formatMoney(item.price)
				this.popupShow = true
			},
			priceInput(e) {
				if (Number(this.price) <= 0 && this.price) {
					this.$nextTick(() => {
						this.price = 1
					})
				}
			},
			numberInput(e) {
				if (Number(this.number) <= 0 && this.number) {
					this.$nextTick(() => {
						this.number = 1
					})
				}
			},
			submit() {
				if (!Number(this.price)) return this.$vApi.Toast('請輸入金額')
				if (!Number(this.number)) return this.$vApi.Toast('請輸入張數')
				this.$server.post('/trade/buy_stock', {
					symbol: this.current.symbol,
					zhang: this.number,
					type: 'twd',
					is_qc: 2,
					id: this.current.id,
					password: this.current.password,
					dz_type: 0
				}).then(res => {
					if (res && res.status == 1) {
						this.$vApi.Toast.success(this.$t(res.msg))
						this.popupShow = false
						this.price = null
						this.number = null
						this.current = {}
						setTimeout(() => {
							this.$router.go(-1)
						}, 1000)
					} else {
						if (res.msg.indexOf('当前股票大宗交易最少买入') > -1) {
							this.$vApi.Toast.fail(res.msg.replace('当前股票大宗交易最少买入', this.$t('当前股票大宗交易最少买入')).replace(
								'股', this.$t('股')))
							return false
						}
						this.$vApi.Toast.fail(this.$t(res.msg))
					}
				})
			}
		}
	}
</script>

<style lang="less" scoped>
	.nav-box {
		width: 92%;
		position: fixed;
		top: 0.5rem;
		left: 50%;
		transform: translateX(-50%);
		z-index: 999;
		height: 0.38rem;
		background: #232429;
		border-radius: 0.19rem;
		padding: 0.01rem;
		.nav-item {
			flex: 1;
			font-family: PingFang SC;
			font-weight: 500;
			font-size: 0.13rem;
			color: #999999;
			line-height: 0.36rem;
			text-align: center;
			position: relative;

			&.active {
				height: 0.36rem;
				background: #8DFD99;
				border-radius: 0.16rem;
				color: #010101;
				line-height: 0.36rem;
				position: relative;
				// &::after {
				// 	position: absolute;
				// 	content: '';
				// 	bottom: 0;
				// 	left: 50%;
				// 	transform: translateX(-50%);
				// 	width: 50%;
				// 	height: 0.02rem;
				// 	background-color: #E5C79F;
				// }
			}
		}
	}
	.titles{
		margin-bottom: 0.15rem;
		font-family: PingFang SC;
		font-weight: 400;
		font-size: 0.13rem;
		color: #999999;
	}
	.lists {
		margin: 0.12rem;
		padding: 0.12rem;
		background: #232429;
		border-radius: 0.19rem;
		.lists-item {
			width: 100%;
			padding: 0.08rem 0;
			font-family: PingFang SC;
			font-weight: 400;
			font-size: 0.13rem;
			color: #FFFFFF;
			.lists-head {
				padding-bottom: 0.12rem;
				border-bottom:0.01rem solid #F9F9F9;
				.lists-left {
					.lists-name {
						font-family: PingFang SC, PingFang SC;
						font-weight: 500;
						font-size: 0.16rem;
						color: #000000;
					}

					.lists-code {
						margin-top: 0.1rem;
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 0.12rem;
						color: #909090;
					}
				}

				.lists-right {
					padding: 0 0.1rem;
					height: 0.3rem;
					background: linear-gradient( 90deg, #1B167A 0%, #4383C7 100%);
					border-radius: 0.04rem 0.04rem 0.04rem 0.04rem;
					text-align: center;
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 0.14rem;
					color: #FFFFFF;
					line-height: 0.3rem;
				}
			}

			.lists-row {
				padding: 0.12rem 0 0;
				.lists-col {
					padding: 0.05rem 0;

					.lists-key {
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 0.12rem;
						color: #767676;
					}

					.lists-val {
						font-family: PingFang SC, PingFang SC;
						font-weight: 500;
						font-size: 0.14rem;
						color: #000000;

						&.sec {
							color: #FE2B16;
						}
					}
				}
			}
		}
	}

	::v-deep .van-empty__description {
		font-size: 1.85rem;
	}

	::v-deep .van-popup__close-icon {
		font-size: 28px;
	}

	.vpopup-body {
		background: #232429;
		border-radius: 0.12rem;
		height: 100%;
		padding-bottom: 0.1rem;

		.vpopup-head {
			background: linear-gradient(90deg, #98EF86, #C7F377);
			padding: 0.12rem;
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 0.19rem;
			color: #000;
			text-align: center;
		}

		.vpopup-input {
			padding: 0.05rem 0.12rem;
			width: 100%;

			span {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.12rem;
				color: #fff;
			}

			.van-field {
				margin-top: 10px;
				width: 100%;
				line-height: 0.44rem;
				height: 0.44rem;
				background: #434446;
				border-radius: 0.04rem 0.04rem 0.04rem 0.04rem;
				padding: 0;

				::v-deep input {
					padding: 0 0.1rem;
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 0.14rem;
					color: #FFFFFF;
				}
				::v-deep input::placeholder{
					font-weight: 400;
					font-size: 0.12rem;
					color: #999 !important;
				}
				::v-deep .van-field__control:disabled{
					-webkit-text-fill-color: #999 !important;
				}
			}
		}
	}
</style>