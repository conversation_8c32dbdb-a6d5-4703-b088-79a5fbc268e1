<template>
	<div class="page ">
		<top-back :title="$t('用户协议')"></top-back>

		<div class="list">
			<div class="item">
				{{ $t("about").txt2 }}
			</div>
			<div class="item" v-html="info"></div>
			<no-data v-if="!info"></no-data>
		</div>
	</div>
</template>

<script>
	export default {
		name: "aboutInfo",
		props: {},
		data() {
			return {
				info: "",
			};
		},
		components: {},
		methods: {
			initData() {
				this.$server.post("/common/wenben", {
						type: this.$stockType,
						name: "用户协议"
					})
					.then((res) => {
						if (res.status == 1) {
							this.info = res.data;
						}
					});
			},
		},
		created() {
			this.initData();
		},
		computed: {},
	};
</script>

<style scoped lang="less">
	::v-deep .item p {
		width: 100%;
		word-break: break-word;
	}

	::v-deep .item span {
		display: block;
		width: 100%;
	}

	.page {
		// background: #fff;
		padding: 0.6rem 0.15rem 0.2rem;
		min-height: 100vh;
	}

	.list {
		background: #ffffff;
		border-radius: 0.08rem;
		padding: 0.15rem;

		.item {
			line-height: 0.3rem;
			// margin-bottom: 0.1rem;
		}
	}
</style>