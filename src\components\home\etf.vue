<template>
	<div class="page ">
		<top-back title="ETF"></top-back>
		<div class="navs">
			<div class="nav-box flex flex-b">
				<div class="nav-item" v-for="(item, index) in typList" :key="index"
					:class="{ active: currmentIndex == item.id }" @click="changeNav(item.id)">
					{{ item.name }}
				</div>
			</div>
		</div>
		
		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<van-skeleton title :row="25" :loading="loading">
				<no-data v-if="isShow"></no-data>
				<div class="list">
					<div class="titles flex">
						<div class="flex-2">股票名稱</div>
						<div class="flex-1">現價</div>
						<div class="flex-1">漲跌幅</div>
						<div class="" style="width: 0.4rem;"></div>
					</div>
					<div class="item" v-for="(item, index) in chooseList" :key="index" @click="stockDetails(item)">
						<div class="flex">
							<div class="flex flex-2">
								<div class="st" :class="item.changePercent.indexOf('-') > -1 ? 'green-bg' : 'red-bg'">
									{{ item.changePercent.indexOf("-") > -1 ? "賣" : "買" }}
								</div>
								<div>
									<div class="name">{{ item.symbolName }}</div>
									<div class="code">{{ item.systexId }}</div>
								</div>
							</div>
							<div class="price flex-1 red" :class="{ 'green': item.changePercent.indexOf('-') > -1 }">{{ item.price }}</div>
							<div class="red per" :class="{ green: item.changePercent.indexOf('-') > -1 }">
								{{ item.changePercent }}</div>
							<div class="btn">買入</div>
						</div>
					</div>
				</div>
			</van-skeleton>
		</van-pull-refresh>

		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "etf",
		data() {
			return {
				loading: true,
				isShow: false,
				isLoading: false,
				currmentIndex: "taiwan",
				stockObj: {},
				chooseList: [],
				typList: [{
						name: "台灣",
						id: "taiwan"
					},
					{
						name: "中國",
						id: "china"
					},
					{
						name: "美國",
						id: "us"
					},
					{
						name: "亞洲",
						id: "asia"
					},
					{
						name: "全球",
						id: "global"
					},
					{
						name: "其他",
						id: "others"
					},
				],
				timer: null,
			};
		},
		computed: {},
		mounted() {
			this.getNew();
			this.timer = setInterval(() => {
				this.getNew();
			}, 10000);
		},
		destroyed() {
			this.timer && clearInterval(this.timer);
		},
		methods: {
			changeNav(id) {
				this.currmentIndex = id;
				this.$refs.loading.open();
				this.getNew();
			},
			// 下拉刷新
			onRefresh() {
				this.isShow = false;
				this.getNew();
			},
			getNew() {
				this.$server
					.post("/parameter/etf", {
						regionId: this.currmentIndex
					})
					.then((res) => {
						this.isLoading = false;
						this.loading = false;
						this.$refs.loading.close();

						if (res.data && res.data.list.length) {
							this.chooseList = res.data.list;
						}

						if (!this.chooseList.length) {
							this.isShow = true;
						}
					});
			},
			stockDetails(item) {
				this.$toDetail(`/market/stockDetail?symbol=${item.systexId}`, item);
			},
		},
	};
</script>
<style scoped lang="less">
	.page {
		padding: 0.84rem 0 0.1rem;
		min-height: 100vh;
		position: relative;
	}

	::v-deep .van-skeleton__row {
		background-color: transparent !important;
	}

	::v-deep .van-skeleton__title {
		background-color: transparent !important;
	}
	.navs{
		position: fixed;
		top: 0.5rem;
		left: 0;
		z-index: 999;
		width: 100%;
		padding: 0 0.12rem;
	}
	.nav-box {
		background: #fff;
		padding: 0 0.12rem;
		border-radius: 0.04rem 0.04rem 0.04rem 0.04rem;
		.nav-item {
			flex: 1;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 0.14rem;
			color: #64726A;
			line-height: 0.36rem;
			text-align: center;
			position: relative;
			&.active {
				height: 0.36rem;
				background: linear-gradient( 90deg, #1B167A 0%, #4383C7 100%);
				border-radius: 0.04rem 0.04rem 0.04rem 0.04rem;	
				color: #FFFFFF;
				line-height: 0.36rem;
				position: relative;
				// &::after {
				// 	position: absolute;
				// 	content: '';
				// 	bottom: 0;
				// 	left: 50%;
				// 	transform: translateX(-50%);
				// 	width: 50%;
				// 	height: 0.02rem;
				// 	background-color: #E5C79F;
				// }
			}
		}
	}

	.list {
		margin: 0.12rem;
		background: #FFFFFF;
		border-radius: 0.06rem 0.06rem 0.06rem 0.06rem;
		padding: 0.12rem;
		.titles {
			padding: 0.1rem 0;
			border-bottom: 0.01rem solid #E0E0E0;
			div{
				font-family: PingFang SC;
				font-weight: 500;
				font-size: 0.12rem;
				color: #8B8B8B;
			}
		}

		.item {
			padding: 0.12rem 0;
			border-bottom: 0.01rem solid #E0E0E0;
			&:last-child{
				border-bottom: none;
			}
			.st {
				font-size: 0.12rem;
				color: #ffffff;
				// padding: 0.02rem;
				width: 0.18rem;
				height: 0.18rem;
				text-align: center;
				line-height: 0.18rem;
				border-radius: 0.02rem;
				margin-right: 0.1rem;

				&.red-bg {
					background-color: #FE0200;
				}

				&.green-bg {
					background-color: #3CAD21;
				}
			}

			.name {
				font-family: PingFang SC;
				font-weight: 500;
				font-size: 0.12rem;
				color: #333333;
			}
			
			.code {
				margin-top: 0.05rem;
				font-family: PingFang SC;
				font-weight: 500;
				font-size: 0.11rem;
				color: #B4B4B4;
			}

			.price,
			.per {
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 0.14rem;
				color: #FFFFFF;
			}

			.t {
				font-size: 0.12rem;
				color: #9f9fa3;
			}

			.btn {
				margin-left: 0.3rem;
				height: 0.28rem;
				background: linear-gradient( 90deg, #1B167A 0%, #4383C7 100%);
				border-radius: 0.06rem 0.06rem 0.06rem 0.06rem;
				line-height: 0.28rem;
				padding: 0 0.1rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
				color: #fff;
			}
		}
	}
</style>