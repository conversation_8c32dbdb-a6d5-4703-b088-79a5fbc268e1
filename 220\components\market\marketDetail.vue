<template>
	<div class="marketDetailPage">
		<div class="Header flex flex-c">
			<div class="back" @click="clickBack()">
				<img src="../../assets/v2/back.png" style="width:0.2rem;height:0.15rem;" />
			</div>
			<div class="title flex-column-item">{{details.local_name}}<span>{{details.symbol}}</span></div>
			<div class="favo" @click="addSelect(details)">
				<img src="../../assets/skin/market/favo1.png" v-if="details.is_zixuan" />
				<img src="../../assets/skin/market/favo2.png" v-else />
			</div>
		</div>
		<div class="Info flex flex-b">
			<div class="price" :class="{'red':details.gain>=0,'green':details.gain<0}">{{$formatMoney(details.price)}}円</div>
			<div class="per">
				<div class="tt">{{details.gain>0?'+':''}}{{$formatMoney(details.gainValue)}}円</div>
				<div :class="{'red':details.gain>=0,'green':details.gain<0}">{{details.gain>0?'+':''}}{{$formatMoney(details.gain)}}%</div>
			</div>
		</div>
		<div class="record flex flex-b flex_wrap">
			<div class="item flex flex-b">{{$t('sharesDetails').txt1}}<span>{{ $formatMoney(details.open) }}円</span></div>
			<div class="item flex flex-b">{{$t('sharesDetails').txt2}}<span>{{ $formatMoney(details.preClose) }}円</span></div>
			<div class="item  flex flex-b">{{$t('sharesDetails').txt3}}<span>{{ $formatMoney(details.high) }}円</span></div>
			<div class="item flex flex-b">{{$t('sharesDetails').txt4}}<span>{{ $formatMoney(details.low) }}円</span></div>
			<div class="item flex flex-b">
				{{$t('sharesDetails').txt5}}<span>{{$formatMoney(parseFloat(details.volume)/100000000) }}{{$t('sharesDetails').txt7}}</span>
			</div>
			<div class="item  flex flex-b">
				{{$t('sharesDetails').txt6}}
				<span
					v-if="details.Amount">{{$formatMoney(parseFloat(details.Amount)/100000000) }}{{$t('sharesDetails').txt7}}</span>
				<span v-else>-</span>
			</div>
		</div>
		<div class="chartBox">
			<chart-21 :detail="details" :code="details.symbol" :stype="type" />
		</div>
		<div class="pageBox">
			<div class="pageTitle">
				<img src="../../assets/skin/market/<EMAIL>" />
				{{$t('sharesDetails').txt8}}
			</div>
			<div class="pageBox-title" v-if="false">
				<div class="pageBox-title-box green">{{$t('sharesDetails').txt9}}</div>
				<div class="pageBox-title-box red">{{$t('sharesDetails').txt10}}</div>
			</div>
			<div class="pageBox-list">
				<div class="pageBox-list-box">
					<div class="pageBox-list-item flex-start">{{$t('sharesDetails').txt11}}1</div>
					<div class="pageBox-list-item green">{{$formatMoney(details.buy1)}}円</div>
					<div class="pageBox-list-item flex-end">
						<div class="greenLine" :style="'width:' + parseInt(details.buyv1)/10 + '%'">{{details.buyv1}}
						</div>
					</div>
				</div>
				<div class="pageBox-list-box">
					<div class="pageBox-list-item flex-start">{{$t('sharesDetails').txt12}}1</div>
					<div class="pageBox-list-item red">{{$formatMoney(details.sell1)}}円</div>
					<div class="pageBox-list-item flex-end">
						<div class="redLine" :style="'width:' + parseInt(details.sellv1)/10 + '%'">{{details.sellv1}}
						</div>
					</div>
				</div>
			</div>
			<div class="pageBox-list">
				<div class="pageBox-list-box">
					<div class="pageBox-list-item flex-start">{{$t('sharesDetails').txt11}}2</div>
					<div class="pageBox-list-item green">{{$formatMoney(details.buy2)}}円</div>
					<div class="pageBox-list-item flex-end">
						<div class="greenLine" :style="'width:' + parseInt(details.buyv2)/10 + '%'">{{details.buyv2}}
						</div>
					</div>
				</div>
				<div class="pageBox-list-box">
					<div class="pageBox-list-item flex-start">{{$t('sharesDetails').txt12}}2</div>
					<div class="pageBox-list-item red">{{$formatMoney(details.sell2)}}円</div>
					<div class="pageBox-list-item flex-end">
						<div class="redLine" :style="'width:' + parseInt(details.sellv2)/10 + '%'">{{details.sellv2}}
						</div>
					</div>
				</div>
			</div>
			<div class="pageBox-list">
				<div class="pageBox-list-box">
					<div class="pageBox-list-item flex-start">{{$t('sharesDetails').txt11}}3</div>
					<div class="pageBox-list-item green">{{$formatMoney(details.buy3)}}円</div>
					<div class="pageBox-list-item flex-end">
						<div class="greenLine" :style="'width:' + parseInt(details.buyv3)/10 + '%'">{{details.buyv3}}
						</div>
					</div>
				</div>
				<div class="pageBox-list-box">
					<div class="pageBox-list-item flex-start">{{$t('sharesDetails').txt12}}3</div>
					<div class="pageBox-list-item red">{{$formatMoney(details.sell3)}}円</div>
					<div class="pageBox-list-item flex-end">
						<div class="redLine" :style="'width:' + parseInt(details.sellv3)/10 + '%'">{{details.sellv3}}
						</div>
					</div>
				</div>
			</div>
			<div class="pageBox-list">
				<div class="pageBox-list-box">
					<div class="pageBox-list-item flex-start">{{$t('sharesDetails').txt11}}4</div>
					<div class="pageBox-list-item green">{{$formatMoney(details.buy4)}}円</div>
					<div class="pageBox-list-item flex-end">
						<div class="greenLine" :style="'width:' + parseInt(details.buyv4)/10 + '%'">{{details.buyv4}}
						</div>
					</div>
				</div>
				<div class="pageBox-list-box">
					<div class="pageBox-list-item flex-start">{{$t('sharesDetails').txt12}}4</div>
					<div class="pageBox-list-item red">{{$formatMoney(details.sell4)}}円</div>
					<div class="pageBox-list-item flex-end">
						<div class="redLine" :style="'width:' + parseInt(details.sellv4)/10 + '%'">{{details.sellv4}}
						</div>
					</div>
				</div>
			</div>
			<div class="pageBox-list">
				<div class="pageBox-list-box">
					<div class="pageBox-list-item flex-start">{{$t('sharesDetails').txt11}}5</div>
					<div class="pageBox-list-item green">{{$formatMoney(details.buy5)}}円</div>
					<div class="pageBox-list-item flex-end">
						<div class="greenLine" :style="'width:' + parseInt(details.buyv5)/10 + '%'">{{details.buyv5}}
						</div>
					</div>
				</div>
				<div class="pageBox-list-box">
					<div class="pageBox-list-item flex-start">{{$t('sharesDetails').txt12}}5</div>
					<div class="pageBox-list-item red">{{$formatMoney(details.sell5)}}円</div>
					<div class="pageBox-list-item flex-end">
						<div class="redLine" :style="'width:' + parseInt(details.sellv5)/10 + '%'">{{details.sellv5}}
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="bottom">
			<div class="btn" @click="sharesBuy(details, 'up')">{{$t('sharesDetails').txt11}}</div>
			<div class="btn btn2" @click="sharesBuy(details, 'down')">{{$t('sharesDetails').txt12}}
			</div>
			<!-- <div class="favo " @click="addSelect(details)">
				<img src="../../assets/skin/market/favo3.png" v-if="details.is_zixuan" />
				<img src="../../assets/skin/market/favo4.png" v-else />
			</div> -->
		</div>
	</div>
</template>

<script>
	import Vue from 'vue';
	import qs from 'qs';
	import axios from 'axios';
	import {
		Toast
	} from 'vant';
	Vue.use(Toast);
	import Chart21 from '../market/chart21.vue'
	export default {
		name: "marketDetail",
		data() {
			return {
				symbol: '',
				type: null,
				details: {}
			}
		},
		components: {
			Chart21
		},
		methods: {
			getDetail() {
				this.$server.post("/trade/stockdetails", {
					symbol: this.symbol,
          type: 'jpy'
				}).then((res) => {
					let vo = res.data.data || {};
					let gz = this.details.is_zixuan;
					if (res.data.data) {
						this.details = vo;
						this.details.is_zixuan = gz;
					}
					if (typeof(this.details.is_zixuan) == "undefined") {
						this.$getOp(this.details.symbol, this.type, b => {
							this.details.is_zixuan = b;
							this.details = {
								...this.details
							}
						});
					}
				});
			},
			sharesBuy(stock, type) {
				var link = "/market/marketBuy?symbol=" + stock.symbol + '&buyType=' + type + '&type=' + this.type;
				this.clickNext(link);
			},
			addSelect(obj) {
				if (!obj.is_zixuan) {
					this.$server.post("/user/addOptional", {
						symbol: this.symbol,
            type: 'jpy'
					}).then((res) => {
						if (res.data.status === 1) {
							obj.is_zixuan = 1;
							this.details = {
								...obj
							};
							this.$forceUpdate()
						}
					});
				} else {
					this.$server.post("/user/removeOptional", {
						symbol: obj.symbol,
            type: 'jpy'
					}).then((res) => {
						if (res.data.status === 1) {
							obj.is_zixuan = 0;
							this.details = {
								...obj
							};
						}
					});
				}
			},
		},
		destroyed() {

		},
		mounted() {
			this.symbol = this.$route.query.symbol;
			this.getDetail();
		},
	}
</script>

<style lang="less">
	.marketDetailPage {
		background: #0F161C;
		min-height: 100vh;
		padding-top: .47rem;
		width: 100%;
		overflow: hidden;

		.Header {
			height: .47rem;
			width: 100%;
			background: #424E4E;
			position: fixed;
			top: 0;
			left: 0;
			z-index: 888;

			.back {
				position: absolute;
				top: .12rem;
				left: .16rem;

				img {
					width: .1rem;
				}
			}

			.title {
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 0.16rem;
				color: #FFFFFF;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
				line-height: 0.2rem;
				span {
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.14rem;
					color: #FFFFFF;
				}
			}
			.favo {
				position: absolute;
				top: .12rem;
				right: .16rem;
				img {
					width: .2rem;
					height: .2rem;
				}
			}
		}

		.Info {
			padding: 0.15rem 0.12rem;
			border-bottom: 0.05rem solid #40464c;
			.price {
				position: relative;
				z-index: 10;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 0.23rem;
				&.red {
					.per {
						background: #F53E3E;
					}
				}
				&.green {
					.per {
						background: #00B258;
					}
				}
			}
			.per {
				font-weight: 500;
				font-size: 0.18rem;
				.tt{
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.18rem;
					color: #FFFFFF;
					text-align: right;
				}
			}
		}
		.record {
			position: relative;
			z-index: 5;
			margin: .08rem .12rem;
			.item {
				width: 47%;
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 0.11rem;
				color: #718A94;
				padding: 0.08rem 0;
				border-top: 0.01rem solid rgba(255, 255, 255, 0.16);
				&:first-child{
					border-top: none;
				}
				&:nth-child(2){
					border-top: none;
				}
		
				span {
					font-family: PingFangSC, PingFang SC;
					font-weight: 600;
					font-size: 0.11rem;
					color: #FFFFFF;
				}
			}
		}

		.pageBox {
			padding: 0.2rem .12rem;
			.pageTitle {
				font-size: .17rem;
				font-family: FZDaHei-B02T;
				font-weight: 400;
				color: #fff;

				img {
					width: .19rem;
					margin-right: .06rem;
				}
			}

			.pageBox-title {
				height: .4rem;
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-bottom: .05rem;

				.pageBox-title-box {
					width: calc(50% - .13rem);
					height: .4rem;
					font-size: .14rem;
					font-family: FZLanTingHeiT-R-GB;
					font-weight: 400;
					color: #FFFFFF;
					display: flex;
					align-items: center;
					justify-content: center;
					border-radius: .12rem;
				}

				.red {
					background: #FA2256;
				}

				.green {
					background: #19C09A;
				}
			}

			.pageBox-list {
				height: .25rem;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.pageBox-list-box {
					width: calc(50% - .13rem);
					height: .25rem;
					display: flex;
					align-items: center;
				}

				.pageBox-list-item {
					width: calc(100% / 3);
					height: .25rem;
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.12rem;
					color: #FFFFFF;
					display: flex;
					align-items: center;
					justify-content: center;
				}

				.flex-start {
					justify-content: flex-start;
				}

				.flex-end {
					justify-content: flex-end;
				}

				.red {
					color: #FF4B76;
				}

				.green {
					color: #19C09A;
				}

				.redLine {
					max-width: 100%;
					height: .12rem;
					background: rgba(226, 38, 36, .2);
					padding: 0 .05rem;
					box-sizing: border-box;
					font-size: .12rem;
					font-family: PingFang SC;
					font-weight: 500;
					color: #E22624;
					display: flex;
					align-items: center;
					justify-content: flex-end;
				}

				.greenLine {
					max-width: 100%;
					height: .12rem;
					background: rgba(0, 170, 112, .2);
					padding: 0 .05rem;
					box-sizing: border-box;
					font-size: .12rem;
					font-family: PingFang SC;
					font-weight: 500;
					color: #00AA70;
					display: flex;
					align-items: center;
					justify-content: flex-end;
				}
			}
		}

		.bottom {
			margin: 0.1rem 0.12rem;
			.btn {
				margin-bottom: 0.1rem;
				height: .46rem;
				background: #5ED5A8;
				border-radius: 0.1rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 0.16rem;
				color: #171D22;
				text-align: center;
				line-height: 0.46rem;
			}
			.btn2{
				border: 0.01rem solid #5ED5A8;
				background: transparent;
				color: #5ED5A8;
			}

			.favo {
				width: .7rem;
				height: .27rem;
				
				img {
					width: .27rem;
				}
			}
		}

	}
</style>