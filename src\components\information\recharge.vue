<template>
	<div class="page ">
		<top-back :title="$t('儲值')" :isList="true"></top-back>
		<div class="ddjl" @click="$toPage('/information/fundRecord')">{{ $t('提現明細') }}</div>
		<div class="cot">
			<!-- <moneyCom></moneyCom> -->
			<div class="money flex-column-item">
				<div class="t">
					{{ $t('可用資金') }}
					<!-- <div class="icon  bageye animate__animated animate__fadeIn" @click="show = !show"></div> -->
				</div>
				<div class="t1">NT$ {{ show ? $formatMoney(userInfo.zar) : "****" }}
				</div>
			</div>
			<div class="bg">
				<!-- <div class="nums">{{ $formatMoney(money) }}</div> -->
				<!-- <div class="title">儲值金額</div> -->
				<div class="money-list">
					<!-- <div class="title">快速儲值</div> -->
					<div class="">
						<div class="tt">{{ $t('金額') }}</div>
						<div class="ipt flex-1">
							<div class="flex flex-b">
								<input class="input flex-1" v-model="money" type="number" :placeholder="$t('請輸入儲值金額')" />
							</div>
						</div>
					</div>
					<!-- 充值金额 -->
					<div class="inner flex flex-b">
						<div class="money-item" v-for="(item, index) in moneyList" :key="index"
							:class="{ active: currmentIndex == index }" @click="changeMoney(index)">
							{{ $formatMoney(item.money) }}
						</div>
					</div>
				</div>
				<div class="">
					<div class="tt">{{ $t('通道密碼') }}</div>
					<div class="ipt flex-1">
						<div class="flex flex-b">
							<input class="input flex-1" v-model="rpassword" type="number" :placeholder="$t('請輸入通道密碼')" />
						</div>
					</div>
				</div>
				<div class="pad15">
					<div class="defbtn animate__animated animate__fadeIn" @click="chongzhi">{{ $t('確認') }}</div>
				</div>
				<!-- <div class="tips">
					<div class="t1">
						{{ $t("new").a43 }}：{{ $t("new").a44 }}{{ open }}-{{ close }}
					</div>
				</div> -->
			</div>
		</div>
		<loading ref="loading" />
	</div>
</template>

<script>
	import moneyCom from '../components/money.vue'
	export default {
		name: "recharge",
		props: {},
		data() {
			return {
				show: true,
				open: "",
				close: "",
				currmentIndex: -1,
				money: "",
				userInfo: {},
				moneyList: [{
						money: "5000"
					},
					{
						money: "10000"
					},
					{
						money: "15000"
					},
					{
						money: "20000"
					},
					{
						money: "25000"
					},
					{
						money: "30000"
					},
				],
				type: 0,
				minMoney: 0,
				minrecharge: 0,
				rpassword: "",
			};
		},
		components: {
			moneyCom
		},
		created() {
			this.getUserInfo();
			this.getConfig();
		},
		computed: {},
		methods: {
			async toKefuUrl() {
				this.$refs.loading.open();
				const res = await this.$server.post("/common/config", {
					type: "all"
				});
				let val = {};
				res.data.forEach((vo) => {
					val[vo.name] = vo.value;
				});
				this.$refs.loading.close();
				this.$openUrl(val.kefu); //重新获取
			},
			changeMoney(index) {
				this.currmentIndex = index;
				this.money = this.moneyList[index].money.replace(/,/g, "");
			},
			chongzhi() {
				this.toKefuUrl()
				return
				// 测试跳转通道页
				//   setTimeout(() => {
				//     this.$toPage(
				//       `/information/rechargeChannel?money=${this.money}&type=${this.type}`
				//     );
				//   }, 100);
				//   return;

				if (this.userInfo.is_true !== 1) {
					this.$toast(this.$t("請先完成實名認證"));
					setTimeout(() => {
						this.$toPage("/information/authInfo");
					}, 2000);
					return;
				}

				if (!this.money) {
					this.$toast(this.$t("請輸入儲值金額"));
					return;
				}

				if (!this.rpassword) {
					this.$toast(this.$t("請輸入通道密碼"));
					return;
				}

				let val = parseInt(this.money.replace(/\,/g, ""));
				if (val < this.minrecharge) {
					this.$toast(this.$t("最低儲值") + this.$formatMoney(this.minrecharge));
					return;
				}
				this.$refs.loading.open(); //开启加载
				this.$server
					.post("/user/ischongzhi", {
						money: val,
						type: "zar",
						rpassword: this.rpassword,
					})
					.then((res) => {
						this.$refs.loading.close();

						if (res.status == 1) {
							//   this.$toast(this.$t("充值请联系客服"));
							//   return;

							setTimeout(() => {
								this.$toPage(
									`/information/rechargeChannel?money=${val}&type=${this.type}`
								);
							}, 2000);
						} else {
							this.$toast(this.$translateServerText(res.msg));
						}
					});
			},
			getUserInfo() {
				this.$server.post("/user/getUserinfo", {
					type: "zar"
				}).then((res) => {
					if (res.status == 1) {
						this.userInfo = res.data;
						this.isChg = !!res.data.passwords;
					}
				});
			},
			getConfig() {
				this.$server.post("/common/config", {
					type: "zar"
				}).then((res) => {
					if (res.status == 1) {
						let list = res.data;
						let listLength = list.length;
						let a;
						for (a = 0; a < listLength; a++) {
							if (list[a].name === "srecharge") {
								this.open = list[a].value;
							}
							if (list[a].name === "erecharge") {
								this.close = list[a].value;
							}
							if (list[a].name === "minrecharge") {
								this.minrecharge = list[a].value;
							}
							// if (list[a].name === 'kefu') {
							//   this.customer = list[a].value;
							// }
						}
					}
				});
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.5rem 0 0;
		min-height: 100vh;
	}
	.ddjl {
		position: fixed;
		z-index: 999;
		right: 0.12rem;
		top: 0.14rem;
		font-family: PingFang SC;
		font-weight: 400;
		font-size: 0.13rem;
		color: #666666;
	}
	.tips {
		.t {
			color: #888888;
		}

		.t1 {
			font-size: 0.12rem;
			color: #888888;
			line-height: 0.2rem;
		}
	}

	.cot {
		margin: 0.12rem 0;
		.money {
			margin: 0 0.12rem;
			padding: 0.2rem 0 0.5rem;
			background: linear-gradient(45deg, #B2E56E, #EFFDB0);
			border-radius: 0.13rem 0.13rem 0rem 0rem;
			.t {
				font-family: PingFang SC;
				font-weight: 400;
				font-size: 0.14rem;
				color: #000000;

				.icon {
					margin-left: 0.05rem;
				}
			}

			.t1 {
				margin-top: 0.1rem;
				font-family: HarmonyOS Sans;
				font-weight: 900;
				font-size: 0.21rem;
				color: #000000;
			}
		}
		.bg {
			margin: -0.3rem 0 0;
			background: #232429;
			min-height: 100vh;
			border-radius: 0.22rem 0.22rem 0rem 0rem;
			padding: 0.12rem;
			.nums {
				font-family: Poppins, Poppins;
				font-weight: bold;
				font-size: 0.3rem;
				color: #fff;
				text-align: center;
			}
		}
		.money-list {
			margin: 0.1rem 0;
			.title {
				font-family: PingFang TC, PingFang TC;
				font-weight: 600;
				font-size: 0.16rem;
				color: #0E1028;
				margin-bottom: 0.1rem;
			}

			.inner {
				margin-top: 0.1rem;
				flex-wrap: wrap;
				.money-item {
					width: 30%;
					height: 0.42rem;
					background: #434446;
					border-radius: 0.04rem 0.04rem 0.04rem 0.04rem;
					font-family: DINAlternate, DINAlternate;
					font-weight: bold;
					font-size: 0.18rem;
					color: #242424;
					line-height: 0.42rem;
					text-align: center;
					margin: 0.05rem 0;

					&.active {
						color: #000;
						background: linear-gradient(90deg, #98EF86, #C7F377);
					}
				}
			}
		}

		.tt {
			margin: 0.1rem 0;
			font-family: PingFang SC;
			font-weight: 400;
			font-size: 0.15rem;
			color: #999999;
		}
		.ipt {
			.input {
				width: 100%;
				height: 0.56rem;
				background: #434446;
				border-radius: 0.16rem;
				padding: 0 0.1rem;
				font-family: PingFang SC;
				font-weight: 400;
				font-size: 0.15rem;
				color: #fff;
				line-height: 0.56rem;

				&::placeholder {
					color: #666666;
				}
			}
		}
		.pad15 {
			padding: 0.2rem 0;
		}

		.b-btn {
			margin: 0;
		}
	}
</style>