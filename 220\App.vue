<template>
	<div id="app">
		<keep-alive>
			<router-view v-if="$route.meta.keepAlive" />
		</keep-alive>
		<router-view v-if="!$route.meta.keepAlive" />

	</div>
</template>
<script>
	import qs from 'qs';
	import axios from 'axios';
	import './assets/css/reset.css'
	import './assets/js/com'


	export default {
		data() {
			return {

			}
		},
		beforeCreate() {

			var _this = this;

			var first = null;

			document.addEventListener("plusready", function() {
				var webview = plus.webview.currentWebview();
				var ver = plus.runtime.version;
				// window.localStorage.setItem("pdver",ver)
				webview.setStyle({
					'popGesture': 'none'
				});
				plus.key.addEventListener("backbutton", function() {
					webview.canBack(function(e) {
						if (e.canBack) {
							webview.back();
						} else {
							if (!first) {
								first = new Date().getTime();
								setTimeout(function() {
									first = null;
								}, 1000);
							} else {
								if (new Date().getTime() - first < 1000) {
									plus.runtime.quit();
								}
							}
						}
					});
				});
				plus.navigator.setStatusBarBackground('#FFF');
				plus.navigator.setStatusBarStyle('dark');

			})
		},
		destroyed() {
			window.localStorage.removeItem("tklm");
			window.localStorage.removeItem("zjkm");
			window.localStorage.removeItem("hklm");
		},
		methods: {

		}
	}
</script>
<style lang="less">
	html,
	body,
	#app {}

	.van-tabs {
		flex: 1;
	}

	.flex {
		display: flex;
		align-items: center;

		&.flex-a {
			justify-content: space-around;
		}

		&.flex-b {
			justify-content: space-between;
		}

		&.flex-c {
			justify-content: center;
		}

		&.flex-e {
			justify-content: flex-end;
		}
	}

	.flex-only {
		display: flex;
	}

	.flex-column-item {
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.flex-1 {
		flex: 1
	}

	.flex-2 {
		flex: 2
	}

	.flex1 {
		flex: 1;
	}

	.flex_wrap {
		flex-wrap: wrap;
	}

	.flex-wrap {
		flex-wrap: wrap;
	}

	.blue {
		color: #0166b3;
	}

	.green {
		color: #3498DA;
	}

	.red {
		color: #FE0000;
	}

	.mb-10 {
		margin-bottom: .05rem;
	}

	.mb-20 {
		margin-bottom: .1rem;
	}

	.mr-20 {
		margin-right: .1rem;
	}

	.mt-20 {
		margin-top: .1rem;
	}

	.mt-30 {
		margin-top: .15rem;
	}

	.pb-10 {
		padding-bottom: .05rem;
	}

	.pt-40 {
		padding-top: .2rem;
	}

	.pt-60 {
		padding-top: .3rem;
	}

	.popLoad {
		position: fixed;
		top: 0;
		left: 0;
		z-index: 20000;
		width: 100%;
		height: 100vh;
		background: none !important;
	}

	.btn-big {
		height: 0.46rem;
		background: #5ED5A8;
		border-radius: 0.1rem;
		display: flex;
		align-items: center;
		justify-content: center;
		font-family: PingFangSC, PingFang SC;
		font-weight: 600;
		font-size: 0.14rem;
		color: #171D22;
	}

	.text-center {
		text-align: center;
	}

	.text-right {
		text-align: right;
	}


	.van-dialog__header {
		font-family: PingFangSC, PingFang SC;
		font-weight: 500;
		font-size: 0.16rem;
		color: #333333;
		padding: 0.2rem 0;
	}
	.van-dialog__message {
		font-weight: 500;
		font-size: 0.18rem;
		color: #333333;
		padding: 0.1rem 0;
	}

	.van-dialog__content {
		font-family: PingFangSC, PingFang SC;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.van-dialog__footer {
		padding: 0.2rem .3rem;

		&:after {
			border: 0;
		}
	}

	.van-button--default:after {
		border: 0;
	}

	.van-dialog__cancel {
		height: .44rem;
		border-radius: 0.05rem;
		border: 0.01rem solid #5ED5A8;
		margin-right: .1rem;
		font-family: PingFangSC, PingFang SC;
		font-weight: 500;
		font-size: 0.16rem;
		color: #5ED5A8;
	}

	.van-dialog__confirm {
		height: .44rem;
		background: #5ED5A8;
		border-radius: 0.05rem;
		margin-left: .08rem;
		font-family: PingFangSC, PingFang SC;
		font-weight: 500;
		font-size: 0.16rem;
		color: #333333;
	}

	.noData {
		margin-top: 1rem;
		font-size: .12rem;
		color: #999;
	}

	.icon {
		&.zy {
			width: 0.2rem;
			height: 0.16rem;
			background: url('assets/v2/zy.png') no-repeat center/100%;
		}

		&.by {
			width: 0.2rem;
			height: 0.16rem;
			background: url('assets/v2/by.png') no-repeat center/100%;
		}

		&.wgx {
			margin-right: 0.05rem;
			width: 0.16rem;
			height: 0.16rem;
			background: url('assets/v2/wgx.png') no-repeat center/100%;
		}

		&.gx {
			margin-right: 0.05rem;
			width: 0.16rem;
			height: 0.16rem;
			background: url('assets/v2/gx.png') no-repeat center/100%;
		}

		&.cdIcon {
			width: 0.2rem;
			height: 0.2rem;
			background: url('assets/v2/cdIcon.png') no-repeat center/100%;
		}

		&.sousuo {
			width: 0.21rem;
			height: 0.22rem;
			background: url('assets/v2/sousuo.png') no-repeat center/100%;
		}

		&.close {
			width: 0.21rem;
			height: 0.22rem;
			background: url('assets/v2/close.png') no-repeat center/100%;
		}

		&.set {
			margin-right: 0.05rem;
			width: 0.18rem;
			height: 0.17rem;
			background: url('assets/v2/set.png') no-repeat center/100%;
		}

		&.xxIcon {
			margin-right: 0.05rem;
			width: 0.15rem;
			height: 0.18rem;
			background: url('assets/v2/xxIcon.png') no-repeat center/100%;
		}

		&.czIcon {
			margin-right: 0.05rem;
			width: 0.14rem;
			height: 0.14rem;
			background: url('assets/v2/czIcon.png') no-repeat center/100%;
		}

		&.txIcon {
			margin-right: 0.05rem;
			width: 0.14rem;
			height: 0.14rem;
			background: url('assets/v2/txIcon.png') no-repeat center/100%;
		}

		&.gx02 {
			width: 0.17rem;
			height: 0.17rem;
			background: url('assets/v2/gx02.png') no-repeat center/100%;
		}

		&.wgx02 {
			width: 0.17rem;
			height: 0.17rem;
			background: url('assets/v2/wgx02.png') no-repeat center/100%;
		}

		&.close02 {
			width: 0.21rem;
			height: 0.21rem;
			background: url('assets/v2/close02.png') no-repeat center/100%;
		}
		&.sgjl {
			margin-right: 0.05rem;
			width: 0.14rem;
			height: 0.14rem;
			background: url('assets/v2/sgjl.png') no-repeat center/100%;
		}
		&.zjjl {
			margin-right: 0.05rem;
			width: 0.14rem;
			height: 0.14rem;
			background: url('assets/v2/zjjl.png') no-repeat center/100%;
		}
	}
</style>