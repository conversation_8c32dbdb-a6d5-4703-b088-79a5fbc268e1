<template>
	<div class="stockGaiKuang">
		<div class="title flex flex-b">
			<div>{{$t('market').gaiKuang.title1}}</div>
			<span v-if="list&&list[0]&&list[0].time">{{$t('market').gaiKuang.txt1}}: {{list[0].time}}</span>
		</div>
		<div class="nav flex">
			<div class="item flex" v-for="(item,idx) in navList" :class="{'sel':navIdx==idx}" :key="idx" @click="navIdx=idx,getInfo()">
				{{item.name}}
			</div>
		</div>
		<div class="list flex">
			<div class="item" v-for="(item,idx) in list" :key="idx" @click="listIdx=idx,getLast(item.code)">
				<div class="name flex">
					{{item.ko_name}}
					<img src="../../assets/skin/home/<USER>" v-if="item.gain>=0" />
					<img src="../../assets/skin/home/<USER>" v-else />
				</div>
				<div class="price" :class="item.gain>=0?'red':'green'">{{item.close}}</div>
				<div class="per" :class="item.gain>=0?'red':'green'">
					{{ item.gain <= 0 ? '' : '+' }}{{ $formatMoney(item.gainValue) }} {{ item.gain <= 0 ? '' : '+' }}{{ item.gain }}%
				</div>
			</div>
		</div>
		<div v-if="list&&list[listIdx]&&list[listIdx].stock_id">
			<!-- 显示指数k线详情 -->
			<k-line ref="kline"  :currentItem="list[listIdx]" :currentId="list[listIdx].stock_id"></k-line>
		</div>
		<div class="flex flex-b mt-20" v-if="lastInfo">
			<div class="">
				{{ $t('market').gaiKuang.txt5 }}
				<span v-if="lastInfo.individuals_net_vol_valued" :class="lastInfo.individuals_net_vol_valued < 0?'green':'red'">
					{{ lastInfo.individuals_net_vol_valued >= 0 ? '+' : '' }}{{ (lastInfo.individuals_net_vol_valued / 100000000).toFixed(0) }}{{ $t('market').gaiKuang.txt8 }}
				</span>
				<span v-else>0</span>
			</div>
			<div class="">
				{{ $t('market').gaiKuang.txt6 }}
				<span v-if="lastInfo.total_institutions_net_vol_valued" :class="lastInfo.total_institutions_net_vol_valued < 0?'green':'red'">
					{{ lastInfo.total_institutions_net_vol_valued >= 0 ? '+' : '' }}{{ (lastInfo.total_institutions_net_vol_valued / 100000000).toFixed(0) }}{{ $t('market').gaiKuang.txt8 }}
				</span>
				<span v-else>0</span>
			</div>
			<div class="">
				{{ $t('market').gaiKuang.txt7 }}
				<span v-if="lastInfo.total_foreigners_net_vol_valued" :class="lastInfo.total_foreigners_net_vol_valued < 0?'green':'red'">
					{{ lastInfo.total_foreigners_net_vol_valued >= 0 ? '+' : '' }}{{ (lastInfo.total_foreigners_net_vol_valued / 100000000).toFixed(0) }}{{ $t('market').gaiKuang.txt8 }}
				</span>
				<span v-else>0</span>
			</div>
		</div>
	</div>
</template>

<script>
	import Vue from 'vue';
	import qs from 'qs';
	import axios from 'axios';
	import {
		Toast
	} from 'vant';
	Vue.use(Toast);
	import kLine from './kLine.vue';
	export default {
		name: "stockGaiKuang",
		data() {
			return {
				navList: [
					{ name: this.$t('market').gaiKuang.txt2, type: 'gngs' }, 
					{ name: this.$t('market').gaiKuang.txt3, type: 'meiguo' }, 
					{ name: this.$t('market').gaiKuang.txt4, type: 'szhb' },
				],
				navIdx:0,
				list:[],
				listIdx:0,
				lastInfo:null,
			}
		},
		components: {
			kLine,
		},
		methods: {
			getInfo() {
				this.$server.post('/hanguo/gszk', { 
					type: this.navList[this.navIdx].type 
				}).then(res => {
					if (res.data.status == 1) {
						let arr = [];
						res.data.data.forEach(item => {
							// 美国的type 下面有一条数据没有details
							arr.push({
								ko_name: item.details ? item.details.ko_name : item.code.toLocaleUpperCase(),
								time: this.$formatDate('YYYY/MM/DD hh:mm:ss ', new Date().getTime()),
								gainValue: item.close - item.prev_close,
								gain: (((item.close - item.prev_close) / item.prev_close) * 100).toFixed(2),
								...item
							});
						});
						this.list = arr;
						this.getLast(this.list[this.listIdx].code);
					}
				});
			},
			getLast(code) {
				this.$server.post('/hanguo/gszk_last', { code }).then(res => {
					if (res.data.status == 1) {
						this.lastInfo = res.data.data;
					}
				});
			},
		},
		destroyed() {
			if(this.timeInit){
				clearInterval(this.timeInit)
			}
		},
		mounted() {
			var _this = this;
			this.getInfo();
			this.timeInit = setInterval(function(){
				_this.getInfo();
			},5000)
		},
	}
</script>

<style lang="less">
	.stockGaiKuang{
		margin:.12rem;padding:.2rem .05rem;
		background: #FFFFFF;
		box-shadow: 0 0 .11rem 0 rgba(0,0,0,0.08);
		border-radius: .1rem;
		.title{
			font-weight: bold;
			font-size: .18rem;
			color: #9A9A9A;
			span{font-size:.11rem;color:#999;}
		}
		.nav{
			margin:.2rem 0;
			.item{
				height: .21rem;
				border-radius: .11rem;
				border: .01rem solid #999999;
				padding:0 .12rem;margin-right:.13rem;
				font-weight: 500;
				font-size: .12rem;
				color: #999999;
				&.sel{
					border: 1px solid #000;color:#000;
				}
			}
		}
		.list{
			.item{
				background: #F5F5F5;
				border-radius: .05rem;
				margin-right:.06rem;
				width:33%;padding:.1rem;
				&:last-child{margin-right:0;}
				.name{
					font-size: .11rem;
					color: #333333;
					img{width:.08rem;margin-left:.06rem;}
				}
				.price{
					font-weight: bold;
					font-size: .16rem;
					margin:.06rem 0;
				}
				.per{
					font-weight: 400;
					font-size: .1rem;
				}
			}
		}
	}
</style>