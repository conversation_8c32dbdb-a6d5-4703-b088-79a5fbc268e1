 <template>
	<div class="bijnm">
		<div class="headf">
			<top :title='$t(text)'></top>
		</div>

		<div style="padding: .1rem;" v-show="!loading">
			<div style="background-color: #ededed;border-radius: .1rem;padding: .1rem;display: flex;
			justify-content: space-between;align-items: center;">
				<div v-if="dh == 0">
					<div style="padding: .1rem 0;">
						<div style="color: #333;font-weight: bold;font-size: .16rem;padding-bottom: .1rem;">{{$t("港币")}}</div>
						<p style="color: #777E8A;">{{$t("可用余额")}}:<span style="color: #D73D3D;padding: 0 .1rem;font-weight: bold;">{{hkd}}</span></p>
					</div>
					<div style="padding: .1rem 0;">
						<div  style="color: #333;font-weight: bold;font-size: .16rem;padding-bottom: .1rem;">{{$t("人民币")}}</div>
						<p style="color: #777E8A;">{{$t("可用余额")}}:<span style="color: #D73D3D;padding: 0 .1rem;font-weight: bold;">{{rmb}}</span></p>
					</div>
				</div>
				<div v-if="dh != 0">
					<div style="padding: .1rem 0;">
						<div style="color: #333;font-weight: bold;font-size: .16rem;padding-bottom: .1rem;">{{$t("人民币")}}</div>
						<p style="color: #777E8A;">{{$t("可用余额")}}:<span style="color: #D73D3D;padding: 0 .1rem;font-weight: bold;">{{rmb}}</span></p>
					</div>
					<div style="padding: .1rem 0;">
						<div  style="color: #333;font-weight: bold;font-size: .16rem;padding-bottom: .1rem;">{{$t("港币")}}</div>
						<p style="color: #777E8A;">{{$t("可用余额")}}:<span style="color: #D73D3D;padding: 0 .1rem;font-weight: bold;">{{hkd}}</span></p>
					</div>
				</div>
				<img @click="changeHb" src="../../assets/jiaoyi.png" style="width: .3rem;height: .3rem;">
			</div>
			<div v-if="dh == 0">
				<div style="color: #333;font-weight: bold;font-size: .16rem;padding: .2rem 0;">{{$t('实时汇率')}}</div>
				<p>
					1{{$t("港币等于")}}<span style="margin: 0 .1rem;color: #D73D3D;font-weight: bold;font-size: .18rem;">{{hkdtocny}}</span>{{$t("人民币")}}
				</p>
			</div>
			<div v-if="dh != 0">
				<div style="color: #333;font-weight: bold;font-size: .16rem;padding: .2rem 0;">{{$t('实时汇率')}}</div>
				<p>
					1{{$t("人民币等于")}}<span style="margin: 0 .1rem;color: #D73D3D;font-weight: bold;font-size: .18rem;">{{cnytohkd}}</span>{{$t("港币")}}
				</p>
			</div>
			<div>
				<div style="color: #333;font-weight: bold;font-size: .16rem;padding: .2rem 0;">{{$t('转换数量')}}</div>
				<input type="number" v-model="num" style="background-color: #ededed;border-radius: .1rem;padding: .1rem;
				width: 100%;" :placeholder="$t('请输入转换数量')"  />
			</div>
			<div class="dhbtn" @click="dhbtn">{{$t('兑换')}}</div>
			<div style="margin-top: .3rem;line-height: .25rem;">
				<p>{{$t("兑换说明")}}：</p>
				<p>{{$t("1、兑换路径：我的-货币兑换，选择货币兑换的币种，金额之后，即可进行货币兑换。")}}</p>
				<p>{{$t("2、汇率：由上游银行提供外汇市场实时汇率。")}}</p>
				<p>{{$t("3、费用：无需额外手续费。")}}</p>
			</div>
		</div>
		<van-skeleton title :row="20" :loading="loading" style="margin-top: .2rem;" />
	</div>
</template>
<script type="text/javascript">
import Vue from 'vue';
import qs from 'qs';
import axios from 'axios';
import top from '../bar/toper.vue'
import { Toast  } from 'vant';
Vue.use(Toast );
import { Skeleton } from 'vant';

Vue.use(Skeleton);
export default {
	name:"BankCard",
	data(){
		return {
			text:'货币兑换',
			id:this.$route.query.id,
			num : null,
			dh : 0,//默认港币兑换人民币
			rmb : '',
			hkd : '',
			hkdtocny : '',//港币兑换人民币汇率
			cnytohkd : '',//人民币兑换港币汇率
			userdata: '',
			loading : true,
			ktiem : null,
		}
	},
	components:{
		top,
	},
	methods:{
		dhbtn(){
			let type;
			if(this.dh == 0){
				type = 'RMB';
			}else{
				type = 'HKD';
			}
			if (!isNaN(this.num) && Number(this.num) > 0) {
			  // 输入是大于0的数字
				this.$server.post('/user/rotation',{
				  money : this.num,
				  type : type
			  }).then(str=>{
			  	if(str.data.status==1){
					this.getUserinfo();
			  		Toast({
			  			message:this.$t(str.data.msg),
			  			duration:2000
			  		});
			  	}else{
					Toast({
						message:this.$t(str.data.msg),
						duration:2000
					});
				}
			  })
			}else{
				Toast({
					message:this.$t("请输入正确的转换数量"),
					duration:2000
				});
			}
		},
		changeHb(){
			if(this.dh == 0){
				this.dh = 1;
			}else{
				this.dh = 0;
			}
		},
		getUserinfo(){
			this.$server.post('/user/getUserinfo').then(str=>{
				if(str.data.status==1){
					this.userdata=str.data.data;
					this.rmb = (Number(this.userdata.abalance)+Number(this.userdata.fbalance)).toFixed(2);
					if(str.data.data.hkd){
						this.hkd = str.data.data.hkd;
					}else{
						this.hkd = 0;
					}
					this.loading = false;
				}
			})
		},
		config(){
			clearInterval(this.ktiem)
			this.ktiem=setInterval(()=>{
				this.config();
			},5000)
			this.$server.post('/agu/config')
			.then(str=>{
				if(str.data.status==1){
					for(let i = 0;i<str.data.data.length;i++){
						if(str.data.data[i].name == "cnytohkd"){
							this.cnytohkd = str.data.data[i].value;
						}
						if(str.data.data[i].name == "hkdtocny"){
							this.hkdtocny = str.data.data[i].value;
						}
					}
				}

			})
		}
	},
	beforeDestroy() {
		clearInterval(this.ktiem)
	},
	mounted(){
		this.config();
		this.getUserinfo();
	},
}
</script>
<style type="text/css" lang="less" scoped="scoped">


	.xxxxxxx p{
		line-height: 0.25rem;
		text-align: left!important;
		font-size: 0.16rem!important;
	}
input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder{
  color: #999;
  font-size: 0.15rem;
}
.bijnm{
	background: #fff;
	min-height: 100vh;
}

.headf{
	width: 100%;
	height: 0.44rem;
	background: linear-gradient(233deg, #f36218, #f59934);
}
.hezi{
	width: 3.45rem;
	border-bottom:0.01rem solid #E0E0E0;
	margin:0 auto;
	margin-top:0.44rem;
	text-align: center;
	padding-bottom:0.2rem;
	img{
		width:0.6rem;
		height:0.6rem;
		border-radius: 50%;
	}
	h6{
		color:#333333;
	}
	p{
		color:#999999;
	}
}
.dhbtn{
	width: 100%;
	height: 0.4rem;
	background: #EA4445;
	border-radius: 0.05rem;
	color: #fff;
	font-size: 0.14rem;
	text-align: center;
	line-height: 0.4rem;
	margin-top: .3rem;
}
</style>
