<template>
	<div class="page">
		<top-back title="所有功能"></top-back>
		<div class="tabs flex flex-b">
			<div class="tabItem" v-for="(item,index) in navs" :key="index" :class="{active:tabIndex==index}"
				@click="tabIndex=index">{{item}}</div>
		</div>
		<template v-if="tabIndex==0">
			<div class="list flex" v-for="(item,i) in list" :key="i"
				@click="$toPage(item.url, item.par && { par: item.par })">
				<img :src="item.icon" alt="" style="width: 0.3rem;height: 0.3rem;">
				<div class="name">{{item.name}}</div>
			</div>
		</template>
		<template v-if="tabIndex==1">
			<div class="list flex" v-for="(item,i) in list01" :key="i" @click="goUrl(item.url)">
				<div class="icon" :class="item.icon"></div>
				<div class="name">{{item.name}}</div>
			</div>
		</template>
		<template v-if="tabIndex==2">
			<div class="list flex" v-for="(item,i) in list02" :key="i" @click="goUrl(item.url)">
				<img :src="item.icon" alt="" style="width: 28px;height: 28px;">
				<div class="name">{{item.name}}</div>
			</div>
		</template>
		<template v-if="tabIndex==3">
			<div class="list flex" v-for="(item,i) in list03" :key="i" @click="goUrl(item.url)">
				<div class="icon" :class="item.icon"></div>
				<div class="name">{{item.name}}</div>
			</div>
		</template>
	</div>
</template>

<script>
	export default {
		data() {
			return {
				tabIndex: 0,
				navs: ['交易', '財務', '市場消息', '個人服務'],
				list: [{
						name: '新股申購',
						icon: require('../../assets/v2/xgsg.png'),
						url: '/home/<USER>',
						par: 2
					},
					{
						name: '競拍',
						icon: require('../../assets/v2/hlgp.png'),
						url: '/home/<USER>',
						par: 3
					}, {
						name: 'ETF',
						icon: require('../../assets/v2/ETF.png'),
						url: '/home/<USER>'
					},
					{
						name: '現股交易',
						icon: require('../../assets/v2/sdxd.png'),
						url: '/favorite/moreList'
					},
					{
						name: '庫藏籌碼',
						icon: require('../../assets/v2/dzjy.png'),
						url: '/market/Plate1'
					},
					{
						name: 'ETF募集',
						icon: require('../../assets/v2/cgjq.png'),
						url: '/home/<USER>'
					},
					{
						name: '存股借券',
						icon: require('../../assets/v2/zczl.png'),
						url: '/home/<USER>'
					},
				],
				list01: [{
						icon: 'wdzh',
						name: '我的帳戶',
						url: '/information/index'
					},
					{
						icon: 'wdcc',
						name: '我的持倉',
						url: '/trade/index'
					},
					{
						icon: 'zjkgl',
						name: '金融卡管理',
						url: '/information/bankList'
					},
					// {
					// 	icon:'cz',
					// 	name:'儲值',
					// 	url:'/information/recharge'
					// },
					{
						icon: 'tx',
						name: '提領',
						url: '/information/cashOut'
					},
					{
						icon: 'zjls',
						name: '資金流水',
						url: '/information/fundRecord'
					}
				],
				list02: [{
						icon: require('../../assets/v2/dpzs.png'),
						name: '大盤指數',
						url: '/market/largeMarket'
					},
					{
						icon: require('../../assets/v2/xwzx.png'),
						name: '新聞資訊',
						url: '/home/<USER>'
					},
				],
				list03: [{
						icon: 'smrz',
						name: '實名認證',
						url: '/information/authInfo'
					},
					{
						icon: 'tongzhi',
						name: '通知公告',
						url: '/information/userInfo'
					},
					{
						icon: 'dlmm',
						name: '登入密碼',
						url: '/information/loginPass'
					},
					{
						icon: 'zjmm',
						name: '資金密碼',
						url: '/information/fundPass'
					},
					{
						icon: 'zxkf',
						name: '聯系客服',
						url: 'kefu'
					},
				]
			}
		},
		mounted() {
			if (this.$route.query.tab) {
				this.tabIndex = this.$route.query.tab
			}
		},
		methods: {
			async getConfig() {
				const res = await this.$server.post("/common/config", {
					type: "all"
				});
				let val = {};
				res.data.forEach((vo) => {
					val[vo.name] = vo.value;
				});
				this.$openUrl(val.kefu); //重新获取
			},
			goUrl(url) {
				if (url == 'kefu') {
					this.getConfig()
				} else {
					this.$toPage(url)
				}
			},
		}
	}
</script>

<style lang="less" scoped>
	.page {
		padding-top: 0.5rem;
	}

	.tabs {

		background: #252525;

		.tabItem {
			width: 25%;
			text-align: center;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 0.15rem;
			color: #666666;
			line-height: 0.4rem;
			position: relative;
		}

		.active {
			color: #fff;

			&::after {
				position: absolute;
				content: '';
				left: 50%;
				transform: translateX(-50%);
				bottom: 0;
				width: 100%;
				height: 0.03rem;
				background-color: #c94d5b;
			}
		}
	}

	.list {
		margin: 0.1rem 0.15rem;
		padding: 0.1rem 0;
		border-bottom: 0.01rem solid #2B2B2B;

		.name {
			margin-left: 16px;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 0.15rem;
			color: #FFFFFF;
		}
	}
</style>