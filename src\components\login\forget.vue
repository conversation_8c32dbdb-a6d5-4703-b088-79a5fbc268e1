<template>
	<div class="pages">
		<div class="point1"></div>
		<div class="point2"></div>
		<div class="boxs">
			<div class="tit">忘記密碼</div>
			<div class="inputs">
				<!-- <div class="tt">身分證</div> -->
				<div class="inputs-i flex">
					<!-- <div class="icon sfz"></div> -->
					<van-field v-model="phone" :border="false" placeholder="請輸入身分證" />
				</div>
				<div class="inputs-i flex">
					<!-- <div class="icon mm"></div> -->
					<van-field v-model="password" :type="showPass ? 'text' : 'password'" :border="false" placeholder="請輸入驗證碼" />
					<div class="t">獲取驗證碼</div>
				</div>
				<!-- <div class="tt">密碼</div> -->
				<div class="inputs-i flex">
					<!-- <div class="icon mm"></div> -->
					<van-field v-model="password" :type="showPass ? 'text' : 'password'" :border="false"
						placeholder="請輸入密碼" />
					<div class="show">
						<img src="../../assets/v6/by.png" v-show="showPass" @click="showPass = !showPass" alt="" />
						<img src="../../assets/v6/zy.png" v-show="!showPass" @click="showPass = !showPass" alt="" />
					</div>
				</div>
				<div class="inputs-i flex">
					<!-- <div class="icon mm"></div> -->
					<van-field v-model="password" :type="showPass ? 'text' : 'password'" :border="false"
						placeholder="請輸入您的新密碼" />
					<div class="show">
						<img src="../../assets/v6/by.png" v-show="showPass" @click="showPass = !showPass" alt="" />
						<img src="../../assets/v6/zy.png" v-show="!showPass" @click="showPass = !showPass" alt="" />
					</div>
				</div>
			</div>
			<div class="btns">
				<div class="login flex flex-c" @click="submit">確認</div>
				<div class="register flex flex-c" @click="$toPage('/login/login')">去登入</div>
			</div>
		</div>
	</div>
</template>

<script>
	import {
		mapMutations
	} from "vuex";
	export default {
		name: "Login",
		data() {
			return {
				phone: "",
				password: "",
				config: {},
				showPass: false,
				rembAcc: false,
				tabIndex: 1,
			};
		},
		components: {
		},
		created() {
			this.getConfig();
			const remb = JSON.parse(localStorage.getItem("rembInfo"));
			if (remb) {
				this.phone = remb.account;
				this.password = remb.password;
				this.rembAcc = true;
			}
		},
		methods: {
			selectLogin() {
				this.tabIndex = 1;
			},
			...mapMutations(["saveToken", "saveAccount"]),
			getConfig() {
				this.$server
					.post("/common/config", {
						type: "all",
					})
					.then((res) => {
						if (res && res.status == 1) {
							const arr = res.data;
							const obj = {};
							arr.forEach((item) => {
								obj[item.name] = item.value;
							});
							this.config = obj;
						}
					});
			},
			rembChange(value) {
				// if (!this.phone || !this.password) return;
				this.rembAcc = value;
				if (value) {
					const obj = {
						account: this.phone,
						password: this.password,
					};
					localStorage.setItem("rembInfo", JSON.stringify(obj));
				} else {
					localStorage.removeItem("rembInfo");
				}
			},
			submit() {
				if (!this.phone) return this.$vApi.Toast("請輸入身分證");
				if (!this.password) return this.$vApi.Toast("請輸入密碼");
				this.$vApi.Toast.loading({
					message: "登入...",
					forbidClick: true,
					loadingType: "spinner",
				});
				this.$server
					.post("/user/login", {
						account: this.phone,
						password: this.password,
					})
					.then((res) => {
						if (res && res.status == 1) {
							this.$vApi.Toast.success("登入成功");
							this.$server.defaults.headers.token = res.data.token;
							this.$server.defaults.headers.account = res.data.account;
							this.saveToken(res.data.token);
							this.saveAccount(res.data.account);
							setTimeout(() => {
								// this.$toPage('/login/loginAfter')
								this.$router.replace("/home/<USER>");
							}, 1000);
						} else {
							this.$vApi.Toast.fail(this.$t(res.msg));
						}
					});
			},
		},
	};
</script>

<style lang="less" scoped>
	::v-deep .van-cell {
		width: auto !important;
	}
	.header {
		width: 100%;
		position: fixed;
		height: 0.5rem;
		top: 0;
		left: 0;
		z-index: 999;
		padding: 0.12rem;
		background-color: #1B167A;
		.t {
			position: absolute;
			left: 50%;
			transform: translateX(-50%);
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			font-size: 0.16rem;
			color: #FFFFFF;
		}
	}

	.pages {
		width: 100%;
		padding: 0.8rem 0 0;
		min-height: 100vh;
		background: #18191B;
		.logo {
			.t {
				margin-top: 0.1rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 0.21rem;
				color: #FFFFFF;
			}
		}
		.point1{
			position: absolute;
			left: 0.15rem;
			top: 0.45rem;
			width: 0.5rem;
			height: 0.5rem;
			opacity: 0.8;
			border-radius: 50%;
			background-image: radial-gradient(circle, rgba(152, 239, 134, 1) 0%,rgba(152, 239, 134, 0.8) 20%, transparent 50%);
		}
		.point2{
			position: absolute;
			right: -1.2rem;
			top: -1.2rem;
			width: 3rem;
			height: 3rem;
			opacity: 0.5;
			border-radius: 50%;
			// box-shadow: 0 0 20px 0px #98EF86, 0 0 20px 0px #98EF86, 0 0 20px 20px #98EF86, 0 0 20px #98EF86;
			background-image: radial-gradient(circle, rgba(152, 239, 134, 0.8) 0%,rgba(152, 239, 134, 0.3) 30%, transparent 50%);
		}
	}
	.info {
		width: 100%;
		height: 0.35rem;
		img {
			width: 0.9rem;
			height: 0.9rem;
		}
		span {
			padding-left: 0.2rem;
			font-family: PangMenZhengDaoBiaoTiTiMianFeiBan,
				PangMenZhengDaoBiaoTiTiMianFeiBan;
			font-weight: normal;
			font-size: 0.6rem;
			color: #ffffff;
		}
	}

	.slogo {
		width: 60%;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
		margin-top: 0.1rem;

		.icon {
			margin-right: 10px;
		}

		.txt {
			line-height: 0.3rem;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 0.15rem;
			color: #dc183b;
		}
	}

	.boxs {
		margin: 0.14rem;
		.tit{
			padding: 0 0.1rem;
			margin-bottom: 0.6rem;
			font-family: PingFang SC;
			font-weight: 600;
			font-size: 0.15rem;
			color: #FFFFFF;
		}
		.tabs {
			width: 100%;
			height: 0.45rem;

			.tabs-i {
				padding-bottom: 0.2rem;
				width: 50%;
				text-align: center;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 0.2rem;
				color: #666666;

				&.active {
					color: #ffffff;
					border-bottom: 0.04rem solid #c94d5b;
				}
			}
		}

		.inputs {
			margin: 0.25rem 0 0;
			.tt {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 0.15rem;
				color: #181818;
			}
			.inputs-i {
				margin: 0.1rem 0 0.2rem;
				height: 0.51rem;
				background: #232429;
				border-radius: 0.26rem;
				padding: 0 0.12rem;
				position: relative;
				.t{
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 0.14rem;
					color: #8EFE99;
				}
				.van-field {
					flex: 1;
					padding: 0 0.1rem;
					line-height: 0.48rem;
					background: transparent;
					::v-deep input {
						line-height: 0.46rem;
						font-family: PingFang SC;
						font-weight: 400;
						font-size: 0.14rem;
						color: #fff;
					}
				}

				img {
					width: 0.17rem;
					height: 0.09rem;
				}

				.show {
					position: absolute;
					right: 0.16rem;
					top: 55%;
					transform: translateY(-50%);
				}
			}
		}
		.btns {
			margin: 0.25rem 0 0;
			.register {
				margin-top: 0.16rem;
				font-family: PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
				color: #666666;
				span{
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 0.13rem;
					color: #fff;
				}
			}

			.login {
				height: 0.46rem;
				background: linear-gradient(90deg, #98EF86, #C7F377);
				border-radius: 0.23rem;
				font-family: PingFang SC;
				font-weight: 500;
				font-size: 0.15rem;
				color: #000000;
				line-height: 0.46rem;
			}
		}

		.van-button {
			margin-top: 0.2rem;
			height: 0.025rem;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 2.125rem;
			color: #ffffff;
			border-radius: 6px;
		}
	}
</style>