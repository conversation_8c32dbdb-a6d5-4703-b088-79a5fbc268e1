<template>
	<div class="page">
		<top-back title="申購記錄"></top-back>
		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" style="min-height: 100vh;">
			<!-- <div class="top">
				<div class="top-item" v-for="(item, index) in stockList" :key="index"
					:class="stockIndex===item.type?'top-on':''" @click="changeStock(item.type)">
					{{item.name}}<span></span>
				</div>
			</div> -->
			<template v-if="list.length>0">
				<div class="cot">
					<div class="item" v-for="(item, i) in list" :key="i">
						<div class="item-top flex flex-b">
							<div class="">
								<div class="name">{{ item.stock_name || "-" }}</div>
								<div class="code">{{ item.stock_code || "-" }}</div>
							</div>
							<div class="item-list">
								<div class="btn2">
									<!-- {{ item.rate }}% -->
									{{ item.status == 0 ? $t('待中籤') : item.status == 1 ? $t('已中籤') : item.status == 2 ? $t('認繳成功') : item.status == 3 ? $t('認繳失敗') : $t('未中籤')}}
								</div>
							</div>
						</div>
						<div class="item-middle flex flex-b flex-wrap">
							<div class="item-list flex flex-b">
								<div class="t2">申購日期</div>
								<div class="t3">
									{{ timestampToTime2(item.buy_time) }}
								</div>
							</div>
							<div class="item-list flex flex-b">
								<div class="t2 ">價格</div>
								<div class="t3">{{ parseFloat(item.apply_price).toFixed(2) }}</div>
							</div>

							<div class="item-list flex flex-b">
								<div class="t2 ">數量</div>
								<div class="t3">{{ parseFloat(item.apply_total/1000) }}張</div>
							</div>

							<div class="item-list flex flex-b"
								v-if="item.status == 1 || item.status == 2 || item.status == 3">
								<div class="t2 ">已認繳</div>
								<div class="t3 ">{{ $formatMoney(item.rjmoney, 0) }}</div>
							</div>

							<div class="item-list flex flex-b"
								v-if="item.status == 1 || item.status == 2 || item.status == 3">
								<div class="t2 ">需認繳</div>
								<div class="t3 ">{{ $formatMoney(item.subs_value, 0) }}</div>
							</div>
							<!-- <div class="item-list flex flex-b">
								<div class="t2 ">申購期間</div>
								<div class="t3 ">
									{{ item.subdate }}
								</div>
							</div>
							<div class="item-list flex flex-b">
								<div class="t2 ">截止日</div>
								<div class="t3 ">
									{{ item.endTime }}
								</div>
							</div>
							<div class="item-list flex flex-b">
								<div class="t3">撥券日</div>
								<div class="t2">{{ item.amtdate }}</div>
							</div> -->
						</div>
					</div>
				</div>
			</template>
			<no-data v-else></no-data>
		</van-pull-refresh>
	</div>
</template>

<script>
	import {
		Toast
	} from "vant";
	import qs from "qs";

	export default {
		data() {
			return {
				all: this.$t('all'),
				lang: this.$t('mine')['newShareOrder'],
				stock: this.$t('stock'),
				stockList: [],
				stockIndex: 'meigu',
				sortList: [],
				sortIndex: 0,
				list: [
					// {
					// 	stock_name: 'stock_name',
					// 	stock_code: 'stock_code',
					// 	status: 0,
					// 	buy_time: 1365656,
					// 	apply_price: 1365656,
					// 	apply_total: 1365656,
					// 	rjmoney: 1365656,
					// 	subs_value: 1365656,
					// },
				],
				isLoading: false
			};
		},
		computed: {
			timestampToTime(timestamp) {
				return (timestamp) => {
					let date = new Date(timestamp);
					let Year = date.getFullYear();
					let Moth =
						date.getMonth() + 1 < 10 ?
						"0" + (date.getMonth() + 1) :
						date.getMonth() + 1;
					let Day = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
					let GMT =
						Year +
						"/" +
						Moth +
						"/" +
						Day;
					return GMT;
				};
			},
			timestampToTime2(timestamp) {
				return (timestamp) => {
					let date = new Date(timestamp * 1000);
					let Year = date.getFullYear();
					let Moth =
						date.getMonth() + 1 < 10 ?
						"0" + (date.getMonth() + 1) :
						date.getMonth() + 1;
					let Day = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
					let GMT =
						Year +
						"/" +
						Moth +
						"/" +
						Day;
					return GMT;
				};
			},
		},
		mounted() {
			let _this = this
			if (this.$route.query.currency) {
				_this.stockIndex = this.$route.query.currency
			}
			// _this.stockList = [
			//   { name: _this.stock.j2, type: 'meigu'},
			//   { name: _this.stock.j3, type: 'yinguo'}
			// ]
			_this.getList()
		},
		methods: {
			onRefresh() {
				this.getList()
			},
			changeStock(type) {
				let _this = this
				_this.stockIndex = type
				_this.getList()
			},
			getList() {
				let _this = this
				Toast.loading({
					duration: 0,
					forbidClick: true,
					message: '載入中'
				})
				let parameter = qs.stringify({
					type: 'zar',
					buy_type: 0
				});
				this.$server.post("/trade/usernewstocklist", parameter).then((str) => {
					if (str) {
						if (parseInt(str.status) === 1) {
							_this.list = str.data
							_this.isLoading = false
							Toast.clear()
						} else {
							Toast({
								message: str.msg,
								duration: 2000,
							});
						}
					}
				})
			},
		}
	}
</script>

<style scoped lang="less">
	.page {
		padding: 0.5rem 0 0;
	}

	.cot {
		padding: 0.12rem;

		.item {
			background: #333237;
			box-shadow: 0rem 0rem 0.09rem 0rem rgba(0,0,0,0.5);
			border-radius: 0.04rem;
			border: 0.01rem solid #3B3E48;
			margin-bottom: 0.15rem;
			padding: 0.12rem;

			.red {
				color: #cf2829;
			}

			.t3 {
				font-size: 0.12rem;
			}

			.item-top {
				padding-bottom: 0.12rem;
				border-bottom: 0.01rem solid #020305;
				.name {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.14rem;
					color: #FFFFFF;
				}
				
				.code {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.11rem;
					color: #999999;
					margin-top: 0.05rem;
				}
				.btn2 {
					height: 0.28rem;
					padding: 0 0.1rem;
					background: #E5C79F;
					border-radius: 0.04rem;
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.13rem;
					color: #3C2500;
					line-height: 0.28rem;
				}

				.t3 {
					font-size: 0.16rem;
				}

				.t4 {
					font-size: 0.12rem;
					color: #fff;
					margin-top: 0.05rem;
				}
			}

			.item-middle {
				padding: 0.1rem 0 0;
				.item-list {
					width: 46%;
					display: flex;
					align-items: center;
					line-height: 0.26rem;
			
					.t2 {
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 0.12rem;
						color: #B6B2AD;
					}
			
					.t3 {
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 0.12rem;
						color: #FFF8EE;
					}
				}
			}
		}
	}
</style>