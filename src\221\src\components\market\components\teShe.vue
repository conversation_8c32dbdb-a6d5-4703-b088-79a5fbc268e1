<template>
  <div class="">
    <van-skeleton title :row="20" :loading="loading">
      <div class="list">
        <div class="tt">
          <div class="flex flex-b titles">
            <div class="flex-1">{{ $t("newt").t57 }}</div>
            <div class="flex-1 t-r">{{ $t("newt").t58 }}</div>
            <div class="flex-1 t-r">{{ $t("newt").t59 }}</div>
          </div>

          <div class="cot">
            <div class="flex flex-b tab">
              <div
                class="tab-item"
                :class="{ active: type == item.type }"
                v-for="(item, i) in typeList"
                :key="i"
                @click="changeType(item.type)"
              >
                {{ item.name }}
              </div>
            </div>
          </div>
        </div>

        <div class="lists">
          <div
            class="list-item flex flex-b"
            v-for="(item, idx) in list"
            :key="idx"
            @click="$toDetail(`/market/stockDetail?symbol=${item.code}`, item)"
          >
            <div class="flex flex-1">
              <div class="icon wsc"></div>
              <div>
                <div class="name">
                  {{ item.ko_name }}
                </div>
                <div class="code">
                  {{ item.code }}
                </div>
              </div>
            </div>
            <div class="price flex flex-e flex-1">
              {{ $formatMoney(item.close) }}
              <div
                class="icon down animate__animated animate__fadeIn"
                :class="{
                  up: item.returns > 0,
                }"
              ></div>
            </div>
            <div
              class="flex flex-1 flex-e per red"
              :class="{
                green: item.returns < 0,
              }"
            >
              {{ item.returns > 0 ? "+" : "" }}{{ item.returns.toFixed(2) }}%
            </div>
          </div>
        </div>
      </div>
    </van-skeleton>
    <loading ref="loading" />
  </div>
</template>

<script>
export default {
  name: "teShe",
  components: {},
  data() {
    return {
      loading: true,
      type: "returns_top",
      typeList: [
        {
          name: this.$t("newt").t60,
          type: "returns_top",
        },
        {
          name: this.$t("newt").t61,
          type: "returns_bottom",
        },
        {
          name: this.$t("newt").t62,
          type: "new_high_price",
        },
        {
          name: this.$t("newt").t63,
          type: "new_low_price",
        },
        // {
        //   name: '上限',
        //   type: 'upper_limit_price'
        // },
        // {
        //   name: '下限',
        //   type: 'lower_limit_price'
        // },
        // {
        //   name: '突破上限',
        //   type: 'out_of_upper_limit_price'
        // },
        // {
        //   name: '突破下限',
        //   type: 'out_of_lower_limit_price'
        // }
      ],
      list: [],
    };
  },
  created() {},
  mounted() {
    this.getInfo();
  },
  onLoad() {},
  methods: {
    changeType(type) {
      this.type = type;
      this.getInfo();
    },
    getInfo() {
      this.$refs.loading.open(); //开启加载
      this.$server.post("/transaction/tsxm", { type: this.type }).then((res) => {
        this.$refs.loading && this.$refs.loading.close();
        this.loading = false;
        if (res.status == 1) {
          let arr = [];
          res.data.data.forEach((item, i) => {
            if (item.returns) {
              item.returns = item.returns;
            }
            if (item.week_returns) {
              item.returns = item.week_returns;
            }
            if (item.month_returns) {
              item.returns = item.month_returns;
            }
            if (item.three_month_returns) {
              item.returns = item.three_month_returns;
            }
            if (item.six_month_returns) {
              item.returns = item.six_month_returns;
            }

            //只展示前面5个
            // if (i <= 4) {
            //   arr.push(item);
            // }
            arr.push(item);
          });

          this.list = arr;
        }
      });
    },
  },
};
</script>

<style scoped lang="less">
.titles {
  padding: 0.1rem;
  border-bottom: 0.01rem solid #f5f5f5;
  div {
    font-size: 0.12rem;
    color: #757575;
  }
}
::v-deep .van-skeleton__row{
  background-color:transparent !important;
}
::v-deep .van-skeleton__title{
  background-color:transparent !important;
}
.lists {
  .list-item {
    padding: 0.1rem;
    // border-bottom: 0.02rem solid #ececec;
    border-bottom: 0.01rem solid #f5f5f5;
    .name {
      font-weight: bold;
      margin-left: 0.05rem;
    }
    .code {
      font-weight: 500;
      font-size: 0.12rem;
      color: #464646;
      margin-left: 0.05rem;
    }
    .price {
      font-weight: bold;
      // font-size: 0.18rem;
      .icon {
        margin-left: 0.05rem;
      }
    }
    .per {
      font-weight: bold;
      // font-size: 0.12rem;
    }
  }
}

.red {
  color: #c04649;
}
.green {
  color: #4f8672;
}

.list {
  .tt {
    .cot {
      padding: 0.1rem;
      .tab {
        .tab-item {
          width: 23%;
          background: #ededed;
          border-radius: 0.04rem;
          font-size: 0.12rem;
          color: #3b3b3b;
          padding: 0.05rem 0.1rem;
          text-align: center;
          &.active {
            background: #6970af;
            color: #ffffff;
          }
        }
      }
    }
  }
}
</style>
