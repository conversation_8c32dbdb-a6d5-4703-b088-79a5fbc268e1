 <template>
 	<div class="WithCard">
 		<div class="headh">
 			<top :title='$t(text)'></top>
 		</div>

 		<div class="kapian" v-for="(item,idx) in cardlist" :key='idx'>
 			<div class="anliuk flex flex-b">
 				<a class="van-tab__text--ellipsis" @click="xiugai(item)" v-if="false">
 					<span>{{$t('点击修改')}}</span>
 				</a>
 				<p class="title">{{item.bank_name}}</p>
 				<a class="sanchu van-tab__text--ellipsis" @click="sanchu(item)">
 					<img src="../../assets/v2/delete.png" />
 				</a>
 			</div>
 			<div class="kaxia">
 				<div class="retext">
 					<!-- <h6>{{item.bank_address}}</h6> -->
 					<p>{{formatNum(item.bank_num)}}</p>
 				</div>
 			</div>
 			<!-- <div class="user">
 				<div>{{item.realname}}</div>
 			</div> -->
 		</div>

 		<div class="btn-big flex flex-c" @click="addbark">
 			{{$t('bankManagement').btn1}}
 		</div>
 	</div>
 </template>
 <script type="text/javascript">
 	import Vue from 'vue';
 	import qs from 'qs';
 	import axios from 'axios';
 	import top from '../bar/toper.vue'
 	import {
 		Dialog
 	} from 'vant';
 	Vue.use(Dialog);
 	import {
 		Toast
 	} from 'vant';
 	Vue.use(Toast);
 	export default {
 		name: "WithCard",
 		data() {
 			return {
 				text: this.$t('bankManagement').title,
 				cardlist: []
 			}
 		},
 		components: {
 			top
 		},
 		computed: {
 			formatNum() {
 				return (value) => {
 					let str = value.slice(0, 4);
 					return `${str} **** **** ****`;
 				}
 			}
 		},
 		methods: {
 			xiugai(e) {
 				this.$router.push({
 					path: '/user/BankCard',
 					query: {
 						num: JSON.stringify(e)
 					}
 				})
 			},
 			addbark() {
 				this.$router.push({
 					path: '/user/BankCard'
 				})
 			},
 			sanchu(e) {
 				Dialog.confirm({
 					title: this.$t("bankManagement").txt1,
 					message: this.$t("bankManagement").txt2,
 					confirmButtonText: this.$t("bankManagement").txt3,
 					cancelButtonText: this.$t("bankManagement").txt4,
 				}).then(() => {
 					var datas = qs.stringify({
 						bankid: e.id,
            type: 'jpy'
 					});
 					this.$server.post('/user/delbakcard', datas).then(str => {
 						if (str.data.status == 1) {
 							Toast({
 								message: this.$t('bankManagement').txt5,
 								duration: 2000
 							});

 							this.getcard();

 						} else {
 							Toast({
 								message: this.$t('bankManagement').txt6,
 								duration: 2000
 							});
 						}
 					})
 				})
 			},
 			getcard() {
 				this.$server.post('/user/cardList', {type: 'jpy'})
 					.then(str => {
 						if (str.data.status == 1) {
 							this.cardlist = str.data.data;
 						} else {
 							Toast({
 								message: this.$t(str.data.msg),
 								duration: 2000
 							});
 						}

 					})
 			},


 		},
 		destroyed() {

 		},
 		mounted() {
 			this.getcard();
 		},
 	}
 </script>
 <style type="text/css" lang="less" scoped="scoped">
 	.WithCard {
 		background: #0f161c;
 		min-height: 100vh;
 		padding-top: .55rem;

 		.headh {
 			height: .44rem;
 			width: 100%;
 			background: #fff;
 			position: fixed;
 			top: 0;
 			left: 0;
 			z-index: 888;
 		}

 		.kapian {
 			background: rgba(94, 213, 168, 0.06);
 			border-radius: 0.1rem;
 			border: 0.01rem solid rgba(255, 255, 255, 0.2);
 			margin: .15rem .13rem;
			padding: 0.15rem;
 			.title {
 				font-family: PingFang SC;
 				font-weight: 500;
 				font-size: 0.13rem;
 				color: #FFFFFF;
 			}

 			.anliuk {
 				a {
 					display: inline-block;

 					img {
 						width: .13rem;
 						height: .15rem;
 					}

 				}
 			}

 			.kaxia {
 				height: 40px;
				margin-top: 0.2rem;
 				.retext {
 					p {
 						font-family: PingFang SC;
 						font-weight: 500;
 						font-size: 0.21rem;
 						color: #FFFFFF;
 					}
 				}
 			}

 			.user {
 				color: #fff;
 				font-size: 0.12rem;
 			}
 		}

 		.btn-big {
 			margin: 0 .12rem;
 			margin-top: 0.2rem;
 		}
 	}
 </style>