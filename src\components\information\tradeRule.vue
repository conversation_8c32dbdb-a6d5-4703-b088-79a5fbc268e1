<template>
	<div class="page ">
		<top-back :title="$t('交易規則')"></top-back>

		<div class="list" v-if="this.info">
      <div class="item" v-html="info"></div>
    </div>

    <no-data v-if="!this.info"></no-data>
	</div>
</template>

<script>
	export default {
		name: "aboutUs",
		props: {},
		data() {
			return {
				info: "",
			};
		}, 
		components: {},
		methods: {
			getInfo() {
				this.$server
					.post("/common/wenben", {
						name: "交易规则",
						type: "zar",
					})
					.then((res) => {
						this.info = res.data.content;
					});
			},
		},
		created() {
			this.getInfo();
		},
		computed: {},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.6rem 0.1rem 0;
		min-height: 100vh;
	}

	.list {
		background: linear-gradient(180deg, #B2E56E, #EFFDB0);
		border-radius: 0.13rem;
		padding: 0.12rem;
		.title{
			margin: 0.1rem 0 0.2rem;
			font-family: PingFang SC;
			font-weight: 500;
			font-size: 0.15rem;
			color: #000000;
			text-align: center;
		}
		.item {
			background: rgba(255, 255, 255, 0.3);
			border-radius: 0.09rem;
			line-height: 0.2rem;
			padding: 0.12rem;
			margin-bottom: 0.1rem;
			font-family: PingFang SC;
			font-weight: 400;
			font-size: 0.12rem;
			color: #666666;
			.tit{
				font-family: PingFang SC;
				font-weight: 400;
				font-size: 0.14rem;
				color: #000000;
			}
		}
	}
</style>