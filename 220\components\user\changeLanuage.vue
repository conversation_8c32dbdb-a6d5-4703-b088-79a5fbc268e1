<template>
	<div class="change">
		<top :title="$t(text)"></top>
		<div class="langItem flex flex-b" v-for="(item,index) in actions" :key="index" @click="selectLang(index)">
			<div class="name">{{item.name}}</div>
			<div class="icon gx02" :class="langIndex==index?'wgx02':'gx02'"></div>
		</div>
	</div>
</template>

<script type="text/javascript">
	import top from '../bar/toper.vue'
	import {
		getLangName,
		getLang,
		setLang,
		langList
	} from "../../lib/local.js";
	export default{
		data(){
			return{
				text: this.$t('other').txt12,
				actions: [
          //   {
					// 	name: "中文繁體",
					// 	type: 'tw'
					// },
					{
						name: "English",
						type: 'en'
					},
					{
						name: "日本語",
						type: 'jp'
					},
				],
				lang: "",
				langIndex:'',
			}
		},
		components:{
			top
		},
		mounted() {
			this.lang = getLangName();
			this.langIndex=this.actions.findIndex(item=>{
				return this.lang==item.name
			})
		},
		methods:{
			selectLang(e) {
				this.langIndex=e
				if(e==0){
					setLang("tw");
				}else if(e==1){
					setLang("en");
				}else if(e==2){
					setLang("jp");
				}else{
					setLang("en");
				}
				location.reload();
			},
		}
	}
</script>

<style type="text/css" lang="less" scoped="scoped">
	.change{
		background:#0f161c;
		min-height: 100vh;
		.langItem{
			margin: 0 0.12rem;
			padding: 0.15rem 0;
			border-bottom: 0.01rem solid rgba(255, 255, 255, 0.16);
			.name{
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 0.13rem;
				color: #FFFFFF;
			}
			
		}
	}
</style>