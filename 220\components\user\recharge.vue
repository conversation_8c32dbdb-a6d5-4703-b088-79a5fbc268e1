<template>
  <div class="recharge">
    <div class="head">
      <top :title="$t(text)" :rech="true"></top>
    </div>
    <div class="flex-column-item">
      <img
        src="../../assets/v2/czBg.png"
        style="width: 1.63rem;height: 1.5rem;margin: 0.1rem 0;"
        alt=""
      />
    </div>
    <div class="box">
      <div class="jine">
        <div class="boxk">
          <div class="flex flex-b">
            <div class="cz">{{ $t("recharge").txt3 }}</div>
          </div>
          <input
            class="inpt"
            :placeholder="$t('recharge').txt5"
            v-model="number"
            @input="changeInput"
          />

          <p class="cz">{{ $t("recharge").txt2 }}</p>
          <div class="xuank">
            <a :class="{ xl: cnum == 1 }" @click="choose(10000, 1)">10,000</a>
            <a :class="{ xl: cnum == 2 }" @click="choose(50000, 2)">50,000</a>
            <a :class="{ xl: cnum == 3 }" @click="choose(100000, 3)">100,000</a>
            <a :class="{ xl: cnum == 4 }" @click="choose(200000, 4)">200,000</a>
            <a :class="{ xl: cnum == 5 }" @click="choose(500000, 5)">500,000</a>
            <a :class="{ xl: cnum == 6 }" @click="choose(1000000, 6)"
              >1,000,000</a
            >
            <a :class="{ xl: cnum == 7 }" @click="choose(3000000, 7)"
              >3,000,000</a
            >
            <a :class="{ xl: cnum == 8 }" @click="choose(5000000, 8)"
              >5,000,000</a
            >
            <a :class="{ xl: cnum == 9 }" @click="choose(10000000, 9)"
              >10,000,000</a
            >
          </div>
<!--          <p class="cz" style="margin-top:0.1rem;">{{ $t("other").txt11 }}</p>-->
<!--          <input-->
<!--            class="inpt"-->
<!--            :placeholder="$t('other').txt10"-->
<!--            v-model="password"-->
<!--            type="password"-->
<!--          />-->

          <div class="btn-big" @click="chongzhi">{{ $t("recharge").txt6 }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script type="text/javascript">
import Vue from "vue";
import qs from "qs";
import axios from "axios";
import top from "../bar/toper.vue";
import { Toast } from "vant";
Vue.use(Toast);

export default {
  name: "recharge",
  data() {
    return {
      text: this.$t("mine").money.btn1,
      number: "",
      password: "",
      cnum: -1,
      cnumd: 1,
      showk: false,
      clist: [],
      shuju: "",
      ruj: "",
      ruj2: "",
      userdata: "",
      title: "",
    };
  },
  components: {
    top,
  },
  methods: {
    changeInput(e) {
      if (this.$formatMoney(e.target.value.split(",").join(""))) {
        console.log(this.$formatMoney(e.target.value.split(",").join("")));
        this.number = this.$formatMoney(e.target.value.split(",").join(""), 0);
      } else {
        this.number = 1;
      }
    },
    formatMoney(str) {
      let newStr = "";
      let strS = str.split(".");
      if (strS.length > 1) {
        for (let i = 0; i < strS[0].length; i++) {
          newStr += strS[0][i];
          if (
            (strS[0].length - (i + 1)) % 3 === 0 &&
            strS[0].length - (i + 1) !== 0
          ) {
            newStr += ",";
          }
        }
        newStr = newStr + "." + strS[1];
      } else {
        for (let i = 0; i < str.length; i++) {
          newStr += str[i];
          if ((str.length - (i + 1)) % 3 === 0 && str.length - (i + 1) !== 0) {
            newStr += ",";
          }
        }
      }
      return newStr;
    },
    choose(e, k) {
      this.cnum = k;
      this.number = e;
    },
    closepop(e) {
      this.showk = false;
      if (e) {
        this.tijiao();
      }
    },
    getUser() {
      this.$server.post("/user/getUserinfo").then((str) => {
        if (str.data.status == 1) {
          this.userdata = str.data.data;
          window.localStorage.setItem(
            "userdata",
            JSON.stringify(str.data.data)
          );
        } else {
          Toast({
            message: this.$t(str.data.msg),
            duration: 2000,
          });
        }
      });
    },
    // /transaction/config
    peiz() {
      this.$server.post("/common/config", {type: 'jpy'}).then((str) => {
        if (str.data.status == 1) {
          str.data.data.forEach((item) => {
            if (item.name == "minrecharge") {
              this.ruj = item;
            }
            if (item.name == "maxrecharge") {
              this.ruj2 = item;
            }
            if (item.name == "title") {
              this.title = item.value;
            }
          });
        } else {
          Toast({
            message: this.$t(str.data.msg),
            duration: 2000,
          });
        }
      });
    },
    bianhuan(e) {
      this.shuju = this.clist[e];
    },
    choosed(e) {
      this.cnumd = e;
    },
    chongzhi() {
      let _this = this;
      var datas = qs.stringify({
        money: this.number, //.split(',').join(''),
        // rpassword: this.password,
        type: "jpy",
      });
      this.$server.post("/user/ischongzhi", datas).then((str) => {
        if (str.data.status == 1) {
          Toast({
            message: this.$t("充值提交成功"),
            duration: 2000,
          });
          setTimeout(function() {
            _this.$router.push({
              path: "/user/rechargeChannel?money=" + _this.number,
            });
          }, 2000);
        } else {
          if (str.data.msg.indexOf("请于入金时间") != -1) {
            Toast({
              message: this.$t("请于入金时间：") + str.data.msg.substr(7, 16),
              duration: 2000,
            });
          } else {
            if (str.data.msg.indexOf("入金最小值") != -1) {
              Toast({
                message:
                  this.$t("入金最小值") +
                  this.$formatMoney(parseFloat(this.ruj.value).toFixed(0)),
                duration: 2000,
              });
            } else {
              if (str.data.msg.indexOf("入金最大值") != -1) {
                Toast({
                  message:
                    this.$t("入金最大值") +
                    this.$formatMoney(parseFloat(this.ruj2.value).toFixed(0)),
                  duration: 2000,
                });
              } else {
                Toast({
                  message: this.$t(str.data.msg),
                  duration: 2000,
                });
              }
            }
          }
        }
      });
    },
    tongdao() {
      this.$server.post("/common/recharge_channel").then((str) => {
        if (str.data.status == 1) {
          this.clist = str.data.data;
          this.shuju = str.data.data[1];
          this.showk = true;
        } else {
          Toast({
            message: this.$t(str.data.msg),
            duration: 2000,
          });
        }
      });
    },
    tijiao() {
      var datas = qs.stringify({
        money: this.number,
      });
      this.$server.post("/user/chongzhi", datas).then((str) => {
        Toast({
          message: this.$t(str.data.msg),
          duration: 2000,
        });
      });
    },
  },
  destroyed() {},
  mounted() {
    this.peiz();
    this.getUser();
  },
};
</script>
<style type="text/css" lang="less" scoped="scoped">
input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  color: #666;
  font-size: 0.14rem;
}

.recharge {
  background: #0f161c;
  min-height: 105vh;
  .box {
    margin: 0.12rem;
  }
  .money {
    background: url("../../assets/skin/mine/img1.png") center no-repeat;
    background-size: 100%;
    width: 100%;
    height: 1rem;
    border-radius: 0.1rem;

    h6 {
      font-weight: 500;
      font-size: 0.24rem;
      color: #fff;
      margin-right: 0.1rem;
    }

    p {
      color: #fff;
      font-size: 0.15rem;
      margin-left: 0.1rem;
    }
  }

  .jine {
    min-height: 0.8rem;
    padding-top: 0.2rem;
    padding-bottom: 0.02rem;
    margin-top: 0.1rem;

    .boxk {
      .cz {
        font-weight: 400;
        font-size: 0.12rem;
        color: #ffffff;
      }

      .min {
        font-weight: 400;
        font-size: 0.12rem;
        color: #fe0000;
        text-align: right;
      }

      .inpt {
        width: 100%;
        height: 0.46rem;
        background: #000000;
        border-radius: 0.1rem;
        margin: 0.1rem 0;
        padding-left: 0.1rem;
        font-weight: 400;
        font-size: 0.14rem;
        color: #fff;
      }

      .xuank {
        display: flex;
        justify-content: space-between;
        margin-top: 0.1rem;
        flex-wrap: wrap;

        a {
          width: 23%;
          height: 0.29rem;
          background: rgba(94, 213, 168, 0.06);
          border-radius: 0.05rem;
          border: 0.01rem solid rgba(255, 255, 255, 0.2);
          font-weight: 500;
          font-size: 0.11rem;
          color: #b3b8b8;
          text-align: center;
          line-height: 0.29rem;
          margin-bottom: 0.1rem;

          &.xl {
            background: #5ed5a8;
            color: #000;
          }
        }
      }

      .btn-big {
        margin: 0.2rem 0;
      }
    }
  }
}
</style>
