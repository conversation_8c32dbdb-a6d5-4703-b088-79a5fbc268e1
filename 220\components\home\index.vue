<template>
	<div class="home">
		<topCom></topCom>
		<div class="imggg" @click="showG=false" v-if="showG">
			<div class="icon close02"></div>
		</div>
		<div class="tit">{{$t('other').txt19}}</div>
		<div class="bot">
			<div class="bot-box flex flex-b">
				<div class="bot-item" v-for="(item, index) in hotList" :key="index" v-if="index<3"
					@click="clickNext('/market/marketDetail?symbol='+ item.symbol.replace('TSE:',''))">
					<div class="bot-title">{{ item.local_name }}</div>
					<div class="" :class="item.gainValue >= 0 ? 'red' : 'green'">
						<div class="bot-price">{{ $formatMoney(item.price) }}円</div>
						<div class="flex">
							<div class="">{{item.gainValue >= 0?'+':""}}{{ $formatMoney(item.gainValue) }}円
							</div>
							<div>({{item.gainValue >= 0?'+':""}}{{ $formatMoney(item.gain) }}%)</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="hot">
			<div class="tit flex flex-b">
				<div>{{ $t('home').txt4 }}</div>
				<div class="more flex" @click="clickNext('/market/market')">
					{{ $t('home').txt5 }}
					<img src="../../assets/skin/home/<USER>" />
				</div>
			</div>
			<div class="item">
				<div class="item-href" v-for="(item, index) in hotList" :key="index" v-if="index >2 && index < 8"
					@click="clickNext('/market/marketDetail?symbol='+ item.symbol.replace('TSE:',''))">
					<div class="item-href-name">
						<!-- <div class="order">{{Number(index)+1}}</div> -->
						{{ item.local_name }}
						<div class="code">{{ item.symbol }}</div>
					</div>
					<div class="item-href-middle flex-1">
						<img src="../../assets/v2/up.png" v-if="item.gain > 0" />
						<img src="../../assets/v2/down.png" v-else />
						{{ $formatMoney(item.price) }}円
					</div>
					<div class="item-href-foot flex-1" :class="item.gain > 0 ? 'red' : 'green'">
						<div class="txt">{{ $formatMoney(item.gainValue) }}円</div>
						<span :class="item.gain > 0 ? 'red' : 'green'">{{ $formatMoney(item.gain) }}%</span>
					</div>
				</div>
			</div>
		</div>

		<div class="news">
			<div class="tit flex flex-b">
				<div>{{ $t('home').txt6 }}</div>
				<div class="more flex" @click="clickNext('/home/<USER>')">
					{{ $t('home').txt5 }}
					<img src="../../assets/skin/home/<USER>" />
				</div>
			</div>
			<div class="newsList">
				<div class="item" v-for="(item, index) in newsList" :key="index" v-if="index<5" @click="jumpToNewsDetail(item)">
					<span class="time">{{$formatDate('YYYY/MM/DD',item.created*1000)}}</span>
					<div class="txt">{{ item.title.split('u3000').join('') }}</div>
				</div>
			</div>
		</div>

		<!-- 底部菜单 -->
		<bottomnav :on='0'></bottomnav>
	</div>
</template>
<script type="text/javascript">
	import Vue from "vue";
	import {
		Toast,
		Popup,
		Icon,
		Dialog
	} from "vant";
	Vue.use(Popup).use(Toast).use(Icon).use(Dialog);
	import bottomnav from "../bar/bottomnav.vue";
	import topCom from '../bar/topCom.vue'
	export default {
		name: "home",
		data() {
			return {
				text: "VIP",
				userdata: {},
				infoList: [],
				top: [
            {
						name: this.$t('home').top2, // 新股申购
						img: 'ico1',
						href: '/market/subscribe',
						sWidth: '.35rem',
						isShow: true
					},
					{
						name: this.$t('home').top9, //日内交易
						img: 'ico2',
						href: '/common/dividend?type=0',
						sWidth: '.34rem',
						isShow: true
					},
					{
						name: this.$t('home').top14, //大宗交易
						img: 'ico3',
						href: '/common/dividend?type=1',
						sWidth: '.34rem',
						isShow: true
					},
					{
						name: this.$t('home').top3, //跟单
						img: 'ico4',
						href: '/gdindex/gdindex',
						sWidth: '.28rem',
						isShow: true
					},
					{
						name: this.$t('home').top10, // 盘前交易
						img: 'ico5',
						href: '/common/dividend2?type=3',
						sWidth: '.34rem',
						isShow: true
					},
					{
						name: this.$t('home').top11, //盘后交易
						img: 'ico6',
						href: '/common/dividend2?type=4',
						sWidth: '.35rem',
						isShow: true
					},
					{
						name: this.$t('home').top12, //抢筹
						img: 'ico7',
						href: '/common/dividend3',
						sWidth: '.33rem',
						isShow: true
					},
					{
						name: this.$t('home').top13, //客服
						img: 'ico8',
						href: 'kefu',
						sWidth: '.33rem',
						isShow: true
					}
				],
				hotList: [],
				yuanList: [],
				newsList: [],
				showG: true,
			};
		},
		beforeDestroy() {

		},
		created() {},
		components: {
			bottomnav,
			topCom
		},
		methods: {
			getHot() {
				this.$server.post('/parameter/top', {
					type: 'jpy'
				}).then(res => {
          var arr = [];
          for (var i in res.data.data) {
            var row = res.data.data[i];
            if (row.gain !== null) {
              if (Math.round(Math.random() * 999) < 100) {
                row.one = 100;
              } else {
                row.one = Math.round(Math.random() * 999);
              }
              if (Math.round(Math.random() * 999) < 100) {
                row.two = 100;
              } else {
                row.two = Math.round(Math.random() * 999);
              }
              if (Math.round(Math.random() * 999) < 100) {
                row.three = 100;
              } else {
                row.three = Math.round(Math.random() * 999);
              }
            }
            arr.push(row)
          }
          this.hotList = arr;
				});
			},
			getNews() {
				this.$server.post('/common/newss', {
          exchange:'jp',
          lang:'jp'
        }).then(ras => {
					this.newsList = ras.data.data.result
				});
			},
			goUrl(item) {
				if (item.href === 'kefu') {
					this.$server.post('/common/config', {type:'all'}).then(res => {
						// console.log(res)
						if (parseInt(res.status) === 200) {
							let list = res.data.data;
							let listLength = list.length;
							let a;
							for (a = 0; a < listLength; a++) {
								if (list[a].name === 'kefu') {
									this.openInBrowser(list[a].value);
								}
							}
						}
					});
				} else {
					this.$router.push({
						path: item.href
					});
				}
			},
			jumpToNewsDetail(obj) {
				window.localStorage.setItem("newsDetail", JSON.stringify(obj))
				this.$router.push({
					path: "/home/<USER>",
				});
			},
			getUser() {
				this.$server.post("/user/getUserinfo").then((str) => {
					if (str.data.status == 1) {
						this.userdata = str.data.data;
					} else {
						Toast({
							message: this.$t(str.data.msg),
							duration: 2000,
						});
					}
				});
			},
		},
		destroyed() {},
		mounted() {
			this.getUser();
			this.getHot();
			this.getNews();

		},
	};
</script>
<style type="text/css" lang="less" scoped="scoped">
	.imggg {
		position: fixed;
		bottom: 0.6rem;
		width: 100%;
		height: 0.7rem;
		background: url('../../assets/v2/xwbg.png') no-repeat center/100%;

		.close02 {
			margin-left: 3.5rem;
			margin-top: 0.1rem;
		}
	}

	.home {
		padding-bottom: 1.4rem;
		background: #0F161C;
		min-height: 100vh;

		.topBox {
			padding: 0 .12rem 0 .12rem;
			position: relative;
			background: linear-gradient(0deg, #3DC7C4, #3CC3BC);

			.user {
				padding: .15rem 0;

				.headImg {
					img {
						width: .38rem;
					}
				}

				.ico {
					img {
						width: .32rem;
					}
				}

				.search {
					height: .38rem;
					background: rgba(147, 237, 235, .75);
					border-radius: .19rem;
					font-size: .15rem;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					color: #C2F6F5;
					margin: 0 .18rem;
				}
			}

			.banner {
				position: relative;

				img {
					width: 100%;
				}

				.txt {
					position: absolute;
					top: 0;
					left: 0;
					width: 100%;
					height: 100%;

					h4 {
						margin-left: .16rem;
						margin-bottom: .1rem;
						font-weight: 400;
						font-size: .23rem;
						color: #FFFFFF;
						font-style: italic;
					}

					h5 {
						margin-left: .2rem;
						font-weight: bold;
						font-size: .14rem;
						color: #FFFFFF;
						font-style: italic;
					}
				}
			}
		}

		.navList {
			background: linear-gradient(180deg, #3DC7C4, #3CC3BC);
			border-radius: 0px 0px 15px 15px;

			.box {
				width: 100%;
				box-sizing: border-box;
				display: flex;
				flex-wrap: wrap;
				overflow: hidden;
				padding-bottom: .1rem;

				.top-item {
					width: .77rem;
					height: .76rem;
					background: #fff;
					border-radius: .16rem;
					border: .01rem solid #4ABCB6;
					box-sizing: border-box;
					border-radius: .18rem;
					display: flex;
					align-items: center;
					justify-content: center;
					flex-direction: column;
					font-size: .12rem;
					line-height: .12rem;
					font-family: FZLanTingHeiT-R-GB;
					font-weight: 400;
					color: #28B2AD;
					margin: .1rem 0 0 .14rem;

					.img {
						height: .35rem;
						margin-bottom: .06rem;

						img {
							object-fit: contain;
						}
					}

				}
			}
		}

		.tit {
			font-size: .16rem;
			padding: 0.05rem 0.12rem;
			background-color: rgba(94, 213, 168, 0.2);
			border-bottom: .03rem solid #fff;
			font-family: PingFangSC, PingFang SC;
			font-weight: 600;
			font-size: 0.14rem;
			color: #FFFFFF;

			.more {
				font-weight: normal;
				font-size: .12rem;
				color: #fff;

				img {
					width: .07rem;
					margin-right: 0;
					margin-left: .04rem;
				}
			}
		}

		.bot {
			margin: .16rem .12rem;

			.bot-box {
				width: 100%;
				white-space: nowrap;
				// overflow: scroll;
			}

			.bot-item {
				display: inline-block;
				width: 32%;
				background: #1B232A;
				border-radius: 0.1rem;
				padding: 0.1rem 0.05rem;

				.bot-title {
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.12rem;
					color: #FFFFFF;
					overflow: hidden;
					text-overflow: ellipsis;
          text-align: center;
				}

				.bot-price {
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.18rem;
					margin: .01rem 0;
					text-align: center;
				}

				.line {
					margin-top: 0.1rem;
					width: 0.93rem;
					height: 0.3rem;
					border: 0.01rem solid #FE0000;
				}

				.bot-list {
					// height: .16rem;
					width: 100%;
					display: flex;
					align-items: center;

					.tit {
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 0.12rem;
						color: #fff;
						margin-right: .07rem;
						flex: none;
					}

					.num {
						font-size: .09rem;
						font-family: FZLanTingHeiT-R-GB;
						font-weight: 400;
						color: #fff;
						margin-left: .09rem;
						flex: none;
					}

					.orange {
						height: .09rem;
						flex: 1;
						background: #FF7E77;
						border-radius: .04rem;
						overflow: hidden;
						position: relative;

						.in {
							background: #FE2B16;
							height: .09rem;
							position: absolute;
							left: 0;
							top: 0;
							z-index: 1;
							display: block;
						}

						.txt {
							width: 100%;
							height: .1rem;
							display: flex;
							align-items: center;
							justify-content: center;
							position: absolute;
							left: 0;
							top: 0;
							z-index: 2;
							font-size: .08rem;
							font-family: FZLanTingHeiT-R-GB;
							font-weight: 400;
							color: #ffffff;
						}
					}

					.green {
						height: .09rem;
						width: 100%;
						background: #7DEEA6;
						border-radius: .06rem;
						overflow: hidden;
						position: relative;

						.in {
							background: #0DCB5C;
							height: .09rem;
							position: absolute;
							left: 0;
							top: 0;
							z-index: 1;
							display: block;
							color: #fff;
						}

						.txt {
							width: 100%;
							height: .1rem;
							display: flex;
							align-items: center;
							justify-content: center;
							position: absolute;
							left: 0;
							top: 0;
							z-index: 2;
							font-size: .08rem;
							font-family: FZLanTingHeiT-R-GB;
							font-weight: 400;
							color: #ffffff;
						}
					}

					.blue {
						height: .09rem;
						width: 100%;
						background: #ABCAFD;
						border-radius: .04rem;
						overflow: hidden;
						position: relative;

						.in {
							background: #5392F8;
							height: .09rem;
							position: absolute;
							left: 0;
							top: 0;
							z-index: 1;
							display: block;
						}

						.txt {
							width: 100%;
							height: .1rem;
							display: flex;
							align-items: center;
							justify-content: center;
							position: absolute;
							left: 0;
							top: 0;
							z-index: 2;
							font-size: .08rem;
							font-family: FZLanTingHeiT-R-GB;
							font-weight: 400;
							color: #ffffff;
						}
					}
				}
			}

		}

		.title {
			padding: .15rem 0;
			font-size: .18rem;
			font-weight: 500;
			color: #000;

			img {
				margin-right: .06rem;
			}

			.more {
				font-weight: normal;
				font-size: .12rem;
				color: #999;

				img {
					width: .07rem;
					margin-right: 0;
					margin-left: .04rem;
				}
			}
		}

		.recommend {
			margin: .16rem .12rem;

			.recomBox {
				padding-bottom: .1rem;

				.itemBox {
					padding: 0 .06rem 0 .04rem;
					overflow: scroll;
					white-space: nowrap;

					.item {
						background: #FFFFFF;
						box-shadow: 0 0 .04rem 0 rgba(0, 0, 0, 0.1);
						border-radius: .04rem;
						font-size: .11rem;
						margin: .05rem .08rem .05rem 0;
						padding: .04rem .05rem;
						width: 1.12rem;
						display: inline-block;

						.name {
							color: #6F7274;
							margin: 0.1rem 0;

							.txt {
								white-space: nowrap;
								text-overflow: ellipsis;
								width: 50%;
								overflow: hidden;
							}

							span {
								color: #666;
								background: #D2D1D1;
								border-radius: .08rem;
								padding: .03rem .05rem 0 .05rem;
							}
						}

						.price {
							color: #333333;

							.txt {
								font-weight: bold;
								font-size: .18rem;
								margin-bottom: 0.04rem;
							}

							span {
								display: inline-block;
								border-radius: .09rem;
								font-size: .14rem;
								padding: .02rem .05rem 0 .05rem;
								margin: .05rem 0;

								&.red {
									background: #FFBAAD;
									color: #DD1021;
								}

								&.green {
									background: #C5DBFF;
									color: #097FD7;
								}
							}
						}

						img {
							width: 100%;
						}
					}
				}
			}
		}

		.hot {
			padding-bottom: .1rem;

			.hotTitle {
				padding: .1rem .12rem 0 .12rem;
				font-weight: 500;
				font-size: .14rem;
				color: #333333;
			}

			.item {
				.item-href {
					display: flex;
					align-items: center;
					justify-content: space-between;
					border-bottom: .01rem solid rgba(255, 255, 255, 0.16);
					padding: 0.1rem 0.12rem;

					&:last-child {
						border-bottom: none;
					}

					.item-href-name {
						width: 30%;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
						font-family: PingFangSC, PingFang SC;
						font-weight: 500;
						font-size: 0.14rem;
						color: #FFFFFF;

						.order {
							font-weight: 400;
							font-size: .14rem;
							color: #818181;
							margin-right: .1rem;
						}

						.icon {
							padding: 0 .03rem;
							height: .13rem;
							background: #c62f1f;
							display: flex;
							align-items: center;
							justify-content: center;
							font-size: .09rem;
							font-family: PingFang SC;
							font-weight: bold;
							color: #000;
							margin-left: .05rem;
						}

						.code {
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							font-size: 0.13rem;
							color: #718A94;
							margin-top: .02rem;
						}
					}

					.item-href-middle {
						display: flex;
						align-items: center;
						justify-content: center;
						font-family: PingFangSC, PingFang SC;
						font-weight: 500;
						font-size: 0.18rem;
						color: #FFFFFF;

						img {
							margin-right: 0.05rem;
							width: .09rem;
							height: .09rem;
						}
					}

					.item-href-foot {
						display: flex;
						flex-direction: column;
						justify-content: flex-end;
						font-family: PingFangSC, PingFang SC;
						font-weight: 500;
						font-size: 0.16rem;
						color: #FFFFFF;
						text-align: center;

						span {
							font-family: PingFangSC, PingFang SC;
							font-weight: 500;
							font-size: 0.14rem;
						}
					}

					.line {
						width: 0.93rem;
						height: 0.3rem;
						border: 0.01rem solid #FE0000;
					}
				}

			}
		}

		.news {
			.newsList {
				font-size: .14rem;
				color: #333;
				font-weight: 500;

				.item {
					font-size: .14rem;
					color: #fff;
					border-bottom: .01rem solid rgba(255, 255, 255, 0.16);
					padding: 0.1rem 0.12rem;

					.ico {
						flex: none;
						width: .48rem;
						height: .45rem;
						display: flex;
						align-items: center;
						justify-content: space-between;

						&:after {
							content: '';
							display: block;
							width: .04rem;
							height: .04rem;
							background: #3CC3BC;
							border-radius: 50%;
							margin-right: -.025rem;
						}

						&.yellow {
							color: #F7BB5C;
						}

						span {
							padding-top: 0;
							width: .48rem;
							height: .25rem;
							line-height: .13rem;
						}
					}

					.txt {
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 0.16rem;
						color: #FFFFFF;
						line-height: 0.19rem;
						overflow: hidden;
						text-overflow: ellipsis;
						-webkit-line-clamp: 2;
						display: -webkit-box;
						-webkit-box-orient: vertical;
					}

					.time {
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 0.14rem;
						color: #718A94;
					}
				}
			}

			.news-list {
				height: .45rem;
				display: flex;
				align-items: center;
				padding: 0 .13rem;

				.news-list-type1 {
					width: .48rem;
					height: .25rem;
					background: rgba(255, 252, 240, 0.07);
					border-radius: .02rem;
					align-items: center;
					justify-content: center;
					font-size: .14rem;
					font-family: PingFang SC;
					font-weight: 500;
					color: rgba(219, 21, 21, 1);
					display: flex;
				}

				.news-list-type2 {
					width: .48rem;
					height: .25rem;
					background: rgba(255, 252, 240, 0.07);
					border-radius: .02rem;
					align-items: center;
					justify-content: center;
					font-size: .14rem;
					font-family: PingFang SC;
					font-weight: 500;
					color: #f7bb5c;
					display: flex;
				}

				.news-list-type3 {
					width: .48rem;
					height: .25rem;
					background: rgba(255, 252, 240, 0.07);
					border-radius: .02rem;
					align-items: center;
					justify-content: center;
					font-size: .14rem;
					font-family: PingFang SC;
					font-weight: 500;
					color: #f7bb5c;
					display: flex;
				}

				.news-list-type4 {
					width: .48rem;
					height: .25rem;
					background: rgba(255, 252, 240, 0.07);
					border-radius: .02rem;
					align-items: center;
					justify-content: center;
					font-size: .14rem;
					font-family: PingFang SC;
					font-weight: 500;
					color: #f7bb5c;
					display: flex;
				}

			}
		}

		.van-popup {
			overflow-y: initial !important;
		}


		input::-webkit-input-placeholder,
		textarea::-webkit-input-placeholder {
			color: #777;
			font-size: 0.14rem;
		}
	}
</style>