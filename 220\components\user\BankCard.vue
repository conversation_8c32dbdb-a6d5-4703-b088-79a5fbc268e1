 <template>
 	<div class="bankCard">
 		<div class="headh">
 			<top :title='$t(text)'></top>
 		</div>
		<div class="flex-column-item">
			<img src="../../assets/v2/jrkBg.png" style="width: 1.5rem;height: 1.5rem;margin: 0.1rem 0;" alt="" />
		</div>
 		<div class="bankInfo-box">
 			<!-- 填写 -->
			<div class="txt">{{ $t('bandBankCard').txt1 }}</div>
 			<div class="list">
 				<input :placeholder="$t('bandBankCard').txt2" v-model="realname" @input="inputEvent" />
 			</div>
			<div class="txt">{{ $t('bandBankCard').txt3 }}</div>
 			<div class="list flex">
 				<input :placeholder="$t('bandBankCard').txt4" v-model="bank_num" />
 			</div>
			<div class="txt">{{ $t('bandBankCard').txt5 }}</div>
 			<div class="list flex">
 				<input :placeholder="$t('bandBankCard').txt6" v-model="bank_name" />
 			</div>
			<div class="txt">{{ $t('bandBankCard').txt7 }}</div>
 			<div class="list flex">
 				<input :placeholder="$t('bandBankCard').txt8" v-model="bank_address" />
 			</div>
			<div class="txt">{{ $t('bandBankCard').txt9 }}</div>
 			<div class="list flex">
 				<input :placeholder="$t('bandBankCard').txt10" v-model="bank_code" />
 			</div>
 			<div class="btn-big" @click="xuanz">{{ $t('bandBankCard').txt11 }}</div>
 		</div>
 	</div>
 </template>
 <script type="text/javascript">
 	import Vue from 'vue';
 	import qs from 'qs';
 	import axios from 'axios';
 	import top from '../bar/toper.vue'
 	import {
 		Toast
 	} from 'vant';
 	Vue.use(Toast);
 	export default {
 		name: "BankCard",
 		data() {
 			return {
 				text: this.$t('bandBankCard').title,
 				num: '',
 				userdata: {},

 				realname: "",
 				bank_num: "",
 				bank_name: "",
 				bank_address: "",
 				bank_code: ""
 			}
 		},
 		components: {
 			top,
 		},
 		methods: {
 			inputEvent(e) {
 				let value = e.detail.value;
 				this.$nextTick(() => {
 					this.realname = value.replace(' ', '');
 				});
 			},
 			getUser() {
 				this.$server.post("/user/getUserinfo").then((str) => {
 					if (str.data.status == 1) {
 						this.userdata = str.data.data;
 						this.realname = this.userdata.realname;
 					} else {
 						Toast({
 							message: this.$t(str.data.msg),
 							duration: 2000,
 						});
 					}
 				});
 			},
 			xuanz() {
 				if (this.num) {
 					this.bianjik(); //编辑
 				} else {
 					this.enterk();
 				}
 			},
 			enterk() {
 				let _this = this
 				if (this.bank_num == '' || this.bank_name == '' || this.bank_code == '') {
 					Toast({
 						message: this.$t('bandBankCard').txt14,
 						duration: 2000
 					});
 					return false;
 				}
 				if (parseInt(this.userdata.is_true) === 0) {
 					Toast({
 						message: this.$t('bandBankCard').txt15,
 						duration: 2000
 					});
 					setTimeout(function() {
 						_this.$router.push({
 							path: "/user/Verified",
 						});
 					}, 2000)
 					return false
 				}

 				var datas = qs.stringify({
 					bank_num: this.bank_num,
 					bank_name: this.bank_name,
 					bank_address: this.bank_address,
 					realname: this.realname.replace(/\s+/g, ''),
          type: 'jpy'
 				});
 				this.$server.post('/user/addCard', datas).then(str => {
 					if (str.data.status == 1) {
 						Toast({
 							message: this.$t(str.data.msg),
 							duration: 2000
 						});
 						setTimeout(() => {
 							this.$router.go(-1);
 						}, 1500)
 					} else {
 						Toast({
 							message: this.$t(str.data.msg),
 							duration: 2000
 						});
 					}
 				})
 			},
 			bianjik() { //编辑
 				if (this.bank_num == '' || this.bank_name == '' || this.bank_code == '') {
 					Toast({
 						message: this.$t('bandBankCard').txt14,
 						duration: 2000
 					});
 					return false;
 				}

 				var datas = qs.stringify({
 					id: this.num.id,
 					bank_num: this.bank_num,
 					bank_name: this.bank_name,
 					bank_address: this.bank_address,
 					realname: this.realname.replace(/\s+/g, ''),
 				});
 				this.$server.post('/user/editCard', datas).then(str => {
 					if (str.data.status == 1) {
 						Toast({
 							message: this.$t(str.data.msg),
 							duration: 2000
 						});
 						setTimeout(() => {
 							this.$router.go(-1);
 						}, 1500)
 					} else {
 						Toast({
 							message: this.$t(str.data.msg),
 							duration: 2000
 						});
 					}
 				})
 			}
 		},
 		destroyed() {

 		},
 		mounted() {
 			this.getUser()
 			if (this.$route.query.num) {
 				this.num = JSON.parse(this.$route.query.num);
 				//this.text = '修改银行卡';
 				this.bank_name = this.num.bank_name;
 				this.bank_address = this.num.bank_address;
 				this.bank_num = this.num.bank_num
 				this.realname = this.num.realname
 			}

 		},
 	}
 </script>
 <style type="text/css" lang="less" scoped="scoped">
 	input::-webkit-input-placeholder,
 	textarea::-webkit-input-placeholder {
 		color: #999;
 		font-size: 0.16rem;
 	}

 	.bankCard {
 		background:#0f161c;
 		min-height: 100vh;
 		padding-top: .5rem;
		padding-bottom: 0.5rem;
 		.headh {
 			height: .44rem;
 			width: 100%;
 			background: #fff;
 			position: fixed;
 			top: 0;
 			left: 0;
 			z-index: 888;
 		}

 		.bankInfo-box {
 			margin: .12rem;
 		}
		.txt {
			margin-bottom: 0.1rem;
			font-family: FZLanTingHeiT-R-GB;
			font-weight: 400;
			font-size: 0.12rem;
			color: #FFFFFF;
		}
 		.list {
 			margin-bottom: .15rem;
			height: 0.46rem;
 			background: #000000;
 			border-radius: 0.1rem;
 			input {
 				width: 100%;
 				height: 0.46rem;
 				background: none;
 				padding: 0 0.1rem;
				font-weight: 400;
				font-size: 0.14rem;
				color: #999999;
 			}
 		}
		.btn-big{
			margin-top: 0.3rem;
		}
 	}
 </style>