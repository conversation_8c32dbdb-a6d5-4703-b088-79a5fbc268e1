 <template>
 	<div class="setPassword">
 		<div class="header">
 			<top :title='$t(text)'></top>
 		</div>

 		<div class="list">
 			<ul class="erty">
 				<div v-show="userdata.passwords">{{$t('loginPassword').txt2}}</div>
 				<li class="" v-show="userdata.passwords">
 					<input :placeholder="$t('loginPassword').txt3" v-model="oldpassword" type="password" />
 				</li>
 				<div>{{$t('loginPassword').txt4}}</div>
 				<li class="">
 					<input :placeholder="$t('loginPassword').txt5" v-model="password" type="password" />
 				</li>
 				<div>{{$t('loginPassword').txt44}}</div>
 				<li class="">
 					<input :placeholder="$t('loginPassword').txt6" v-model="passwords" type="password" />
 				</li>
 			</ul>
 			<div class="btn-big" @click="enterpassword">{{$t('loginPassword').btn1}}</div>
 		</div>
 	</div>
 </template>
 <script type="text/javascript">
 	import Vue from 'vue';
 	import qs from 'qs';
 	import axios from 'axios';
 	import {
 		Toast
 	} from 'vant';
 	Vue.use(Toast);
 	import top from '../bar/toper.vue'
 	export default {
 		name: "setPassword",
 		data() {
 			return {
 				text: this.$t('loginPassword').txt8,
 				password: '',
 				passwords: '',
 				oldpassword: "",
 				userdata: [],
 			}
 		},
 		components: {
 			top,
 		},
 		methods: {
 			changepassword() {
 				if (this.password == '' || this.passwords == '' || this.oldpassword == '') {
 					Toast({
 						message: this.$t('loginPassword').txt7,
 						duration: 2000
 					});
 					return false;
 				}

 				var datas = qs.stringify({
 					old_pass: this.oldpassword,
 					new_pass: this.passwords,
 					new_passs: this.password,
 				});
 				this.$server.post('/user/changePasswords', datas)
 					.then(str => {
 						if (str.data.status == 1) {
 							Toast({
 								message: this.$t(str.data.msg),
 								duration: 2000
 							});
 							setTimeout(() => {
 								this.$router.go(-1)
 							}, 1500)
 						} else {
 							Toast({
 								message: this.$t(str.data.msg),
 								duration: 2000
 							});
 						}

 					})
 			},
 			enterpassword() {
 				if (this.userdata.passwords) {
 					this.changepassword();
 					return false;
 				}
 				if (this.password == '' || this.passwords == '') {
 					Toast({
 						message: this.$t('loginPassword').txt7,
 						duration: 2000
 					});
 					return false;
 				}

 				var datas = qs.stringify({
 					new_pass: this.passwords,
 					new_passs: this.password,
 				});
 				this.$server.post('/user/setPasswords', datas)
 					.then(str => {
 						if (str.data.status == 1) {
 							Toast({
 								message: this.$t(str.data.msg),
 								duration: 2000
 							});
 							setTimeout(() => {
 								this.$router.go(-1)
 							}, 1500)

 						} else {
 							Toast({
 								message: this.$t(str.data.msg),
 								duration: 2000
 							});
 						}

 					})
 			},
 			getUser() {
 				this.$server.post('/user/getUserinfo')
 					.then(str => {
 						if (str.data.status == 1) {
 							this.userdata = str.data.data;
 							window.localStorage.setItem("userdata", JSON.stringify(str.data.data));
 						} else {
 							Toast({
 								message: this.$t(str.data.msg),
 								duration: 2000
 							});
 						}

 					})
 			},

 		},
 		destroyed() {

 		},
 		mounted() {
 			this.getUser();
 		},
 	}
 </script>
 <style type="text/css" lang="less" scoped="scoped">
 	input::-webkit-input-placeholder,
 	textarea::-webkit-input-placeholder {
 		color: #999;
 		font-size: 0.14rem;
 	}

 	.setPassword {
 		min-height: 100vh;
 		background: #0f161c;

 		.header {
 			width: 100%;
 			height: .44rem;
 			background: #fff;
 		}

 		.list {
 			margin: .13rem;
 			font-weight: 400;
 			font-size: 0.12rem;
 			color: #FFFFFF;

 			.btn-big {
 				margin-top: .3rem;
 				margin-bottom: .3rem;
 			}
 		}

 		.erty {
 			width: 100%;
 			margin: 0 auto;
 			min-height: 0.8rem;
 			margin-top: -0.27rem;
 			padding-top: 0.3rem;

 			li {
 				margin: 0 auto;
 				margin: 0.1rem 0;
 				font-weight: 500;
 				font-size: .14rem;
 				color: #24272C;

 				input {
					width: 100%;
 					height: 0.46rem;
 					background: #000000;
 					border-radius: 0.1rem;
 					font-weight: 400;
 					font-size: 0.14rem;
 					color: #fff;
 					padding-left: 0.1rem;
 				}
 			}
 		}
 	}
 </style>