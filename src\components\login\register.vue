<template>
	<div class="pages">
		<!-- <top-back title="註冊开户" :back="true"></top-back> -->
		<!-- <div class="header flex flex-c">
			<div class="t">登入</div>
		</div>
		<div class="flex-column-item logo">
			<img :src="config.logo" style="width: 0.78rem;height: 0.78rem;" />
			<div class="t">{{ config.title }}</div>
		</div> -->
		<div class="point1"></div>
		<div class="point2"></div>
		<div class="boxs">
			<div class="tit">註冊</div>
			<!-- <div class="tabs nflex nflex-lb">
				<span class="tabs-i" @click="$toPage('/login')">帳號登入</span>
				<span class="tabs-i active">註冊帳號</span>
			</div> -->
			<div class="inputs">
				<!-- <div class="tt">身分證</div> -->
				<div class="inputs-i nflex nflex-lb">
					<!-- <div class="icon sfz"></div> -->
					<van-field v-model="phone" maxlength="10" :border="false" placeholder="請輸入身分證" />
				</div>
				<!-- <div class="tt">密碼</div> -->
				<div class="inputs-i nflex nflex-lb">
					<!-- <div class="icon mm"></div> -->
					<van-field v-model="password" :type="showPass01 ? 'text' : 'password'" :border="false"
						placeholder="請輸入密碼" />
					<div class="show">
						<img src="../../assets/v6/by.png" v-show="showPass01" @click="showPass01 = !showPass01" alt="">
						<img src="../../assets/v6/zy.png" v-show="!showPass01" @click="showPass01 = !showPass01" alt="">
					</div>
				</div>
				<!-- <div class="tt">密碼</div> -->
				<div class="inputs-i nflex nflex-lb">
					<!-- <div class="icon mm"></div> -->
					<van-field v-model="passwords" :type="showPass02 ? 'text' : 'password'" :border="false"
						placeholder="請再次輸入密碼" />
					<div class="show">
						<img src="../../assets/v6/by.png" v-show="showPass02" @click="showPass02 = !showPass02" alt="">
						<img src="../../assets/v6/zy.png" v-show="!showPass02" @click="showPass02 = !showPass02" alt="">
					</div>
				</div>
				<!-- <div class="tt">擔保碼</div> -->
				<div class="inputs-i nflex nflex-lb">
					<!-- <div class="icon mm"></div> -->
					<van-field v-model="invCode" :border="false" placeholder="請輸入擔保碼" />
				</div>
			</div>
			<div class="btns">
				<div class="login flex flex-c" @click="submit">立即註冊</div>
				<div class="register flex flex-c" @click="$toPage('/login/login')">已有帳號？<span>登入</span></div>
			</div>
		</div>
		<!-- <div class="bottoms">
			<div class="t1">登录即同意{{ config.title }}用户隱私協議</div>
			<div class="t1" @click="$toPage('kefu', config.kefu)">聯絡線上營業員</div>
			<div class="t2">1.5.2（2024050701）</div>
		</div> -->
	</div>
</template>

<script>
	export default {
		name: 'Register',
		data() {
			return {
				phone: '',
				password: '',
				passwords: '',
				invCode: '',
				config: {},
				showPass01: false,
				showPass02: false,
			}
		},
		created() {
			this.invCode = this.$route.query.invitecode;
			this.getConfig()
		},
		methods: {
			getConfig() {
				this.$server.post('/common/config', {
					type: 'all'
				}).then(res => {
					if (res && res.status == 1) {
						const arr = res.data
						const obj = {}
						arr.forEach(item => {
							obj[item.name] = item.value
						})
						this.config = obj
					}
				})
			},
			submit() {
				if (!this.phone) return this.$vApi.Toast('請輸入身分證')
				if (!this.isTwIDCard(this.phone)) return this.$vApi.Toast('身分證不合規')
				if (!this.password) return this.$vApi.Toast('請輸入密碼')
				if (!this.passwords) return this.$vApi.Toast('請再次輸入密碼')
				if (this.password != this.passwords) return this.$vApi.Toast('兩次輸入密碼不一致')
				//if (!this.invCode) return this.$vApi.Toast('請輸入擔保碼')
				this.$vApi.Toast.loading({
					message: '註冊...',
					forbidClick: true,
					loadingType: 'spinner'
				})
				this.$server.post('/user/register', {
					account: this.phone,
					pwd: this.password,
					pwd1: this.passwords,
					// pwd2: this.passwords,
					inviter: this.invCode
				}).then(res => {
					if (res && res.status == 1) {
						this.$vApi.Toast.success('註冊成功')
						setTimeout(() => {
							this.$emit('selectLogin')
							// this.$toPage('/login/login')
						}, 1000)
					} else {
						this.$vApi.Toast.fail(this.$t(res.msg))
					}
				})
			},
			isTwIDCard(idcard) {
				var reg = /^[A-Z][0-9]{9}$/;
				if (reg.test(idcard) === false) {
					return false
				} else {
					return true
				}
			}
		}
	}
</script>

<style lang="less" scoped>
	::v-deep .van-cell {
		width: auto !important;
	}
	.header {
		width: 100%;
		position: fixed;
		height: 0.5rem;
		top: 0;
		left: 0;
		z-index: 999;
		padding: 0.12rem;
		background-color: #1B167A;
		.t {
			position: absolute;
			left: 50%;
			transform: translateX(-50%);
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			font-size: 0.16rem;
			color: #FFFFFF;
		}
	}
	.pages {
		width: 100%;
		padding: 0.8rem 0 0;
		min-height: 100vh;
		background: #18191B;
		.logo {
			.t {
				margin-top: 0.1rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 0.21rem;
				color: #FFFFFF;
			}
		}
		.point1{
			position: absolute;
			left: 0.15rem;
			top: 0.45rem;
			width: 0.5rem;
			height: 0.5rem;
			opacity: 0.8;
			border-radius: 50%;
			background-image: radial-gradient(circle, rgba(152, 239, 134, 1) 0%,rgba(152, 239, 134, 0.8) 20%, transparent 50%);
		}
		.point2{
			position: absolute;
			right: -1.2rem;
			top: -1.2rem;
			width: 3rem;
			height: 3rem;
			opacity: 0.5;
			border-radius: 50%;
			// box-shadow: 0 0 20px 0px #98EF86, 0 0 20px 0px #98EF86, 0 0 20px 20px #98EF86, 0 0 20px #98EF86;
			background-image: radial-gradient(circle, rgba(152, 239, 134, 0.8) 0%,rgba(152, 239, 134, 0.3) 30%, transparent 50%);
		}
	}

	.info {
		width: 100%;
		height: 0.35rem;

		img {
			width: 0.9rem;
			height: 0.9rem;
		}

		span {
			padding-left: 0.2rem;
			font-family: PangMenZhengDaoBiaoTiTiMianFeiBan,
				PangMenZhengDaoBiaoTiTiMianFeiBan;
			font-weight: normal;
			font-size: 0.6rem;
			color: #ffffff;
		}
	}

	.slogo {
		width: 60%;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
		margin-top: 0.1rem;

		.icon {
			margin-right: 10px;
		}

		.txt {
			line-height: 0.3rem;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 0.15rem;
			color: #dc183b;
		}
	}

	.boxs {
		margin: 0.14rem;
		.tit{
			padding: 0 0.1rem;
			margin-bottom: 0.6rem;
			font-family: PingFang SC;
			font-weight: 600;
			font-size: 0.15rem;
			color: #FFFFFF;
		}
		.tabs {
			width: 100%;
			height: 0.45rem;
			.tabs-i {
				padding-bottom: 0.2rem;
				width: 50%;
				text-align: center;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 0.2rem;
				color: #666666;
	
				&.active {
					color: #ffffff;
					border-bottom: 0.04rem solid #c94d5b;
				}
			}
		}
	
		.inputs {
			margin: 0.25rem 0 0;
			.tt {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 0.15rem;
				color: #181818;
			}
			.inputs-i {
				margin: 0.1rem 0 0.2rem;
				height: 0.51rem;
				background: #232429;
				border-radius: 0.26rem;
				padding: 0 0.12rem;
				position: relative;
				.van-field {
					flex: 1;
					padding: 0 0.1rem;
					line-height: 0.48rem;
					background: transparent;
					::v-deep input {
						line-height: 0.46rem;
						font-family: PingFang SC;
						font-weight: 400;
						font-size: 0.14rem;
						color: #fff;
					}
				}
		
				img {
					width: 0.17rem;
					height: 0.09rem;
				}
		
				.show {
					position: absolute;
					right: 0.16rem;
					top: 55%;
					transform: translateY(-50%);
				}
			}
		}
	
		.blocks {
			padding: 0 0 0.1rem;
			.blocks-text {
				padding-left: 0.06rem;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 0.15rem;
				color: rgba(21,21,21,0.88);
			}
	
			.blocks-text2 {
				font-family: Inter, Inter;
				font-weight: 400;
				font-size: 0.15rem;
				line-height: 0.2rem;
				color: #1B167A;
				text-decoration: underline;
			}
		}
	
		.btns {
			margin: 0.25rem 0 0;
			.register {
				margin-top: 0.16rem;
				font-family: PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
				color: #666666;
				span{
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 0.13rem;
					color: #fff;
				}
			}
		
			.login {
				height: 0.46rem;
				background: linear-gradient(90deg, #98EF86, #C7F377);
				border-radius: 0.23rem;
				font-family: PingFang SC;
				font-weight: 500;
				font-size: 0.15rem;
				color: #000000;
				line-height: 0.46rem;
			}
		}
	
		.van-button {
			margin-top: 0.2rem;
			height: 0.025rem;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 2.125rem;
			color: #ffffff;
			border-radius: 6px;
		}
	}
	.bottoms {
		margin: 0.1rem 0.2rem 0;
		.t1 {
			padding: 0.03rem 0;
			text-align: center;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 0.14rem;
			color: #FFFFFF;
		}
		.t2{
			text-align: center;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 0.12rem;
			color: #FFFFFF;
		}
	}
</style>