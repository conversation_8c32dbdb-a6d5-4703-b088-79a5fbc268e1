<template>
  <div class="page">
    <div class="top-fixed">
      <div class="header flex flex-b">
        <div class="icon ss" @click="$toPage('/favorite/search')"></div>
        <div class="tit">大盤</div>
        <div class="flex">
          <div class="icon ring2 animate__animated animate__fadeIn" @click="$toPage('/information/userInfo')"><span v-if="readNum>0" style="width: .08rem;height: .08rem;border-radius: 50%;background: #ff0000;margin-right: .05rem;display: block;"></span></div>
          <div style="margin-left: 0.16rem;" class="icon set animate__animated animate__fadeIn"
               @click="$toPage('/information/setting')"></div>
        </div>
      </div>
    </div>

    <div class="flex" style="margin:.1rem;">
      <div class="split"></div>
      <div class="cunnamne">{{ currentSymbolName }}</div>
    </div>

    <div class="zx-cot">
      <div class="top-middle" id="chart"></div>
      <div class="zx-list flex flex-b">
        <div :class="item.symbol == currentSymbol?'zx-itemd':''"
             @click="selectType(item.symbol,item.symbolName,item.kline)" class="zx-item flex-column-item"
             v-for="(item, i) in indexList" :key="i" v-if="i<3">
          <div class="name">{{ item.symbolName }}</div>
          <div class="price" :class="item.change > 0 ? 'red' : 'green'">
            {{ $formatMoney(item.price) }}
          </div>
          <div class="flex per" :class="item.change > 0 ? 'red' : 'green'">
            {{ $formatMoney(item.change) }}
          </div>
          <div class="flex per" :class="item.change > 0 ? 'red' : 'green'">
            <div class="icon down03" :class="{up03:item.change > 0}"></div>
            <template v-if="item.changePercent!=='100.00%' && item.changePercent!=='-100.00%'">{{ item.changePercent }}</template>
            <template>--</template>
          </div>
        </div>
      </div>
    </div>
    <div class="zx-cot02">
      <div class="zx-list">
        <div class="zx-item flex" v-for="(item, i) in indexList" :key="i" v-if="i>2">
          <div class="name flex-1">{{ item.symbolName }}</div>
          <div class="price flex-1" :class="item.change > 0 ? 'red' : 'green'">
            {{ $formatMoney(item.price) }}
          </div>
          <div class="flex flex-e per flex-1" :class="item.change > 0 ? 'red' : 'green'">
            <div class="icon down03" :class="{up03:item.change > 0}"></div>
            {{ $formatMoney(item.change) }}({{ item.changePercent }})
          </div>
        </div>
      </div>
    </div>


    <div class="flex" style="margin:.1rem;">
      <div class="split"></div>
      <div class="cunnamne">個股漲跌分佈</div>
    </div>

    <div class="line">
      <div id="lineBody"></div>
    </div>

    <tab-bar :current="4"></tab-bar>
  </div>
</template>

<script>
import qs from "qs";
import {
  init,
  dispose,
  extension
} from "klinecharts";
import * as echarts from 'echarts';

export default {
  data() {
    return {
      readNum: 0,
      indexList: [],
      currentSymbol: '',
      currentSymbolName: '',
      chartData: [],
      setInterval:'',
      chart:''
    }
  },
  created() {
    this.readData();
    this.getIndexList();

    this.setInterval = setInterval(()=>{
      this.getIndexList1()
    },5000)

    this.$nextTick(() => {
      this.getGgfb()
    })
  },
  destroyed() {
    clearInterval(this.setInterval)
    this.setInterval = ''
  },
  methods: {
    readData() {
      this.$server.post("/user/notice", { type: "zar" }).then((res) => {
        if (res.status == 1) {
          let list = res.data;
          let length = list.length
          let a
          for(a=0;a<length;a++){
            let read = localStorage.getItem("readMsg")
            let oldRead = JSON.parse(read)
            let hasValue = oldRead.id.includes(list[a].id.toString())
            if(!hasValue){
              this.readNum += 1
            }
          }
        }
      });
    },
    getGgfb() {
      let myChart = echarts.init(document.getElementById('lineBody'));
      this.$server.post('/parameter/zdtock')
          .then((res) => {
            myChart.setOption({
              // title: {
              //   text: '台股市場漲跌分佈',
              //   textStyle: {fontSize: 15, color: '#333', fontWeight: 500}
              // },
              tooltip: {},
              xAxis: {
                data: ['>7%', '7%~5%', '5%~3%', '3%~0', '平盤', '0~3%', '3%~5%', '5%~7%', '>7%'],
                axisLabel: {interval: 0, rotate: 30},
                splitLine: {
                  show: false
                }
              },
              yAxis: {
                splitLine: {
                  show: false
                }
              },
              series: [{
                type: 'bar',
                label: {
                  show: true,
                  position: "top",
                  textStyle: {
                    fontWeight: "bolder",
                    fontSize: "12",
                  },
                },
                data: [{
                  value: res.data[0],
                  itemStyle: {
                    normal: {
                      color: '#d31b26'
                    }
                  }
                },
                  {
                    value: res.data[1],
                    itemStyle: {
                      normal: {
                        color: '#d31b26'
                      }
                    }
                  },
                  {
                    value: res.data[2],
                    itemStyle: {
                      normal: {
                        color: '#d31b26'
                      }
                    }
                  },
                  {
                    value: res.data[3],
                    itemStyle: {
                      normal: {
                        color: '#d31b26'
                      }
                    }
                  },
                  {
                    value: res.data[4],
                    itemStyle: {
                      normal: {
                        color: '#2f4554'
                      }
                    }
                  },
                  {
                    value: res.data[5],
                    itemStyle: {
                      normal: {
                        color: '#1BBD5D'
                      }
                    }
                  },
                  {
                    value: res.data[6],
                    itemStyle: {
                      normal: {
                        color: '#1BBD5D'
                      }
                    }
                  },
                  {
                    value: res.data[7],
                    itemStyle: {
                      normal: {
                        color: '#1BBD5D'
                      }
                    }
                  },
                  {
                    value: res.data[8],
                    itemStyle: {
                      normal: {
                        color: '#1BBD5D'
                      }
                    }
                  },
                ]
              }]
            });
          });
    },
    selectType(symbol, symbolName, kline) {
      this.chartData = []
      this.currentSymbol = symbol
      this.currentSymbolName = symbolName
      if(this.chart){
        dispose(this.chart)
      }
      this.getChart(kline)
    },
    getIndexList() {
      this.$server.post("/parameter/zhishulist", {
        type: "zar"
      }).then((res) => {
        this.indexList = res.data;
        this.currentSymbolName = this.indexList[0].symbolName
        this.currentSymbol = this.indexList[0].symbol
        this.getChart(this.indexList[0].kline)
      });
    },
    getIndexList1() {
      this.$server.post("/parameter/zhishulist", {
        type: "zar"
      }).then((res) => {
        this.indexList = res.data;
      });
    },
    setChartOption() {
      this.chart = init(document.getElementById("chart"));
      // chart.setTimezone("Australia/Sydney")
      this.chart.setStyleOptions({
        candle: {
          type: 'area',
          // 蜡烛柱
          bar: {
            upColor: "#5dc933",
            downColor: "#c62d29",
            noChangeColor: "#888888",
          },
          tooltip: {
            showRule: 'none'
          },
        },
        // 技术指标
        technicalIndicator: {
          margin: {
            top: 0.2,
            bottom: 0.1,
          },
          bar: {
            upColor: "#5dc933",
            downColor: "#c62d29",
            noChangeColor: "#888888",
          },
          line: {
            size: 1,
            colors: ["#FF9600", "#9D65C9", "#2196F3", "#E11D74", "#01C5C4"],
          },
          circle: {
            upColor: "#5dc933",
            downColor: "#c62d29",
            noChangeColor: "#888888",
          },
        },
        grid: {
          show: false,
          // 网格水平线
          horizontal: {
            show: true,
            size: 1,
            color: "#7B7B7B",
            // 'solid'|'dash'
            style: "dash",
            dashValue: [2, 2],
          },
          // 网格垂直线
          vertical: {
            show: true,
            size: 1,
            color: "#7B7B7B",
            // 'solid'|'dash'
            style: "dash",
            dashValue: [2, 2],
          },
        },
        xAxis: {
          tickLine: {
            color: "#7B7B7B",
          },
          //   坐标轴线
          axisLine: {
            show: false,
            color: "#7B7B7B",
          },
        },
        yAxis: {
          tickLine: {
            show: true,
            size: 1,
            length: 3,
            color: "#191a1f",
          },
          //   坐标轴线
          axisLine: {
            show: false,
            color: "#191a1f",
          },
        },
        separator: {
          size: 1,
          color: "#fff",
          activeBackgroundColor: "rgba(115, 1, 16, .5)",
        },

      });
      this.chart.applyNewData(this.chartData);
    },
    getChart(items) {
      items.forEach(item => {
        let kLineModel = {
          open: item.c === null ? 0 : item.c / 1,
          close: item.c === null ? 0 : item.c / 1,
          high: item.c === null ? 0 : item.c / 1,
          low: item.c === null ? 0 : item.c / 1,
          volume: item.s === null ? 0 : item.s * 10,
          timestamp: item.t * 1
        };
        this.chartData.push(kLineModel);
      })
      this.setChartOption()
    },
  }
}
</script>

<style scoped lang="less">
.top-middle {
  width: 100%;
  height: 2rem;
}

.line {
  padding: 20px;
  background: #1C1C1C;

  #lineBody {
    width: 100%;
    height: 3rem;
  }
}

.page {
  padding: 0.5rem 0 1rem;

  .top-fixed {
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999;

    .header {
      height: 0.5rem;
      padding: 0.15rem;
      width: 100%;
      background: #2D2D2D;
      position: relative;

      .tit {
        position: absolute;
        left: 48%;
        top: 50%;
        transform: translateY(-50%);
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 0.15rem;
        color: #FFFFFF;
      }
    }
  }

  .split {
    width: 5px;
    height: 29px;
    background: #DE9F2E;
    border-radius: 0px 0px 0px 0px;
  }

  .cunnamne {
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 0.15rem;
    color: #FFFFFF;
    line-height: 20px;
    text-align: center;
    font-style: normal;
    text-transform: none;
    margin-left: 6px;
  }

  .red {
    color: #EB2733;
  }

  .green {
    color: #60AA45;
  }

  .zx-cot {
    .zx-list {
      padding: 0.1rem;

      .zx-item {
        padding: 0.1rem;
        width: 32%;
        border-radius: 0.04rem 0.04rem 0.04rem 0.04rem;
        border: 0.01rem solid #323232;

        .name {
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 0.14rem;
          color: #FFFFFF;
        }

        .price {
          padding: 0.05rem 0;
          font-family: PingFang SC, PingFang SC;
          font-weight: 500;
          font-size: 0.16rem;
        }

        .per {
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 0.12rem;

          .icon {
            margin-right: 0.02rem;
          }
        }

      }

      .zx-itemd {
        border: 1px solid #B8863B;
      }
    }
  }

  .zx-cot02 {
    .zx-list {
      padding: 0.1rem 0;
      background: #212121;

      .zx-item {
        padding: 0.15rem 0.1rem;
        width: 100%;
        border-bottom: 0.01rem solid #303030;

        .name {
          font-family: PingFang SC, PingFang SC;
          font-weight: 500;
          font-size: 0.14rem;
          color: #FFFFFF;
        }

        .price {
          font-family: PingFang SC, PingFang SC;
          font-weight: 500;
          font-size: 0.12rem;
        }

        .per {
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 0.12rem;

          .icon {
            margin-right: 0.02rem;
          }
        }
      }
    }
  }
}


</style>