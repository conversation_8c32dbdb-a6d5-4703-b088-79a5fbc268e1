<template>
	<div class="tabss flex flex-b"><!-- 底部导航 -->
			<div class="item" :class="{ active: current == 0 }" @click="$toPage('/home/<USER>')">
				<div class="icon a animate__animated animate__fadeIn" :class="{ ac: current == 0 }"></div>
				<div class="t">{{ $t("menu").href1 }}</div>
			</div>
			
			<div class="item" :class="{ active: current == 1 }" @click="$toPage('/market/index')">
				<div class="icon a1 animate__animated animate__fadeIn" :class="{ ac1: current == 1 }"></div>
				<div class="t">{{ $t("menu").href3 }}</div>
			</div>
			
			<div class="item" :class="{ active: current == 2 }" @click="$toPage('/favorite/index')" >
				<div class="icon a2 animate__animated animate__fadeIn" :class="{ ac2: current == 2 }" ></div>
				<!-- <div class="t">{{ $t("menu").href4 }}</div> -->
			</div>
			
			<div class="item" :class="{ active: current == 3 }" @click="$toPage('/trade/index')">
				<div class="icon a3 animate__animated animate__fadeIn" :class="{ ac3: current == 3 }"></div>
				<div class="t">{{ $t("menu").href6 }}</div>
			</div>
			
			<!-- <div class="item" :class="{ active: current == 2 }" @click="$toPage('/home/<USER>')">
				<div class="icon a2 animate__animated animate__fadeIn" :class="{ ac2: current == 2 }"></div>
				<div class="t">{{ $t("市场") }}</div>
			</div> -->
			

			<div class="item" :class="{ active: current == 4 }" @click="$toPage('/home/<USER>')">
				<div class="icon a4 animate__animated animate__fadeIn" :class="{ ac4: current == 4 }"></div>
				<div class="t">{{ $t("menu").href2 }}</div>
			</div>
		
	</div>
</template>

<script>
	export default {
		name: "tab-bar",
		props: {
			current: {
				type: Number,
				default: "",
			},
		},
		data() {
			return {};
		},
		components: {},
		methods: {},
		created() {},
		computed: {},
	};
</script>

<style scoped lang="less">
	.tabss {
		width: 100%;
		height: 0.7rem;
		background: url('../../assets/v2/tabBg02.png') no-repeat center/100%;
		position: fixed;
		bottom: -0.01rem;
		left: 0;
		z-index: 999;
		padding: 0.3rem 0 0.2rem;
		
		.item {
			text-align: center;
			flex: 1;
			&:nth-child(3){
				margin-top: -0.3rem;
			}
			&.active {
				.t {
					color:#e10414;
				}
			}

			.icon {
				margin: 0 auto;
			}

			.t {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
				color: #78787A;
				margin-top:.05rem;
			}
		}
	}
</style>