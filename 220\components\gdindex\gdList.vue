<template>
	<div class="gdList">
		<div class="headf">
			<top :title="$t(text)"></top>
		</div>

		<div class="pageTow">
			<div class="list-box">
				<div class="list-content" v-if="myList.length > 0">
					<div class="list-item flex flex-b flex-wrap text-center" v-for="(item, index) in myList" :key="index">
						<div class="item">
							<div class="t">
								{{ $t("产品") }}
							</div>
							<div class="t1">
								{{ item.name }}
							</div>
						</div>
						<div class="item">
							<div class="t">
								{{ $t("买入金额") }}
							</div>
							<div class="t1">
								{{ item.money }}円
							</div>
						</div>

						<div class="item">
							<div class="t">
								{{ $t("买入时间") }}
							</div>
							<div class="t1">
								{{ item.buy_time }}
							</div>
						</div>

						<div class="item">
							<div class="t">
								{{ $t("总盈利") }}
							</div>
							<div class="t1 red">
								{{ item.aprofit }}
							</div>
						</div>
						<div class="item">
							<div class="t">
								{{ $t("昨日盈利") }}
							</div>
							<div class="t1 red">
								{{ item.dprofit }}
							</div>
						</div>
						<div class="item">
							<div class="t">
								{{ $t("锁仓时间") }}
							</div>
							<div class="t1">
								{{ item.locktime }}
							</div>
						</div>
					</div>
				</div>
				<div v-else>
					<div class="noData text-center">{{$t('home').txt21}}</div>
				</div>
			</div>
		</div>

	</div>
</template>

<script>
	import axios from "axios";
	import top from "../bar/toper.vue";
	import Vue from "vue";
	import qs from "qs";
	import {
		Toast
	} from "vant";
	Vue.use(Toast);
	import {
		Popup
	} from "vant";
	Vue.use(Popup);
	export default {
		name: "gdList",
		props: {},
		data() {
			return {
				text: this.$t('fllow').txt1,
				secNavList: ["名称/代码", "价格/申请量", "成交额", "状态"],
				secNavListW: [
					"text-align:left;width:25%",
					"text-align:center;width:30%",
					"text-align:center;width:30%",
					"text-align:right;width:20%",
				],
				myList: [],
			};
		},
		components: {
			top,
		},
		methods: {
			getFllow() {
				this.$server.post("/riben/userproductlist ", {}).then((res) => {
					// res.data = {
					//   "status": 1,
					//   "msg": "查询成功",
					//   "data": [
					//     {
					//       "id": 15,
					//       "strategy_num": "202309251436383200",
					//       "agent_id": 100,
					//       "account": "***********",
					//       "sender": "六老师",
					//       "name": "刘老师基金一号",
					//       "money": "100000.00",
					//       "buy_time": "2023-09-25 14:36",
					//       "dprofit": "0.00",
					//       "aprofit": "0.00",
					//       "pid": 1,
					//       "status": 0,
					//       "locktime": "2023-10-31 00:00",
					//       "code": "SZ000001",
					//       "allstock": [
					//         {
					//           "shou": 100,
					//           "symbolName": "平安银行",
					//           "name": "平安银行",
					//           "symbol": "SZ000001",
					//           "price": 11.16,
					//           "open": 11.22,
					//           "preClose": 11.22,
					//           "volume": 444980,
					//           "gain": -0.534763,
					//           "gainValue": -0.****************,
					//           "high": 11.26,
					//           "low": 11.14,
					//           "Amount": *********,
					//           "pe": "",
					//           "pb": "",
					//           "buy1": 11.16,
					//           "buy2": 11.15,
					//           "buy3": 11.14,
					//           "buy4": 11.13,
					//           "buy5": 11.12,
					//           "buyv1": 971,
					//           "buyv2": 6267,
					//           "buyv3": 5148,
					//           "buyv4": 8127,
					//           "buyv5": 11268,
					//           "sell1": 11.17,
					//           "sell2": 11.18,
					//           "sell3": 11.19,
					//           "sell4": 11.2,
					//           "sell5": 11.21,
					//           "sellv1": 2186,
					//           "sellv2": 4731,
					//           "sellv3": 4938,
					//           "sellv4": 6490,
					//           "sellv5": 915
					//         }
					//       ]
					//     },
					//     {
					//       "id": 16,
					//       "strategy_num": "202309252042416890",
					//       "agent_id": 100,
					//       "account": "***********",
					//       "sender": "六老师",
					//       "name": "刘老师基金一号",
					//       "money": "10000.00",
					//       "buy_time": "2023-09-25 20:42",
					//       "dprofit": "0.00",
					//       "aprofit": "0.00",
					//       "pid": 1,
					//       "status": 0,
					//       "locktime": "2023-10-31 00:00",
					//       "code": "SZ000001",
					//       "allstock": [
					//         {
					//           "shou": 100,
					//           "symbolName": "平安银行",
					//           "name": "平安银行",
					//           "symbol": "SZ000001",
					//           "price": 11.16,
					//           "open": 11.22,
					//           "preClose": 11.22,
					//           "volume": 444980,
					//           "gain": -0.534763,
					//           "gainValue": -0.****************,
					//           "high": 11.26,
					//           "low": 11.14,
					//           "Amount": *********,
					//           "pe": "",
					//           "pb": "",
					//           "buy1": 11.16,
					//           "buy2": 11.15,
					//           "buy3": 11.14,
					//           "buy4": 11.13,
					//           "buy5": 11.12,
					//           "buyv1": 971,
					//           "buyv2": 6267,
					//           "buyv3": 5148,
					//           "buyv4": 8127,
					//           "buyv5": 11268,
					//           "sell1": 11.17,
					//           "sell2": 11.18,
					//           "sell3": 11.19,
					//           "sell4": 11.2,
					//           "sell5": 11.21,
					//           "sellv1": 2186,
					//           "sellv2": 4731,
					//           "sellv3": 4938,
					//           "sellv4": 6490,
					//           "sellv5": 915
					//         }
					//       ]
					//     }
					//   ]
					// };

					if (res.data.status === 1) {
						// this.fllowList = [];
						// for (let key in res.data) {
						//   let obj = res.data[key];
						//   this.fllowList.push(obj);
						// }

						// // 單獨處理一個列表,返回數據裡面有其他的內容
						// let data = res.data;
						// delete data.jrsy;
						// delete data.zrsy;
						// delete data.zsy;
						// this.myList = Object.values(data);
						this.myList = res.data.data;
						
						//   console.log('跟單記錄', this.FllowList);
					}
				});

				// this.$api.request('/transaction', '/productlist', {}).then(res => {
				//   if (res.status === 1) {
				//     let dict = {};
				//     res.data.forEach(vo => {
				//       dict[vo.name] = vo;
				//     });

				//     this.fllow = dict;
				//     console.log(this.fllow);
				//   }
				// });
			},
			toInfo(item) {
				localStorage.setItem('gdInfo', JSON.stringify(item));
				this.$router.push('/gdindex/gdInfo')
			}
		},
		created() {
			this.getFllow();
		},
		computed: {},
	};
</script>

<style scoped lang="less">
	.gdList{
		background: #F5F5F5;
		min-height: 100vh;
		padding-top:.44rem;
		.headf {
			height: 0.48rem;
			width: 100vw;
			background: #fff;
			position: fixed;
			top: 0;
			left: 0;
			z-index: 998;
		}
		
		.pageTow {
			padding-top:.2rem;padding-bottom: .2rem;
			.list-box {
				.list-item {
					background: #fff;
					box-shadow: 0 .02rem .09rem 0 rgba(0,0,0,0.18);
					border-radius: .12rem;
					overflow: hidden;
					padding: .1rem;
					margin: 0 .1rem .1rem;				
					.item {
						width: 33%;
						margin-bottom:.1rem;
					}
		
					.t {
						color: #999;
						font-size: 12px;
						margin-top: 10px;
					}
		
					.t1 {
						color: #000;
						font-size: 14px;
						margin-top: 10px;
		
						&.red {
							color: #ea3544;
						}
					}
		
					
				}
		
				
			}
		
			
		}
		
	}
	
	
</style>