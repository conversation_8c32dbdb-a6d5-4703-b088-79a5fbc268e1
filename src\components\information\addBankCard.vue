<template>
	<div class="page ">
		<top-back title="添加存摺"></top-back>

		<div class="cot">
			<!-- <div class="tit">帳號資訊</div> -->
			<div class="item">
				<div class="t">持卡人姓名</div>
				<input placeholder="請輸入持卡人姓名" v-model="realname" />
			</div>
			<div class="item">
				<div class="t">金融卡卡號</div>
				<input placeholder="請輸入銀行存摺帳號" v-model="bank_num" />
			</div>

			<div class="item">
				<div class="t">金融卡銀行名稱</div>
				<input placeholder="請輸入銀行名稱" v-model="bank_name" />
			</div>

			<div class="item">
				<div class="t">分行名稱</div>
				<input placeholder="請輸入分行名稱" v-model="bank_address" />
			</div>
			<div class="item">
				<div class="t">分行代碼</div>
				<input placeholder="請輸入銀行代碼" v-model="bank_code" />
			</div>
			<div class="defbtn animate__animated animate__fadeIn" @click="addCard">
				確認
			</div>
		</div>
		
		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "addBankCard",
		props: {},
		data() {
			return {
				bank_num: "",
				bank_name: "",
				bank_address: "",
				realname: "",
				bank_code: "",
				userInfo: {},
			};
		},
		components: {},
		created() {
			this.getUserInfo();
		},
		computed: {},
		methods: {
			async getUserInfo() {
				const res = await this.$server.post("/user/getUserinfo", {
					type: "twd"
				});
				this.userInfo = res.data;
			},
			addCard() {
				// 未完成實名跳轉
				if (this.userInfo.is_true !== 1) {
					this.$toast("請先完成實名認證");
					setTimeout(() => {
						this.$toPage("/information/authInfo");
					}, 1500);

					return;
				}

				let that = this;
				if (
					!!that.bank_num &&
					!!that.bank_name &&
					!!that.realname &&
					!!that.bank_address &&
					!!that.bank_code
				) {
					let parmes = {
						type: "twd",
						bank_num: that.bank_num,
						bank_name: that.bank_name,
						bank_address: that.bank_address,
						realname: this.realname,
						bank_code: this.bank_code,
					};
					this.$refs.loading.open(); //开启加载

					this.$server.post("/user/addCard", parmes).then((res) => {
						this.$refs.loading.close();

						if (res.status == 1) {
							this.$toast(res.msg);
							setTimeout(() => {
								this.$router.go(-1);
							}, 1500);
						}
					});
				} else {
					this.$toast("請填寫金融卡信息");
				}
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.6rem 0.12rem 0;
		min-height: 100vh;
		.cot {
			.tit{
				font-family: PingFang SC;
				font-weight: 400;
				font-size: 0.15rem;
				color: #999999;
				margin-bottom: 0.2rem;
			}
			.item {
				margin-bottom: 0.1rem;

				.t {
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 0.15rem;
					color: #999999;
					margin: 0.12rem 0;
				}
				input {
					height: 0.56rem;
					background: #434446;
					border-radius: 0.16rem;
					width: 100%;
					padding: 0 0.1rem;
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 0.15rem;
					color: #fff;
					line-height: 0.56rem;

					&::placeholder {
						color: #999999;
					}
				}
			}
		}
	}
</style>