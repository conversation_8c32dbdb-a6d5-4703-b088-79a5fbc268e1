<template>
  <div>
    <!-- <div class="flex flex-b nav-box">
      <div
        class="nav-item"
        :class="{ active: type == item.type }"
        v-for="(item, i) in typeList"
        :key="i"
        @click="changeType(item.type)"
      >
        {{ $t(item.name) }}
      </div>
    </div> -->

    <van-list
      v-model="loading"
      :finished="finished"
      :finished-text="$t('new').a51"
      :loading-text="$t('new').a"
      @load="onLoad"
    >
      <div class="list">
        <no-data v-if="show"></no-data>

        <div
          class="item flex flex-b"
          v-for="(item, i) in list"
          :key="i"
          @click="$openUrl(item.link)"
        >
          <div class="txt">
            <div class="t el">{{ item.title }}</div>
            <div class="t1">{{ item.dt }}</div>
          </div>
          <div class="img animate__animated animate__fadeIn" v-if="item.image">
            <img :src="item.image" mode="aspectFill" />
          </div>
        </div>
      </div>
    </van-list>

    <loading ref="loading" />
  </div>
</template>

<script>
export default {
  name: "questionInfo",
  props: {
    symbol: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      show: false,
      loading: false,
      finished: false,
      type: "xiaoxi",
      page: 0,
      typeList: [
        {
          name: "消息",
          type: "xiaoxi",
        },
        {
          name: "披露",
          type: "pilou",
        },
        {
          name: "红外",
          type: "hongwai",
        },
        {
          name: "报告",
          type: "baogao",
        },
      ],
      list: [],
    };
  },
  created() {},
  methods: {
    onLoad() {
      this.page += 1;
      this.getInfo();
    },
    changeType(type) {
      this.type = type;
      this.page = 0;
      this.list = [];
      this.getInfo();
      this.$refs.loading.open(); //开启加载
    },
    getInfo() {
      this.$refs.loading.open(); //开启加载

      this.$server
        .post("/transaction/wenti", {
          symbol: this.symbol,
          type: this.type,
          page: this.page,
        })
        .then((res) => {
          this.loading = false;
          if (res.status == 1) {
            this.$refs.loading.close(); //关闭加载

            let data = [];
            // 前面两项返回数据结构不同
            if (this.type == "xiaoxi" || this.type == "pilou") {
              data = res.data.data;
            } else {
              data = res.data;
            }
            data.forEach((item) => {
              // item.dt = this.formatDate('YYYY.MM.DD hh:mm:ss', new Date(item.dt).getTime());
              item.dt = this.$getTimeData(item.dt);
            });
            this.list = [...this.list, ...data];
            if (this.list.length !== 0 && data.length == 0) {
              this.finished = true; //结束列表加载
            }
            if (this.list.length == 0 && data.length == 0) {
              this.show = true; //显示暂无数据
            }
          }
        });
    },
    formatDate(fmt, date) {
      let ret;
      date = new Date(date);
      const opt = {
        "Y+": date.getFullYear().toString(), // 年
        "M+": (date.getMonth() + 1).toString(), // 月
        "D+": date.getDate().toString(), // 日
        "h+": date.getHours().toString(), // 时
        "m+": date.getMinutes().toString(), // 分
        "s+": date.getSeconds().toString(), // 秒
        // 有其他格式化字符需求可以继续添加，必须转化成字符串
      };
      for (let k in opt) {
        ret = new RegExp("(" + k + ")").exec(fmt);
        if (ret) {
          fmt = fmt.replace(
            ret[1],
            ret[1].length === 1 ? opt[k] : opt[k].padStart(ret[1].length, "0")
          );
        }
      }
      return fmt;
    },
  },
};
</script>

<style scoped lang="less">
.nav-box {
  padding: 0.1rem;
  background: #ffffff;
  box-shadow: 0rem 0.02rem 0.02rem 0rem rgba(149, 149, 149, 0.25);
  // margin-bottom: 0.1rem;
  .nav-item {
    background: #ededed;
    font-size: 0.12rem;
    color: #424242;
    border-radius: 0.3rem;
    padding: 0.05rem 0.1rem;
    width: 40%;
    text-align: center;
    &.active {
      font-size: 0.12rem;
      color: #ffffff;
      background: #6970af;
    }
  }
}

.el {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2; /* 显示2行文本 */
}

.list {
  .item {
    padding: 0.1rem;
    border-bottom: 0.01rem solid #f5f5f5;
    .img {
      img {
        width: 0.88rem;
        height: 0.6rem;
        // object-fit: contain;
        object-fit: cover;
        border-radius: 0.02rem;
      }
    }

    .txt {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 0.6rem;
      margin-right: 0.15rem;
      .t {
        font-weight: 500;
        font-size: 0.12rem;
      }
      .t1 {
        font-size: 0.1rem;
        color: #9ea3ae;
      }
    }
  }
}

.tab {
  padding: 0 20px 30px;
  .tab-item {
    background: #f1f2f4;
    border-radius: 10px;
    color: #999999;
    height: 60px;
    line-height: 60px;
    text-align: center;
    width: 20%;
    margin-right: 20px;
    &.active {
      background: #e7f1f9;
      color: #014b8d;
    }
  }
}
</style>
