import Vue from 'vue'
import promise from 'es6-promise'
promise.polyfill();
import axios from "axios"
import { Toast } from 'vant';
import store from '../store/index.js'
import { initLang } from '../assets/local'
import router from '../router'


initLang(Vue);

Vue.use(Toast);

var common = {}

if (process.env.NODE_ENV === 'development') {
	// 这里用国际版接口
	common = {

		baseUri: 'https://www.fdlijlk.com/api/vue/',

		baseUri: 'https://www.uixzhbv.icu/api/vue/',
		// baseUri: '/api/vue/',
		imgUrl: 'https://www.fdlijlk.com/api/',
		imgUrls: "https://www.fdlijlk.com", //实名认证上传图片回显
	}
} else {
	common = {
		baseUri: window.location.origin + '/api/vue/',
		imgUrl: window.location.origin + '/api/',
		imgUrls: window.location.origin, //实名认证上传图片回显
	}

}
// console.log('store.getters.getToken', store.getters.getToken)

let headers = {
	token: store.state.tokend,
	account: store.state.account
}

let settings = {
	baseURL: common.baseUri,
	// 加载超时限定5s
	timeout: 10000,
	headers,
}


const server = axios.create(settings);
server.url = common
server.interceptors.request.use(
	config => {
		// axios.defaults.headers['token'] = window.localStorage.getItem("tokend");
		// axios.defaults.headers['account'] = window.localStorage.getItem("account");
		return config
	}, function (error) {

		return Promise.reject(error)
	}
)






// axios.defaults.baseURL = common.baseUri;

server.interceptors.response.use((res) => {
	if (res.data.status != 1) {
		if (res.data.msg) {

			// 充值时间提示 、提现时间提示
			// if (res.data.msg.includes('请于入金时间') || res.data.msg.includes('请于出金时间')) {
			// 	Toast({
			// 		message: Vue.prototype.$formTextTime(res.data.msg),
			// 		duration: 2000
			// 	});
			// 	return
			// }

			// 其他或包含有数字的提示，需要格式化
			if(res.data.msg.includes('请于出金时间')){
				Toast({
					message: Vue.prototype.$t(res.data.msg.replaceAll('请于出金时间',Vue.prototype.$t('请于出金时间')).replaceAll("操作",Vue.prototype.$t('操作'))),
					duration: 2000
				});
				return false
			}
			if(res.data.msg.includes('请于入金时间')){
				Toast({
					message: Vue.prototype.$t(res.data.msg.replaceAll('请于入金时间',Vue.prototype.$t('请于入金时间')).replaceAll("操作",Vue.prototype.$t('操作'))),
					duration: 2000
				});
				return false
			}
			if(res.data.msg.includes('出金最小值')){
				Toast({
					message: Vue.prototype.$t(res.data.msg.replaceAll('出金最小值',Vue.prototype.$t('出金最小值')).replaceAll("元",Vue.prototype.$t('元'))),
					duration: 2000
				});
				return false
			}
			if(res.data.msg.includes('出金最大值')){
				Toast({
					message: Vue.prototype.$t(res.data.msg.replaceAll('出金最大值',Vue.prototype.$t('出金最大值')).replaceAll("元",Vue.prototype.$t('元'))),
					duration: 2000
				});
				return false
			}
			if(res.data.msg.includes('入金最小值')){
				Toast({
					message: Vue.prototype.$t(res.data.msg.replaceAll('入金最小值',Vue.prototype.$t('入金最小值')).replaceAll("元",Vue.prototype.$t('元'))),
					duration: 2000
				});
				return false
			}
			if(res.data.msg.includes('入金最大值')){
				Toast({
					message: Vue.prototype.$t(res.data.msg.replaceAll('入金最大值',Vue.prototype.$t('入金最大值')).replaceAll("元",Vue.prototype.$t('元'))),
					duration: 2000
				});
				return false
			}
			if(res.data.msg.includes('单只新股，只能申购')){
				Toast({
					message: Vue.prototype.$t(res.data.msg.replaceAll('单只新股，只能申购',Vue.prototype.$t('单只新股，只能申购')).replaceAll("次",Vue.prototype.$t('次'))),
					duration: 2000
				});
				return false
			}
			if(res.data.msg.includes('当前账号资金不足')){
				Toast({
					message: Vue.prototype.$t(res.data.msg.replaceAll('当前账号资金不足',Vue.prototype.$t('当前账号资金不足')).replaceAll("元，不能进行申购",Vue.prototype.$t('元，不能进行申购'))),
					duration: 2000
				});
				return false
			}
			if(res.data.msg.includes('最低认购量为')){
				Toast({
					message: Vue.prototype.$t(res.data.msg.replaceAll('最低认购量为',Vue.prototype.$t('最低认购量为')).replaceAll("股",Vue.prototype.$t('股'))),
					duration: 2000
				});
				return false
			}
			if(res.data.msg.includes('当前股票大宗交易最少买入')){
				Toast({
					message: Vue.prototype.$t(res.data.msg.replaceAll('当前股票大宗交易最少买入',Vue.prototype.$t('当前股票大宗交易最少买入')).replaceAll("股",Vue.prototype.$t('股'))),
					duration: 2000
				});
				return false
			}
			if(res.data.msg.includes('当前大宗一天限购')){
				Toast({
					message: Vue.prototype.$t(res.data.msg.replaceAll('当前大宗一天限购',Vue.prototype.$t('当前大宗一天限购')).replaceAll("股",Vue.prototype.$t('股'))),
					duration: 2000
				});
				return false
			}
			if(res.data.msg.includes('日不可卖出')){
				Toast({
					message: Vue.prototype.$t(res.data.msg.replaceAll('日不可卖出',Vue.prototype.$t('日不可卖出'))),
					duration: 2000
				});
				return false
			}
			if(res.data.msg.includes('分钟后才可以卖出')){
				Toast({
					message: Vue.prototype.$t(res.data.msg.replaceAll('分钟后才可以卖出',Vue.prototype.$t('分钟后才可以卖出'))),
					duration: 2000
				});
				return false
			}

			Toast({
				message: Vue.prototype.$t(res.data.msg),
				duration: 2000
			});
		}

	}

	return res.data
}, error => {
	// 此处判断拦截需要处理的错误状态码并处理
	// if (error.response.data.msg) {
	// 	Toast({
	// 		message: error.response.data.msg,
	// 		message: 'error',
	// 		duration: 2000
	// 	});
	// }
	// token 已过期到登录页面
	if (error.response.status == 401) {
		setTimeout(() => {
			let obj = localStorage.getItem('rembInfo')
			localStorage.clear();
			localStorage.setItem("rembInfo", JSON.stringify(obj));
			this.$toPage("/login/login");
		}, 2000)
	}


	return Promise.reject(error)

})


export default server;
