<template>
	<div class="page ">
		<!-- <top-menu :title="$t('home').tab1"></top-menu> -->
		<top-back :title="$t('熱門資訊')"></top-back>
		<!-- <div class="top-fixed">
			<div class="header flex flex-b">
				<div class="tit">資訊</div>
				<div class="icon ss" @click="$toPage('/favorite/search')"></div>
			</div>
		</div> -->
		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<!-- <van-list v-model="loading" :finished="finished" :finished-text="$t('new').a51" :loading-text="$t('new').a"
				@load="onLoad"> -->
			<no-data v-if="!articleList.length"></no-data>
			<div class="news-list" v-if="articleList.length">
				<div class="news-item02" v-for="(item, index) in articleList" :key="index" @click="toNewsDetail(item)" v-if="index==0">
					<img v-if="item.img" :src="item.img" alt="" />
					<img v-else src="../../assets/home/<USER>" alt="" />
					<div class="t">{{ item.title }}</div>
				</div>
				<div class="news-item" v-for="(item, index) in articleList" :key="index" @click="toNewsDetail(item)" v-if="index>0">
					<div class="flex flex-b">
						<div class="flex-1">
							<div class="t">{{ item.title }}</div>
							<div class="time">
								{{ $formatDate("YYYY/MM/DD hh:mm:ss", item.created * 1000) }}
							</div>
						</div>
						<img v-if="item.img" :src="item.img" alt="" />
						<img v-else src="../../assets/home/<USER>" alt="" />
					</div>
				</div>
			</div>
			<!-- </van-list> -->
		</van-pull-refresh>
		<!--点击按钮加载效果组件 -->
		<loading ref="loading" />
		<!-- <tab-bar :current="1"></tab-bar> -->
	</div>
</template>

<script>
	export default {
		name: "newsList",
		props: {},
		data() {
			return {
				readNum: 0,
				isLoading: false,
				loading: false,
				finished: false,
				articleList: [],
				page: 0,
			};
		},
		components: {},
		created() {},
		mounted() {
			this.readData();
			this.getNews();
		},
		computed: {},
		methods: {
			readData() {
				this.$server.post("/user/notice", {
					type: "zar"
				}).then((res) => {
					if (res.status == 1) {
						let list = res.data;
						let length = list.length
						let a
						for (a = 0; a < length; a++) {
							let read = localStorage.getItem("readMsg")
							let oldRead = JSON.parse(read)
							let hasValue = oldRead.id.includes(list[a].id.toString())
							if (!hasValue) {
								this.readNum += 1
							}
						}
					}
				});
			},
			async getConfig() {
				this.$refs.loading.open();
				const res = await this.$server.post("/common/config", {
					type: "zar",
				});
				let val = {};
				res.data.forEach((vo) => {
					val[vo.name] = vo.value;
				});
				this.$refs.loading.close();
				this.$openUrl(val.kefu); //重新获取
			},
			// 下拉刷新
			onRefresh() {
				this.page = 1;
				this.getNews();
			},
			onLoad() {
				// this.page += 1;
				// this.getNews();
			},
			getNews() {
				this.$refs.loading.open();
				this.$server
					.post("/common/newss", {
						exchange: "za",
						lang: "cn",
					})
					.then((res) => {
						this.$refs.loading.close();
						this.isLoading = false; //下拉刷新状态
						this.loading = false;
						let arr = res.data.result;
						// this.articleList = [...this.articleList, ...arr];
						this.articleList = arr;
						// console.log(this.articleList,78787)
						if (arr.length == 0) {
							this.finished = true; //结束列表加载
						}
					});
			},
			toNewsDetail(item) {
				this.$storage.save("newsDetail", item);
				this.$refs.loading.open(); //开启加载

				setTimeout(() => {
					this.$refs.loading.close(); //关闭加载

					this.$toPage("/home/<USER>");
				}, 1000);
			},
		},
	};
</script>

<style scoped lang="less">
	.top-fixed {
		width: 100%;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 999;

		.header {
			height: 0.5rem;
			padding: 0.15rem;
			width: 100%;
			background: #2C2B30;
			position: relative;

			.tit {
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 0.16rem;
				color: #FFFFFF;
			}
		}

	}

	.page {
		padding: 0.6rem 0.12rem 1rem;
	}

	.news-list {
		background: #FFFFFF;
		border-radius: 0.1rem;
		padding: 0.12rem;
		.news-item {
			padding: 0.12rem 0;
			border-bottom: 0.01rem solid #E0E0E0;
			.t {
				margin-top: 0.05rem;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;
				overflow: hidden;
				text-overflow: ellipsis;
				font-family: PingFang SC;
				font-weight: bold;
				font-size: 0.14rem;
				color: #333333;
			}

			.time {
				padding-top: 0.1rem;
				font-family: PingFang SC;
				font-weight: 500;
				font-size: 0.12rem;
				color: #AEAEAE;
			}

			img {
				width: 0.67rem;
				border-radius: 0.04rem;
				margin-left: 0.1rem;
			}
		}
		.news-item02 {
			border-bottom: 0.01rem solid #202020;
			padding-bottom: 0.1rem;
			position: relative;
			.t {
				position: absolute;
				bottom: 0.26rem;
				padding: 0 0.12rem;
				margin-top: 0.05rem;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;
				overflow: hidden;
				text-overflow: ellipsis;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
				color: #FFFFFF;
			}

			img {
				width: 100%;
				border-radius: 0.04rem;
			}
		}
	}
</style>