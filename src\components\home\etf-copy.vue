<template>
	<div class="pages">
		<!-- <top-back title="ETF"></top-back> -->
		<div class="nav-box flex flex-b">
			<div class="nav-item" v-for="(item, index) in typList" :key="index"
				:class="{ active: currmentIndex == item.id }" @click="changeNav(item.id)">
				{{ item.name }}
			</div>
		</div>
		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<van-skeleton title :row="25" :loading="loading">
				<no-data v-if="isShow"></no-data>
				<div class="list">
					<!-- <div class="titles flex">
						<div class="flex-2">股票名称</div>
						<div class="flex-1">現價</div>
						<div class="flex-1">漲跌幅</div>
						<div class="" style="width: 0.4rem;"></div>
					</div> -->
					<div class="item" v-for="(item, index) in chooseList" :key="index" @click="stockDetails(item)">
						<div class="flex">
							<div class="flex flex-2">
								<div class="st" :class="item.changePercent.indexOf('-') > -1 ? 'green-bg' : 'red-bg'">
									{{ item.changePercent.indexOf("-") > -1 ? "賣" : "買" }}
								</div>
								<div>
									<div class="name">{{ item.symbolName }}</div>
									<div class="code">{{ item.systexId }}</div>
								</div>
							</div>
							<div class="price flex-1 red" :class="{ green: item.changePercent.indexOf('-') > -1 }">
								{{ item.price }}</div>
							<div class="red per" :class="{ green: item.changePercent.indexOf('-') > -1 }">
								{{ item.changePercent }}</div>
							<div class="btn">買入</div>
						</div>
					</div>
				</div>
			</van-skeleton>
		</van-pull-refresh>

		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "etf",
		data() {
			return {
				loading: true,
				isShow: false,
				isLoading: false,
				currmentIndex: "taiwan",
				stockObj: {},
				chooseList: [],
				typList: [{
						name: "台灣",
						id: "taiwan"
					},
					{
						name: "中國",
						id: "china"
					},
					{
						name: "美國",
						id: "us"
					},
					{
						name: "亞洲",
						id: "asia"
					},
					{
						name: "全球",
						id: "global"
					},
					{
						name: "其他",
						id: "others"
					},
				],
				timer: null,
			};
		},
		computed: {},
		mounted() {
			this.getNew();
			this.timer = setInterval(() => {
				this.getNew();
			}, 10000);
		},
		destroyed() {
			this.timer && clearInterval(this.timer);
		},
		methods: {
			changeNav(id) {
				this.currmentIndex = id;
				this.$refs.loading.open();
				this.getNew();
			},
			// 下拉刷新
			onRefresh() {
				this.isShow = false;
				this.getNew();
			},
			getNew() {
				this.$server
					.post("/parameter/etf", {
						regionId: this.currmentIndex
					})
					.then((res) => {
						this.isLoading = false;
						this.loading = false;
						this.$refs.loading.close();

						if (res.data && res.data.list.length) {
							this.chooseList = res.data.list;
						}

						if (!this.chooseList.length) {
							this.isShow = true;
						}
					});
			},
			stockDetails(item) {
				this.$toDetail(`/market/stockDetail?symbol=${item.systexId}`, item);
			},
		},
	};
</script>
<style scoped lang="less">
	.pages {
		padding: 0.3rem 0 0.1rem;
		min-height: 100vh;
		position: relative;
	}

	::v-deep .van-skeleton__row {
		background-color: transparent !important;
	}

	::v-deep .van-skeleton__title {
		background-color: transparent !important;
	}

	.nav-box {
		position: fixed;
		top: 0.5rem;
		left: 0;
		width: 100%;
		z-index: 999;
		background-color: #0F1B2B;
		.nav-item {
			padding: 0.12rem 0;
			flex: 1;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 0.13rem;
			color: #FFFFFF;
			text-align: center;
			position: relative;

			&::after {
				content: "";
				width: 30%;
				height: 0.03rem;
				position: absolute;
				bottom: 0;
				left: 50%;
				transform: translateX(-50%);
				background: transparent;
			}
			&.active {
				font-weight: 600;
				color: #4DB2FA;

				&::after {
					background: #4DB2FA;
				}
			}
		}
	}

	.list {
		background: #0F1B2B;
		.titles {
			padding: 0.04rem 0.12rem;
			background: #252525;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 0.12rem;
			color: #646464;
		}

		.item {
			padding: 0.15rem 0.12rem;
			border-bottom: 0.01rem solid #333;

			.st {
				font-size: 0.12rem;
				color: #ffffff;
				// padding: 0.02rem;
				width: 0.18rem;
				height: 0.18rem;
				text-align: center;
				line-height: 0.18rem;
				border-radius: 0.02rem;
				margin-right: 0.1rem;

				&.red-bg {
					background-color: #E61716;
				}

				&.green-bg {
					background-color: #25D322;
				}
			}

			.name {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.15rem;
				color: #FFFFFF;
			}

			.code {
				margin-top: 0.05rem;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 0.12rem;
				color: #646464;
			}

			.price,
			.per {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.17rem;
			}

			.t {
				font-size: 0.12rem;
				color: #9f9fa3;
			}

			.btn {
				margin-left: 0.3rem;
				height: 0.26rem;
				background: #4db2fa;
				border-radius: 0.04rem 0.04rem 0.04rem 0.04rem;
				line-height: 0.26rem;
				padding: 0 0.1rem;
				font-family: FZLanTingHeiT-R-GB, FZLanTingHeiT-R-GB;
				font-weight: 400;
				font-size: 0.12rem;
				color: #FFFFFF;
			}
		}
	}
</style>