<template>
	<div class="page ">
		<top-back title="搜尋"></top-back>
		<div class="cot">
			<div class="top">
				<div class="flex flex-b  search">
					<div class="icon sou animate__animated animate__fadeIn"></div>
					<input class="flex-1" @input="handleInput" type="text" v-model="keyword" placeholder="股票名稱/股票代碼" />
				</div>
			</div>
			<!-- 历史搜索 -->
			<div class="keyword-block" v-if="!!oldKeywordList.length">
				<div class="flex flex-b">
					<div class="keyword-list-header">搜尋歷史</div>
					<div @click="clearHistory">
						<!-- <van-icon name="close" size=".2rem" /> -->
						<div class="icon del animate__animated animate__fadeIn"></div>
					</div>
				</div>
				<div class="keyword">
					<div v-for="(keyword, index) in oldKeywordList" @click="doSearch(keyword)" :key="index">
						{{ keyword }}
					</div>
				</div>
			</div>

			<div class="rm-list" v-show="keyword" v-if="listData.length > 0">
				<!-- 股票列表 -->
				<div class="">
					<!-- <div class="titles flex flex-b">
						<div class="flex-2">名稱</div>
						<div class="flex-1 t-c">價格</div>
						<div class="flex-2 t-c">漲跌</div>
						<div class="flex-2 t-r">漲跌%</div>
					</div> -->
					<div class="list">
						<div class="rm-item flex flex-b" v-for="(item, idx) in listData" :key="idx" @click="$toDetail(`/market/stockDetail?symbol=${item.symbol}`, item)">
							<div class="toph flex">
								<div class="animate__animated animate__fadeIn" @click.stop="optional(item)">
									<div class="icon xz" v-if="item.isZx"></div>
									<div class="icon wxz" v-if="!item.isZx"></div>
								</div>
								<div>
									<div class="flex">
										<div class="name">{{ $t(item.local_name) }}</div>
										<div class="code">{{ $t(item.symbol) }}</div>
									</div>
									<div class="price" :class="Number(item.gain) > 0 ? 'red' : 'green'">{{ $formatMoney(item.price) || 0 }}
									</div>
								</div>
							</div>
							<div>
								<!-- <div class="icon animate__animated animate__fadeIn" :class="Number(item.gain) > 0 ? 'up' : 'down'"></div> -->
								<!-- <div class="per" :class="{ green: Number(item.gain) < 0, }">{{ $formatMoney(item.gainValue) || "-" }}</div> -->
								<div class="icon" :class="Number(item.gain)>0?'redLine':'greenLine'"></div>
								<div class="per" :class="Number(item.gain) > 0 ? 'red' : 'green'">{{ item.gain }}%</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "search",
		props: {},
		data() {
			return {
				currmentIndex: 0,
				keyword: "",
				imgList: {
					search: "",
					delete: "",
					hook: "",
					jia: "",
				},
				oldKeywordList: [],
				listData: [],
				flag: true,
				account: "",
			};
		},
		components: {},
		created() {
			this.account = this.$storage.get("account");
			this.oldKeywordList = !!this.$storage.get(this.account + "searchHistory") ?
				this.$storage.get(this.account + "searchHistory") : [];
		},
		mounted() {
			// 使用 lodash 的防抖函数
			this.handleInput = _.debounce(this.handleInput, 1000);
		},
		methods: {
			handleInput() {
				this.searchFn();
			},
			changePage(index) {
				this.currmentIndex = index;
			},
			optional(obj) {
				this.$refs.loading.open(); //开启加载
				if (!obj.isZx) {
					this.$server
						.post("/user/addOptional", {
							type: "twd",
							symbol: obj.symbol
						})
						.then((res) => {
							this.$refs.loading.close();
							if (res.status == 1) {
								this.listData = this.listData.map((item) => {
									if (item.symbol == obj.symbol) {
										item.isZx = true;
									}
									return item;
								});
							}
						});
				} else {
					this.$server
						.post("/user/removeOptional", {
							type: "twd",
							symbol: obj.symbol
						})
						.then((res) => {
							this.$refs.loading.close();
							if (res.status == 1) {
								this.listData = this.listData.map((item) => {
									item.isZx = false;
									return item;
								});
							}
						});
				}
			},
			getMine() {
				this.$server.post("/user/Optionallist", {
					type: "twd"
				}).then((res) => {
					if (res.status == 1) {
						// 判断当前是否在自选列表里面
						let arr = this.listData.map((item) => {
							res.data.forEach((it) => {
								if (item.symbol == it.symbol) {
									item.isZx = true;
								}
							});
							return item;
						});
						this.listData = arr;
					}
				});
			},
			doSearch(str) {
				this.keyword = str;
				this.searchFn();
			},
			clearHistory() {
				this.oldKeywordList = [];
				this.$storage.remove(this.account + "searchHistory");
			},
			searchFn() {
				let that = this;
				this.$refs.loading.open(); //开启加载

				this.$server
					.post("/trade/search", {
						type: "twd",
						content: this.keyword
					})
					.then((res) => {
						this.$refs.loading.close();

						if (res.status == 1) {
							if (res.data.length == 1) {
								let searchHistory = this.oldKeywordList;
								if (searchHistory.length > 10) {
									searchHistory.shift();
								}
								if (!searchHistory.includes(this.keyword)) {
									searchHistory.push(this.keyword);
								}

								this.$storage.save(this.account + "searchHistory", searchHistory);

								this.oldKeywordList = searchHistory;
							}
							this.listData = [];
							if (res.data.length > 0) {
								this.listData = res.data;
								// 判断是否自选
								this.getMine();
							}
						}
					});
			},
		},
	};
</script>

<style scoped lang="less">
	.rm-list {
		margin: 0 0.12rem;
		background: #232429;
		border-radius: 0.19rem;
		padding: 0.12rem;

		.titles {
			padding-bottom: 0.1rem;
			div {
				font-family: PingFang SC;
				font-weight: 500;
				font-size: 0.12rem;
				color: #8B8B8B;
			}

			.icon {
				margin-left: 0.05rem;
			}
		}

		.rm-item {
			position: relative;
			padding: 0.12rem 0;
			.xz,
			.wxz {
				width: 0.15rem;
				height: 0.15rem;
				margin-right: 0.1rem;
			}

			.toph {
				.name {
					font-family: PingFang SC;
					font-weight: 500;
					font-size: 0.14rem;
					color: #999999;
				}

				.code {
					margin-left: 0.05rem;
					font-family: PingFang SC;
					font-weight: 500;
					font-size: 0.14rem;
					color: #999999;
				}
			}

			.price {
				margin-top: 0.05rem;
				font-family: PingFang SC;
				font-weight: 500;
				font-size: 0.14rem;
			}

			.per {
				text-align: center;
				margin-top:-0.25rem;
				font-family: PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
			}
		}
	}

	.page {
		padding: 0.5rem 0 0.1rem;
		min-height: 100vh;
	}

	.cot {
		.top {
			padding: 0.12rem;
			.search {
				height: 0.32rem;
				background: #434446;
				border-radius: 0.38rem;
				padding: 0 0.1rem;

				.sou {
					width: 0.16rem;
					height: 0.16rem;
				}

				input {
					line-height: 0.3rem;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.13rem;
					color: #fff;
					margin-left: 0.05rem;

					&::placeholder {
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 0.13rem;
						color: #888888;
					}
				}
			}
		}

		.keyword-block {
			padding: 0.12rem;

			.keyword-list-header {
				font-size: 0.14rem;
				font-weight: 500;
				color: #fff;
			}

			.keyword {
				display: flex;
				flex-flow: wrap;
				align-items: center;
				padding: 0.1rem 0;

				div {
					background: #2D2D2D;
					border-radius: 0.06rem;
					display: flex;
					align-items: center;
					justify-content: center;
					padding: 0.05rem 0.1rem;
					font-size: 0.12rem;
					color: #fff;
					margin: 0 0.1rem 0.1rem 0;
				}
			}
		}
	}

	.box {
		.titles {

			// padding: 0.1rem 0 0;
			div {
				font-size: 0.12rem;
				color: #535353;
			}
		}

		.list {
			.list-item {
				padding: 0.1rem 0;
				border-bottom: 0.01rem solid #f4f4f4;

				.name {
					font-size: 0.12rem;
					color: #000000;
				}

				.code {
					font-size: 0.1rem;
					color: #c4c4c4;
				}

				.price {
					font-size: 0.12rem;
					color: #0c0c0c;
					text-align: center;
				}

				.per {
					margin-right: 0.05rem;

					.t {
						font-size: 0.12rem;
						color: #0c0c0c;

						&.t1 {
							margin-left: 0.05rem;
						}
					}
				}
			}
		}

		.red {
			color: #c04649;
		}

		.green {
			color: #4f8672;
		}
	}
</style>