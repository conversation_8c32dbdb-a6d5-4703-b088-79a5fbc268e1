// 批量国际化处理脚本
// 这个脚本用于快速识别和处理Vue文件中的中文文字

const fs = require('fs');
const path = require('path');

// 需要处理的页面列表
const pagesToProcess = [
  'src/components/information/authInfo.vue',
  'src/components/information/bankList.vue', 
  'src/components/information/cashOut.vue',
  'src/components/information/fundRecord.vue',
  'src/components/information/recharge.vue',
  'src/components/market/stockDetail.vue',
  'src/components/market/Plate.vue',
  'src/components/market/Plate1.vue',
  'src/components/favorite/index.vue',
  'src/components/favorite/search.vue',
  'src/components/favorite/moreList.vue',
  'src/components/home/<USER>',
  'src/components/home/<USER>',
  'src/components/home/<USER>',
  'src/components/home/<USER>',
  'src/components/components/top-back.vue',
  'src/components/components/top-menu.vue',
  'src/components/components/tab-bar.vue',
  'src/components/components/no-data.vue'
];

// 常见的中文文字模式
const chineseTextPatterns = [
  // 模板中的中文文字
  />\s*([^<]*[\u4e00-\u9fff][^<]*)\s*</g,
  // placeholder中的中文
  /placeholder\s*=\s*["']([^"']*[\u4e00-\u9fff][^"']*)["']/g,
  // title中的中文
  /title\s*=\s*["']([^"']*[\u4e00-\u9fff][^"']*)["']/g,
  // JavaScript字符串中的中文
  /["']([^"']*[\u4e00-\u9fff][^"']*)["']/g
];

// 需要添加到语言包的翻译键值对
const translationsToAdd = {
  // 个人中心相关
  '個人資料': {
    af: 'Persoonlike Inligting',
    en: 'Personal Information',
    zu: 'Ulwazi Lomuntu Siqu',
    xh: 'Ulwazi Lomntu'
  },
  '真實姓名': {
    af: 'Werklike Naam',
    en: 'Real Name', 
    zu: 'Igama Langempela',
    xh: 'Igama Lokwenyani'
  },
  '身分證號': {
    af: 'ID Nommer',
    en: 'ID Number',
    zu: 'Inombolo Yesazisi',
    xh: 'Inombolo Yesazisi'
  },
  '實名認證': {
    af: 'Werklike Naam Verifikasie',
    en: 'Real Name Verification',
    zu: 'Ukuqinisekisa Igama Langempela',
    xh: 'Ukuqinisekisa Igama Lokwenyani'
  },
  '已實名': {
    af: 'Geverifieer',
    en: 'Verified',
    zu: 'Kuqinisekisiwe',
    xh: 'Kuqinisekisiwe'
  },
  '審核中': {
    af: 'Onder Hersiening',
    en: 'Under Review',
    zu: 'Kubuyekezwa',
    xh: 'Kuphononongwa'
  },
  
  // 银行卡管理
  '銀行卡管理': {
    af: 'Bank Kaart Bestuur',
    en: 'Bank Card Management',
    zu: 'Ukuphatha Ikhadi Lebhange',
    xh: 'Ulawulo Lwekhadi Lebhanki'
  },
  '添加銀行卡': {
    af: 'Voeg Bank Kaart By',
    en: 'Add Bank Card',
    zu: 'Engeza Ikhadi Lebhange',
    xh: 'Yongeza Ikhadi Lebhanki'
  },
  '卡號': {
    af: 'Kaart Nommer',
    en: 'Card Number',
    zu: 'Inombolo Yekhadi',
    xh: 'Inombolo Yekhadi'
  },
  '持卡人': {
    af: 'Kaart Houer',
    en: 'Card Holder',
    zu: 'Umphathi Wekhadi',
    xh: 'Umnini Wekhadi'
  },
  '開戶行': {
    af: 'Bank Tak',
    en: 'Bank Branch',
    zu: 'Igatsha Lebhange',
    xh: 'Isebe Lebhanki'
  },
  
  // 资金相关
  '資金提現': {
    af: 'Fondse Onttrekking',
    en: 'Fund Withdrawal',
    zu: 'Ukukhipha Izimali',
    xh: 'Ukurhoxisa Imali'
  },
  '提現金額': {
    af: 'Onttrekking Bedrag',
    en: 'Withdrawal Amount',
    zu: 'Inani Lokukhipha',
    xh: 'Imali Yokurhoxisa'
  },
  '手續費': {
    af: 'Hantering Fooi',
    en: 'Handling Fee',
    zu: 'Imali Yokusebenza',
    xh: 'Umrhumo Wokusebenza'
  },
  '到賬金額': {
    af: 'Ontvang Bedrag',
    en: 'Received Amount',
    zu: 'Inani Elifinyelelayo',
    xh: 'Imali Efunyenweyo'
  },
  
  // 交易明细
  '交易明細': {
    af: 'Handel Besonderhede',
    en: 'Transaction Details',
    zu: 'Imininingwane Yokuhweba',
    xh: 'Iinkcukacha Zorhwebo'
  },
  '收入': {
    af: 'Inkomste',
    en: 'Income',
    zu: 'Imali Engenayo',
    xh: 'Ingeniso'
  },
  '支出': {
    af: 'Uitgawe',
    en: 'Expense',
    zu: 'Imali Ephumayo',
    xh: 'Inkcitho'
  },
  '餘額': {
    af: 'Balans',
    en: 'Balance',
    zu: 'Ibhalansi',
    xh: 'Ibhalansi'
  },
  
  // 股票相关
  '股票詳情': {
    af: 'Aandeel Besonderhede',
    en: 'Stock Details',
    zu: 'Imininingwane Yesitoko',
    xh: 'Iinkcukacha Zesitoko'
  },
  '買入': {
    af: 'Koop',
    en: 'Buy',
    zu: 'Thenga',
    xh: 'Thenga'
  },
  '賣出': {
    af: 'Verkoop',
    en: 'Sell',
    zu: 'Thengisa',
    xh: 'Thengisa'
  },
  '漲': {
    af: 'Styg',
    en: 'Rise',
    zu: 'Khuphuka',
    xh: 'Nyuka'
  },
  '跌': {
    af: 'Daal',
    en: 'Fall',
    zu: 'Wehla',
    xh: 'Wehla'
  },
  '開盤': {
    af: 'Opening',
    en: 'Open',
    zu: 'Vula',
    xh: 'Vula'
  },
  '收盤': {
    af: 'Closing',
    en: 'Close',
    zu: 'Vala',
    xh: 'Vala'
  },
  '最高': {
    af: 'Hoogste',
    en: 'High',
    zu: 'Okuphezulu',
    xh: 'Okuphezulu'
  },
  '最低': {
    af: 'Laagste',
    en: 'Low',
    zu: 'Okuphansi',
    xh: 'Okuphantsi'
  },
  '成交量': {
    af: 'Volume',
    en: 'Volume',
    zu: 'Ivolumu',
    xh: 'Umthamo'
  },
  
  // 搜索相关
  '搜索': {
    af: 'Soek',
    en: 'Search',
    zu: 'Sesha',
    xh: 'Khangela'
  },
  '搜索股票': {
    af: 'Soek Aandele',
    en: 'Search Stocks',
    zu: 'Sesha Izitoko',
    xh: 'Khangela Izitoko'
  },
  '熱門搜索': {
    af: 'Gewilde Soektogte',
    en: 'Popular Searches',
    zu: 'Okusesha Okuthandwayo',
    xh: 'Okukhangela Okuthandwayo'
  },
  
  // 通用按钮和操作
  '確認': {
    af: 'Bevestig',
    en: 'Confirm',
    zu: 'Qinisekisa',
    xh: 'Qinisekisa'
  },
  '取消': {
    af: 'Kanselleer',
    en: 'Cancel',
    zu: 'Khansela',
    xh: 'Rhoxisa'
  },
  '提交': {
    af: 'Indien',
    en: 'Submit',
    zu: 'Thumela',
    xh: 'Ngenisa'
  },
  '保存': {
    af: 'Stoor',
    en: 'Save',
    zu: 'Gcina',
    xh: 'Gcina'
  },
  '刪除': {
    af: 'Verwyder',
    en: 'Delete',
    zu: 'Susa',
    xh: 'Cima'
  },
  '編輯': {
    af: 'Wysig',
    en: 'Edit',
    zu: 'Hlela',
    xh: 'Hlela'
  },
  '返回': {
    af: 'Terug',
    en: 'Back',
    zu: 'Buyela',
    xh: 'Buyela'
  },
  '下一步': {
    af: 'Volgende',
    en: 'Next',
    zu: 'Okulandelayo',
    xh: 'Okulandelayo'
  },
  '上一步': {
    af: 'Vorige',
    en: 'Previous',
    zu: 'Okwangaphambili',
    xh: 'Okwangaphambili'
  },
  '完成': {
    af: 'Voltooi',
    en: 'Complete',
    zu: 'Qeda',
    xh: 'Gqiba'
  },
  
  // 状态相关
  '成功': {
    af: 'Suksesvol',
    en: 'Success',
    zu: 'Kuphumelele',
    xh: 'Impumelelo'
  },
  '失敗': {
    af: 'Misluk',
    en: 'Failed',
    zu: 'Kuhlulekile',
    xh: 'Ayiphumelelanga'
  },
  '處理中': {
    af: 'Verwerk',
    en: 'Processing',
    zu: 'Kucutshungulwa',
    xh: 'Kuyasenziwa'
  },
  '等待中': {
    af: 'Wag',
    en: 'Waiting',
    zu: 'Kulindile',
    xh: 'Kulindile'
  },
  
  // 时间相关
  '今日': {
    af: 'Vandag',
    en: 'Today',
    zu: 'Namuhla',
    xh: 'Namhlanje'
  },
  '昨日': {
    af: 'Gister',
    en: 'Yesterday',
    zu: 'Izolo',
    xh: 'Izolo'
  },
  '本週': {
    af: 'Hierdie Week',
    en: 'This Week',
    zu: 'Kuleli Viki',
    xh: 'Kule Veki'
  },
  '本月': {
    af: 'Hierdie Maand',
    en: 'This Month',
    zu: 'Kuleli Nyanga',
    xh: 'Kule Nyanga'
  }
};

console.log('批量国际化处理脚本已准备就绪');
console.log('需要处理的页面数量:', pagesToProcess.length);
console.log('预定义翻译数量:', Object.keys(translationsToAdd).length);
