<template>
	<div class="bottomMenu">
		<ul class="nav flex flex-b">
			<li v-for="(item, idx) in navData" :key="idx">
				<router-link :to="item.router">
					<div class="img flex flex-c">
						<img :src="require('../../assets/v2/tab/'+item.className+ 'A.png')" v-if="on==idx" />
						<img :src="require('../../assets/v2/tab/'+item.className+ '.png')" v-else />
					</div>
					{{ $t('menu')[item.name] }}
				</router-link>
			</li>
		</ul>
	</div>
</template>

<script>
	import Vue from "vue";
	import qs from "qs";
	import axios from "axios";
	import {
		Toast
	} from "vant";
	Vue.use(Toast);
	export default {
		props: {
			on: {
				type: Number,
				default: 0
			}
		},
		data() {
			return {
				navData: [{
						id: 1,
						className: "h",
						name: "txt1",
						router: {
							path: "/home/<USER>"
						},
					},
					{
						id: 2,
						className: "hq",
						name: "txt3", //行情
						router: {
							path: "/market/market"
						},
					},
					{
						id: 3,
						className: "jy",
						name: "txt2", //交易
						router: {
							path: "/position/position"
						},
					},
					{
						id: 4,
						className: "zj",
						name: "txt4", //资金记录
						router: {
							path: "/user/FundingDetails"
						},
					},
					// {
					// 	id: 4,
					// 	className: "cc",
					// 	name: "txt4",//跟单
					// 	router: {
					// 		path: "/gdindex/gdindex"
					// 	},
					// },
					// {
					// 	id: 5,
					// 	className: "user",
					// 	name: "txt5",//我的
					// 	router: {
					// 		path: "/user/usercenter"
					// 	},
					// },
					{
						id: 5,
						className: "xw",
						name: "txt6", //我的
						router: {
							path: "/home/<USER>"
						},
					},
				],
			};
		},
		computed: {},
		methods: {},
		mounted() {},
		updated() {
			console.log(111);
		},
	};
</script>

<style type="text/css" lang="less" scoped="scoped">
	.bottomMenu {
		.nav {
			background: #424E4E;
			box-shadow: 0rem 0rem 0.04rem 0rem rgba(0,0,0,0.1);
			height: 0.6rem;
			font-size: 0;
			position: fixed;
			left: 0;
			bottom: 0;
			width: 100%;
			z-index: 22;

			li {
				width: 20%;
				text-align: center;
				font-size: 0.12rem;
				display: inline-block;
				position: relative;

				a {
					display: block;
					color: #B6B6B6;
					font-size: 0.14rem;
					line-height: .13rem;
					position: relative;

					&.navActive {
						color: #5ED5A8;
					}

					.img {
						width: .24rem;
						height: .24rem;
						margin: 0 auto .02rem auto;
						margin-bottom: 0.08rem;
					}

					img {
						height: .23rem;
						display: block;
					}
				}
			}
		}

		.h50 {
			height: 0.57rem;
			width: 100%;
		}
	}
</style>