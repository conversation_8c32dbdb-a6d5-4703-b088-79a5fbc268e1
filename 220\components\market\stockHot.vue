<template>
	<div class="stockHot" @scroll="scrollList">
		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3" :loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<div class="topThree flex flex-b">
				<div class="item text-center" :class="{'red':item.gain>=0,'green':item.gain<0}" v-for="(item,idx) in topThree" :key="idx">
					<div class="name flex flex-b">
						<div class="txt">{{item.name}}</div>
						<span>{{item.symbol}}</span>
					</div>
					<div class="price">{{$formatMoney(item.price)}}</div>
					<div class="per flex flex-c">
						<img src="../../assets/skin/home/<USER>" v-if="item.gain>=0" />
						<img src="../../assets/skin/home/<USER>" v-else />
						<span class="mr-20">{{Number(item.gainValue).toFixed(2)}}円</span>
						<span>{{Number(item.gain).toFixed(2)}}%</span>
					</div>
				</div>
			</div>
			
			
			<div class="list">
				<div class="listTitle flex flex-b">
					<div class="flex-1">{{$t('market').stockHot.ti1}}</div>
					<div class="flex-1 text-center">{{$t('market').stockHot.ti2}}</div>
					<div class="flex-1 text-center">{{$t('market').stockHot.ti3}}</div>
					<div style="width: 10%;"></div>
				</div>
				<div class="itemBox">
					<div class="item flex flex-b" v-for="(item,idx) in list" :key="idx" @click="clickDetail(item)">
						<div class="order flex flex-c">{{Number(idx)+1}}</div>
						<div class="name flex-1">
							<div>{{item.name}}</div>
							<span>{{item.symbol}}</span>
						</div>
						<div class="price flex-1 text-center" :class="{'red':item.gain>=0,'green':item.gain<0}">{{item.price}}円</div>
						<div class="per flex-1 text-center">
							<div class="flex flex-c" :class="{'red':item.gain>=0,'green':item.gain<0}">{{ item.gain < 0 ? '' : '+' }}{{ item.gain.toFixed(2) }}%</div>
						</div>
						<div class="favo text-right">
							<img src="../../assets/skin/market/favo2.png" />
						</div>
					</div>
				</div>
			</div>
		</van-pull-refresh>
	</div>
</template>

<script>
	import Vue from 'vue';
	import qs from 'qs';
	import axios from 'axios';
	import {
		Toast
	} from 'vant';
	Vue.use(Toast);
	export default {
		name: "stockHot",
		data() {
			return {
				loading: false,
				loading2: true,
				isLoading: false,
				finished: false,
				
				isShow: false,
				list: [],
				topThree:[],
				page:1,
				
				
			}
		},
		components: {
			
		},
		methods: {
			// 下拉刷新
			onRefresh() {
				this.list = [];
				this.isShow = false;
				this.page = 1;
				//this.getTopFive();
				this.getInfo();
			},
			onLoad() {
				console.log(111)
				this.page += 1;
				this.getInfo();
			},
			
			getInfo() {
				this.$server.post('/riben/allstocks', { 
					page: this.page,
					size: 20,
					market: 'bottom'
				}).then(res => {
					this.isLoading = false; //下拉刷新状态
					this.loading2 = false;
					if (res.data.status == 1) {
						let arr = [];
						let top = [];
						res.data.data.forEach((item, i) => {
							if(this.page==1&&i<3){
								top.push(item);
							}else{
								arr.push(item);
							}
						});
						if (res.data.data.length == 0) {
							this.finished = false;
						}
						this.list = [...this.list,...arr];
						this.topThree = top;
						if (!this.list.length) {
							this.isShow = true;
						}
					}
				});
			},
			clickDetail(item){
				var link = "/market/marketDetail?symbol="+item.code;
				this.clickNext(link);
			},
			scrollList(event){
				console.log(event)
			}
			
			
		},
		destroyed() {
			if(this.timeInit){
				clearInterval(this.timeInit)
			}
		},
		mounted() {
			var _this = this;
			this.getInfo();
			/* this.timeInit = setInterval(function(){
				_this.getInfo();
			},5000) */
			
			
		},
	}
</script>

<style lang="less">
	.stockHot{
		margin:.12rem;
		background: #FFFFFF;
		border-radius: .1rem;
		.topThree{
			margin-bottom: .3rem;
			.item{
				width:32%;
				height: .7rem;
				border-radius: .05rem;
				&.red{
					background: #F7E8E8;
				}
				&.green{
					background: #D8F8E7;
				}
				.name{
					font-size:.11rem;color:#000;
					margin:.05rem .08rem;
					span{font-size:.09rem;color:rgba(0,0,0,.6);}
					.txt{
						width: 100%;overflow: hidden;white-space: nowrap;
						text-overflow: ellipsis;
					}
				}
				.price{font-size:.2rem;}
				.per{
					font-size:.1rem;
					img{width:.08rem;margin-right:.03rem;}
				}
			}
		}
		
		.list{
			.listTitle{
				font-weight: 500;
				font-size: .12rem;
				color: #999999;
				margin-bottom: .15rem;
				div:first-child{
					margin-left:.2rem;
				}
			}
			.itemBox{
				.item{
					align-items: flex-start;
					margin-bottom: .2rem;
					&:last-child{margin-bottom:0;}
					.order{
						width: .13rem;
						height: .13rem;
						border-radius: 50%;
						font-weight: bold;
						font-size: .12rem;
						color: #999999;
						margin-right:.07rem;margin-top:.02rem;
					}
					.favo{
						width: 10%;
						img{width:.13rem;}
					}
					.name{
						font-weight: bold;
						font-size: .14rem;
						color: #333333;
						span{
							font-weight: 400;
							font-size: .1rem;
							color: #999999;
						}
					}
					.price{
						font-weight: 400;
						font-size: .14rem;
					}
					.per{
						div{
							display:inline-flex;
							height: .25rem;
							border-radius: .03rem;
							font-weight: 400;
							font-size: .12rem;
							color: #FFFFFF;
							padding:0 .1rem;
						}
						.red{background: #FD2222;}
						.green{background: #00AA70;}
					}
					&:nth-child(1){
						.order{background: #333333;color:#fff;}
					}
					&:nth-child(2){
						.order{background: #333333;color:#fff;}
					}
					&:nth-child(3){
						.order{background: #333333;color:#fff;}
					}
				}
			}
		}
		.more{
			font-weight: 500;
			font-size: .12rem;
			color: #999999;
			padding:.1rem 0;
		}
	}
</style>