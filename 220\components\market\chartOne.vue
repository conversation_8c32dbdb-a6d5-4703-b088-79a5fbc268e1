<template>
	<div class="chart-wrapper">
		<div class="xinat">
			<a :class="{ xuan: knum == 1 }" @click="choose(1, '1m')">{{ $t("分时") }}</a>
			<a :class="{ xuan: knum == 2 }" @click="choose(2, 'd')">{{ $t("日线") }}</a>
			<a :class="{ xuan: knum == 3 }" @click="choose(3, 'w')">{{ $t("周线") }}</a>
			<a :class="{ xuan: knum == 4 }" @click="choose(4, 'm')">{{ $t("月线") }}</a>

			<a :class="{ xuan: knum == 5 }" @click="choose(5, '1m')">1{{ $t("分") }}</a>
			<a :class="{ xuan: knum == 6 }" @click="choose(6, '5m')">5{{ $t("分") }}</a>

			<span class="btn-set" v-on:click="onSet">
				<van-icon size="20" name="setting-o" />
			</span>

			<div class="set-group" v-if="isSetting">
				<div :class="{ xuan: fuquan == 0 }" @click="onFuquan(0)">{{ $t("不复权") }}</div>
				<div :class="{ xuan: fuquan == 1 }" @click="onFuquan(1)">{{ $t("前复权") }}</div>
				<div :class="{ xuan: fuquan == 2 }" @click="onFuquan(2)">{{ $t("后复权") }}</div>

				<div :class="{ xuan1: id1 == 'MA' }" @click="onId1('MA')">MA</div>
				<div :class="{ xuan1: id1 == 'BOLL' }" @click="onId1('BOLL')">BOLL</div>

				<div :class="{ xuan2: id2 == 'VOL' }" @click="onId2('VOL')">VOL</div>
				<div :class="{ xuan2: id2 == 'MACD' }" @click="onId2('MACD')">MACD</div>
				<div :class="{ xuan2: id2 == 'KDJ' }" @click="onId2('KDJ')">KDJ</div>
				<div :class="{ xuan2: id2 == 'ATR' }" @click="onId2('ATR')">ATR</div>
			</div>
		</div>

		<div :class="isFullScreen ? 'chart-view fullscreen' : 'chart-view'">
			<div id="minuteChart" ref="minute" v-show="Minute.IsShow"></div>
			<div id="kline" ref="kline" v-show="Kline.IsShow"></div>

			<div :class="isFullScreen?'h-icon-group':'icon-group'">
				<div class="icon-space" v-if="Kline.IsShow" @click="onMinus">
					<van-icon size="12" name="minus" color="white" />
				</div>
				<div class="icon-space" v-if="Kline.IsShow" @click="onPlus">
					<van-icon size="12" name="plus" color="white" />
				</div>
				<div class="icon-space" @click="onFullScreen">
					<van-icon size="12" name="expand-o" color="white" v-if="!isFullScreen" />
					<van-icon size="13" name="shrink" color="white" v-if="isFullScreen" />
				</div>
			</div>

			<div class="h-peroid" v-if="isFullScreen">
				<a :class="{ xuan: knum == 1 }" @click="choose(1, '1m')"><span>{{ $t("分时") }}</span></a>
				<a :class="{ xuan: knum == 2 }" @click="choose(2, 'd')"><span>{{ $t("日线") }}</span></a>
				<a :class="{ xuan: knum == 3 }" @click="choose(3, 'w')"><span>{{ $t("周线") }}</span></a>
				<a :class="{ xuan: knum == 4 }" @click="choose(4, 'm')"><span>{{ $t("月线") }}</span></a>

				<a :class="{ xuan: knum == 5 }" @click="choose(5, '1m')"><span>1{{ $t("分") }}</span></a>
				<a :class="{ xuan: knum == 6 }" @click="choose(6, '5m')"><span>5{{ $t("分") }}</span></a>
				<a :class="{ xuan: knum == 7 }" @click="choose(7, '5m')"><span>30{{ $t("分") }}</span></a>
			</div>

			<div class="h-set-group" v-if="isFullScreen">
				<div :class="{ xuan: fuquan == 0 }" @click="onFuquan(0)"><span>{{ $t("不复权") }}</span></div>
				<div :class="{ xuan: fuquan == 1 }" @click="onFuquan(1)"><span>{{ $t("前复权") }}</span></div>
				<div :class="{ xuan: fuquan == 2 }" @click="onFuquan(2)"><span>{{ $t("后复权") }}</span></div>

				<div :class="{ xuan1: id1 == 'MA' }" @click="onId1('MA')"><span>MA</span></div>
				<div :class="{ xuan1: id1 == 'BOLL' }" @click="onId1('BOLL')"><span>BOLL</span></div>

				<div :class="{ xuan2: id2 == 'VOL' }" @click="onId2('VOL')"><span>VOL</span></div>
				<div :class="{ xuan2: id2 == 'MACD' }" @click="onId2('MACD')"><span>MACD</span></div>
				<div :class="{ xuan2: id2 == 'KDJ' }" @click="onId2('KDJ')"><span>KDJ</span></div>
				<div :class="{ xuan2: id2 == 'ATR' }" @click="onId2('ATR')"><span>ATR</span></div>
			</div>
		</div>
	</div>
</template>


<script type="text/javascript">
	import Vue from "vue";
	import qs from "qs";
	
	import {
		Toast,
		Popup,
		Icon
	} from 'vant';

	Vue.use(Toast).use(Popup).use(Icon);
	import HQChart from "hqchart";
	//import "hqchart/src/jscommon/umychart.resource/css/tools.css";
	//import "hqchart/src/jscommon/umychart.resource/font/iconfont.css";
	import axios from "axios";

	function timestampToTime(timestamp) {
		var date = new Date(timestamp * 1000); //时间戳为10位需*1000，时间戳为13位的话不需乘1000
		var Y = date.getFullYear();
		var M =
			date.getMonth() + 1 < 10 ?
			"0" + (date.getMonth() + 1) :
			date.getMonth() + 1;
		var D =
			date.getDate() + 1 < 10 ? "0" + (date.getDate() + 1) : date.getDate() + 1;
		var h =
			date.getHours() + 1 < 10 ?
			"0" + (date.getHours() + 1) :
			date.getHours() + 1;
		var m =
			date.getMinutes() + 1 < 10 ?
			"0" + (date.getMinutes() + 1) :
			date.getMinutes() + 1;
		var s =
			date.getSeconds() + 1 < 10 ?
			"0" + (date.getSeconds() + 1) :
			date.getSeconds() + 1;
		return String(h) + String(m) + String(s);
	}

	function NetworkFilter(data, callback) {
		//网络协议回调
		data.PreventDefault = true; //设置true不请求数据
		let date = new Date();
		let seperator1 = "-"; //格式分隔符
		let year = date.getFullYear(); //获取完整的年份(4位)
		let month = date.getMonth() + 1; //获取当前月份(0-11,0代表1月)
		let strDate = date.getDate(); // 获取当前日(1-31)
		if (month >= 1 && month <= 9) month = "0" + month; // 如果月份是个位数，在前面补0
		if (strDate >= 0 && strDate <= 9) strDate = "0" + strDate; // 如果日是个位数，在前面补0
		let {
			period
		} = data.Request.Data;
		let timer = year + "" + month + "" + strDate;
		console.log("shit请求", data, period);

		switch (data.Name) {
			case "MinuteChartContainer::RequestMinuteData": //分钟走势图
				//通过data.Request里的数据 对应到第3方数据
				//console.log(this.$props.message)
				this.$server
					.post("/common/stockdetails", {
						symbol: msg,
					})
					.then(async function(str) {
						let dataObj = {};
						this.$server
							.post("/common/kline", {
								symbol: msg,
								type: "1m",
							})
							.then(async function(str2) {
								if (str2) {
									let arr = [];
									for (let i = 0; i < str2.data.length; i++) {
										arr.push({
											price: str2.data[i].price, //价格
											open: str2.data[i].open, //这1分钟的开盘价
											high: str2.data[i].high, //最高
											low: str2.data[i].low, //最低
											vol: str2.data[i].volumn, //成交量
											amount: str2.data[i].price, //成交金额
											time: timestampToTime(str2.data[i]
											.time), //交易时间 格式：hhmm 如930 就是9：30，
											avprice: str2.data[i].avgPrice, //均价
										});
									}

									dataObj = {
										stock: [{
											date: timer, //交易日期 格式yyyymmdd 如 20190829 => 2019-08-29
											price: str.data[0].price, //最新价/收盘价
											open: str.data[0].open, //当日开盘价
											yclose: str.data[0].preClose, //昨收
											high: str.data[0].high, //当日最高价
											low: str.data[0].low, //当日最低价
											vol: str.data[0].volume, //当日成交量
											amount: str.data[0].Amount, //当日成交金额
											symbol: "600000.sh", //股票代码
											name: str.data[0].symbolName, //股票名称
											minute: arr, //分钟数据
										}, ],
									};
									callback(dataObj);
								}
							});
					});
				break;
			case "KLineChartContainer::RequestHistoryData": //分钟全量数据下载
			case "KLineChartContainer::ReqeustHistoryMinuteData":
				//通过data.Request里的数据 对应到第3方数据
				this.$server
					.post("/common/stockdetails", {
						symbol: msg,
					})
					.then(async function(str) {
						let dataObj = {};
						this.$server
							.post("/common/kline", {
								symbol: msg,
								type: period > 2 ? "1m" : "d",
							})
							.then(async function(str2) {
								if (str2) {
									let arr = [];
									for (let i = 0; i < str2.data.length; i++) {
										arr.push([
											timestampToTime(str2.data[i].time), //日期 date
											str2.data[i].open, //str2.data[i].yclose,//前收盘价 yclose
											str2.data[i].open, //开盘价 open
											str2.data[i].high, //最高 high
											str2.data[i].low, //最低 low
											str2.data[i].close, //收盘价 close
											str2.data[i].volumn, //成交量 vol
											Number(str2.data[i].volumn) * Number(str2.data[i]
												.close), //str2.data[i].amount//成交金额 amount
										]);
									}
									dataObj = {
										data: arr,
										symbol: "600000.sh", //股票代码
										name: str.data[0].symbolName, //股票名称
									};
									callback(dataObj);
								}
							});
					});
				break;
		}
	}

	const GetMinuteOption = function(symbol) {
		var option = {
			Type: "分钟走势图", //创建图形类型

			//窗口指标
			Windows: [],
			Symbol: symbol,

			IsAutoUpdate: true, //是自动更新数据
			DayCount: 1, //1 最新交易日数据 >1 多日走势图
			IsShowCorssCursorInfo: true, //是否显示十字光标的刻度信息
			CorssCursorInfo: {
				Left: 2,
				Right: 0,
				Bottom: 1,
				IsShowCorss: true
			}, //十字光标刻度设置
			IsShowRightMenu: false, //是否显示右键菜单
			CorssCursorTouchEnd: false,
			MinuteLine: {
				IsDrawAreaPrice: true, //是否画价格面积图
			},

			//边框
			Border: {
				Left: 1, //左边间距
				Right: 1, //右边间距
				Top: 1,
				Bottom: 20,
			},

			//子框架设置
			Frame: [{
					SplitCount: 3,
					StringFormat: 0,
				},
				{
					SplitCount: 2,
					StringFormat: 0,
				},
				{
					SplitCount: 3,
					StringFormat: 0,
				},
			],

			//扩展图形
			ExtendChart: [{
					Name: "MinuteTooltip"
				}, //手机端tooltip
			],
			NetworkFilter: NetworkFilter,
		};

		return option;
	};

	const GetKLineOption = function(symbol) {
		let data = {
			Symbol: symbol,
			Type: "历史K线图",
			//窗口指标
			Windows: [{
					Index: "MA",
					Modify: false,
					Change: false,
					Close: false
				},
				{
					Index: "VOL",
					Modify: false,
					Change: false,
					Close: false
				},
			],
			IsAutoUpate: true, //是自动更新数据
			IsShowCorssCursorInfo: false, //是否显示十字光标的刻度信息
			IsShowRightMenu: false, //右键菜单

			//边框
			Border: {
				Left: 1,
				Right: 1, //右边间距
				Top: 1,
				Bottom: 1,
			},
			KLineTitle: {
				IsShowName: !1,
				IsShowSettingInfo: !1,
				isShowDateTime: !0,
			},

			KLine: {
				DragMode: 3, //拖拽模式 0 禁止拖拽 1 数据拖拽 2 区间选择
				Right: 0, //复权 0 不复权 1 前复权 2 后复权
				Period: 0, //周期: 0 日线 1 周线 2 月线 3 年线
				PageSize: 50, //一屏展示多少数据
				IsShowTooltip: false, //是否显示K线提示信息
			},

			//扩展图形
			ExtendChart: [{
					Name: "KLineTooltip"
				}, //手机端tooltip
			],
			NetworkFilter: NetworkFilter,
		};

		return data;
	};

	// 3分时 0 天 1 周 2 月 4 1分 5 5分 7 30分
	// 分时 天 周 月 1分 5分
	// ["分时",{Value:1,KLineShow:false,MinuteShow:true}],
	//                     ["五日",{Value:5,KLineShow:false,MinuteShow:true}],
	//                     ["日线",{Value:0,KLineShow:true,MinuteShow:false}],
	//                     ["周线",{Value:1,KLineShow:true,MinuteShow:false}],
	//                     ["月线",{Value:2,KLineShow:true,MinuteShow:false}],
	//                     ["年线",{Value:3,KLineShow:true,MinuteShow:false}],
	//                     ["1分钟",{Value:4,KLineShow:true,MinuteShow:false}],
	//                     ["5分钟",{Value:5,KLineShow:true,MinuteShow:false}],
	//                     ["15分钟",{Value:6,KLineShow:true,MinuteShow:false}],
	//                     ["30分钟",{Value:7,KLineShow:true,MinuteShow:false}],
	//                     ["60分钟",{Value:8,KLineShow:true,MinuteShow:false}],
	const dict = {
		1: 1,
		2: 0,
		3: 1,
		4: 2,
		5: 4,
		6: 5
	};
	var JSCommon = HQChart.Chart;
	var JSCommonStock = HQChart.Stock;
	var msg = "";
	export default {
		props: ["message"],
		name: "constitute",
		data() {
			return {
				knum: 1,
				isFullScreen: false,
				isSetting: false,
				ymbol: "600000.sh",

				fuquan: 0, //不 前 后
				id1: "MA", //ma boll
				id2: "VOL", //vol atr macd kdj

				Minute: {
					JSChart: null,
					IsShow: true,
					Option: GetMinuteOption(this.Symbol),
				},
				Kline: {
					JSChart: null,
					IsShow: false,
					Option: GetKLineOption(this.Symbol),
				},
			};
		},
		components: {},
		mounted() {
			console.log("shit", this.$props);
			this.Symbol = this.$props.message;
			msg = this.Symbol || "600000.sh";
			this.OnSize();

			window.onresize = () => {
				this.OnSize();
			};

			this.choose(1, "1m");
		},
		methods: {
			choose(e, k) {
				this.knum = e;

				this.Kline.IsShow = e != 1;
				this.Minute.IsShow = e == 1;

				if (this.Kline.IsShow) this.ChangeKLinePeriod(dict[e]);
				if (this.Minute.IsShow) this.ChangeMinutePeriod(dict[e]);

				this.OnSize();
			},
			onPlus() {
				var obj = {
					ID: 4
				};
				if (this.Kline.IsShow) {
					this.Kline.JSChart.JSChartContainer.ChartOperator(obj);
				}
				if (this.Minute.IsShow) {
					this.Minute.JSChart.JSChartContainer.ChartOperator(obj);
				}
				//       OP_SCROLL_LEFT:1,
				//     OP_SCROLL_RIGHT:2,
				//     OP_ZOOM_OUT:3,  //缩小
				//     OP_ZOOM_IN:4,   //放大
				//     OP_GOTO_HOME:5, //最新页面
				//     OP_GOTO_END:6,  //起始页面 （ver>=8929的版才有）
			},
			onMinus() {
				var obj = {
					ID: 3
				};
				if (this.Kline.IsShow) {
					this.Kline.JSChart.JSChartContainer.ChartOperator(obj);
				}
				if (this.Minute.IsShow) {
					this.Minute.JSChart.JSChartContainer.ChartOperator(obj);
				}
			},
			onSet() {
				this.isSetting = !this.isSetting;
			},
			onFuquan(v) {
				this.fuquan = v;
				//  this.updateWindow();
				if (!this.Kline.JSChart) return;
				this.Kline.JSChart.ChangeRight(v);
			},
			onId1(v) {
				this.id1 = v;
				if (!this.Kline.JSChart) return;
				this.Kline.JSChart.ChangeIndex(0, v);
			},
			onId2(v) {
				this.id2 = v;
				if (!this.Kline.JSChart) return;
				this.Kline.JSChart.ChangeIndex(1, v);
			},

			onFullScreen() {
				this.isFullScreen = !this.isFullScreen;
				this.updateType();
				this.OnSize();
			},
			updateWindow() {
				this.Kline.Option.KLine.Right = this.fuquan;

				if (this.Kline.JSChart) {
					this.Kline.JSChart.SetOption(this.Kline.Option);
				}
			},
			updateType() {
				this.Kline.Option.Type = this.Kline.Option.Type.replace("横屏", "");
				if (this.isFullScreen) {
					this.Kline.Option.Type += "横屏";
				}

				this.Minute.Option.Type = this.Minute.Option.Type.replace("横屏", "");
				if (this.isFullScreen) {
					this.Minute.Option.Type += "横屏";
				}
				if (this.Minute.JSChart) {
					this.Minute.JSChart.SetOption(this.Minute.Option);
				}
				if (this.Kline.JSChart) {
					this.Kline.JSChart.SetOption(this.Kline.Option);
				}
				//    ChartOperator
				console.log("当前类型", this.Minute.Option.Type, this.Minute.JSChart);
			},
			OnSize() {
				var chartHeight = window.innerHeight - 15;
				var chartWidth = window.innerWidth - 15;
				var left = 0;
				if (!this.isFullScreen) {
					chartHeight = 300;
				} else {
					chartWidth = window.innerWidth - 90;
					chartHeight = window.innerHeight - 40;
					left = 40;
				}

				if (this.Kline.IsShow) {
					var minute = this.$refs.kline;
					minute.style.width = chartWidth + "px";
					minute.style.height = chartHeight + "px";
					minute.style["margin-left"] = left + "px";

					if (this.Kline.JSChart) {
						this.Kline.JSChart.OnSize();
					}
				}
				if (this.Minute.IsShow) {
					var minute = this.$refs.minute;
					minute.style.width = chartWidth + "px";
					minute.style.height = chartHeight + "px";

					minute.style["margin-left"] = left + "px";

					if (this.Minute.JSChart) {
						this.Minute.JSChart.OnSize();
					}
				}
			},

			ChangeKLinePeriod(
				period //历史K线周期切换
			) {
				if (!this.Kline.JSChart) {
					//不存在创建
					this.Kline.Option.KLine.Period = period;
					this.CreateKLineChart();
				} else {
					this.Kline.JSChart.ChangePeriod(period);
				}
			},
			ChangeMinutePeriod(period) {
				if (!this.Minute.JSChart) {
					//不存在创建
					this.Minute.Option.DayCount = period;
					this.CreateMinuteChart();
				} else {
					this.OnSize();
					this.Minute.JSChart.OnSize();
					this.Minute.JSChart.ChangeDayCount(period);
				}
			},

			CreateKLineChart() {
				//创建K线图
				if (this.Kline.JSChart) return;
				this.Kline.Option.Symbol = this.Symbol;
				let chart = JSCommon.JSChart.Init(this.$refs.kline);
				chart.SetOption(this.Kline.Option);
				//  chart.AddEventCallback({
				//    event: JSCommon.JSCHART_EVENT_ID.ON_CLICK_INDEXTITLE,
				//    callback: this.OnClickIndexTitle,
				//  }); //点击事件通知回调
				this.Kline.JSChart = chart;
			},

			CreateMinuteChart() {
				//创建日线图
				if (this.Minute.JSChart) return;
				this.Minute.Option.Symbol = this.Symbol;
				let chart = JSCommon.JSChart.Init(this.$refs.minute);
				HQChart.Chart.JSChart.GetResource().FrameLogo.Text = null;
				chart.SetOption(this.Minute.Option);
				this.Minute.JSChart = chart;
			},
		},
		destroyed() {},
	};
</script>

<style type="text/css" lang="less" scoped="scoped">
	.set-group {
		padding: 1px 5px;
		background-color: white;
		position: absolute;
		right: -10px;
		top: 30px;
		z-index: 9;
		border: 1px solid #eee;

		div {
			padding: 8px 10px;
		}

		.xuan {
			color: #0263e2;
			font-weight: 800;
			background: #f5f5f5;
			box-shadow: inset 0 0.055556rem 0.092593rem rgba(0, 0, 0, 0.125);
		}

		.xuan1 {
			color: #ea4646;
			font-weight: 800;
			background: #f5f5f5;
			box-shadow: inset 0 0.055556rem 0.092593rem rgba(0, 0, 0, 0.125);
		}

		.xuan2 {
			color: #1daa07;
			font-weight: 800;
			background: #f5f5f5;
			box-shadow: inset 0 0.055556rem 0.092593rem rgba(0, 0, 0, 0.125);
		}
	}

	.h-peroid {
		display: flex;
		flex-direction: column;
		width: 40px;
		height: 100%;
		position: absolute;
		top: 0px;
		left: 0px;
		z-index: 50;

		a {
			flex: 1;
			text-align: center;
			justify-content: center;
			align-items: center;
			display: flex;

			span {
				text-align: center;
				display: block;
				white-space: nowrap;
				-webkit-transform: rotate(90deg);
				transform: rotate(90deg);
			}
		}

		.xuan {
			color: #0263e2;
			font-weight: 800;
			background: #f5f5f5;
			box-shadow: inset 0 0.055556rem 0.092593rem rgba(0, 0, 0, 0.125);
		}
	}

	.h-set-group {
		display: flex;
		flex-direction: column;
		width: 40px;
		height: 100%;
		position: absolute;
		top: 0px;
		right: -40px;
		z-index: 50;
		background-color: white;

		div {
			flex: 1;
			text-align: center;
			justify-content: center;
			align-items: center;
			display: flex;

			span {
				text-align: center;
				display: block;
				white-space: nowrap;
				-webkit-transform: rotate(90deg);
				transform: rotate(90deg);
			}
		}

		.xuan {
			color: #0263e2;
			font-weight: 800;
			background: #f5f5f5;
			box-shadow: inset 0 0.055556rem 0.092593rem rgba(0, 0, 0, 0.125);
		}

		.xuan1 {
			color: #ea4646;
			font-weight: 800;
			background: #f5f5f5;
			box-shadow: inset 0 0.055556rem 0.092593rem rgba(0, 0, 0, 0.125);
		}

		.xuan2 {
			color: #1daa07;
			font-weight: 800;
			background: #f5f5f5;
			box-shadow: inset 0 0.055556rem 0.092593rem rgba(0, 0, 0, 0.125);
		}
	}

	.chart-view {
		padding: 8px;
		position: relative;
	}

	.fullscreen {
		position: absolute;
		top: 0px;
		left: 0px;
		z-index: 9;
		background-color: white;
		height: 100%;
	}

	.xinat {
		width: 3.45rem;
		margin: 0 auto;
		display: flex;
		justify-content: space-between;
		margin-top: 0.14rem;
		position: relative;

		a {
			width: 0.83rem;
			height: 0.29rem;
			font-size: 0.12rem;
			color: #999;
			text-align: center;
			line-height: 0.29rem;

			&.xuan {
				color: #f55d48;
				background-color: #f5f5f5;
				border-radius: 5px;
			}
		}
	}

	.icon-group {
		position: absolute;
		right: 20px;
		bottom: 20px;
	}

	.h-icon-group {
		position: absolute;
		right: 20px;
		bottom: 10px;
	}

	.icon-space {
		margin-left: 15px;
		display: inline;
		background-color: #ea4545;
		border-radius: 8px;
		padding: 2px 4px;
	}

	.btn-set {
		height: 0.29rem;
		width: 0.83rem;
		text-align: center;
		padding: 0.04rem 0;

		img {
			width: 0.21rem;
			height: 0.21rem;
		}
	}
</style>