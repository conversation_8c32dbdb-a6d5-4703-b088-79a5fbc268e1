<template>
	<div class="pages">
		<!-- <div class="info nflex nflex-lc">
			<img :src="$config.baseUrl + config.logo" alt="logo">
			<span v-show="config.title">{{ config.title }}</span>
		</div> -->
		<div class="logo flex-column-item" v-if="false">
			<img :src="config.logo" style="width: 0.88rem;height: 0.86rem;" />
			<!-- <div style="font-size: .24rem;color: #dc183b;margin-left: .1rem;font-weight: bold;">
				{{ config.title }}
			</div> -->
		</div>
		<div class="progress">
			<div class="progress-container">
				<div class="progress-bar" :style="{ width: `${progress}%` }"></div>
			</div>
			<span class="progress-text flex flex-c">應用程式加載中...({{ progress }}%)</span>
		</div>
<!--		<div class="botBg">-->
<!--			<img src="../../assets/v3/botBg.png" alt="" />-->
<!--		</div>-->
	</div>
</template>

<script>
	export default {
		name: 'Start',
		data() {
			return {
				progress: 0,
				userInfo: null,
				config: {}
			}
		},
		created() {
			this.updateProgress()
			// this.getUserInfo()
			this.getConfig()
		},
		methods: {
			getUserInfo() {
				this.$server.post('/user/getUserinfo').then(res => {
					if (res && res.data.status == 1) {
						this.userInfo = res.data.data
					}
				})
			},
			getConfig() {
				this.$server.post('/common/config',{
					type:'all'
				}).then(res => {
					if (res && res.status == 1) {
						const arr = res.data
						const obj = {}
						arr.forEach(item => {
							obj[item.name] = item.value
						})
						this.config = obj
					}
				})
			},
			updateProgress() {
				setTimeout(() => {
					this.progress += 20
					if (this.progress < 100) {
						this.updateProgress()
					} else {
						if (this.userInfo) {
							setTimeout(() => {
								this.$router.replace('/home');
							}, 1000)
						} else {
							this.$router.replace('/login/login');
						}
					}
				}, 1000)
			}
		}
	}
</script>

<style lang="less" scoped>
	.pages {
		width: 100%;
		min-height: 100vh;
		// background: url("../../assets/v2/newstartbg.png") no-repeat;
		// background-size: 100%;
		background: url("../../assets/start.png") no-repeat;
		background-size: 100% 100%;
	}

	.info {
		position: relative;
		top: 1.5rem;
		width: 100%;
		img {
			width: .45rem;
			height: .45rem;
		}

		span {
			padding-left: .2rem;
			font-family: PangMenZhengDaoBiaoTiTiMianFeiBan, PangMenZhengDaoBiaoTiTiMianFeiBan;
			font-weight: normal;
			font-size: .3rem;
			color: #FFFFFF;
		}
	}

	.logo {
		padding-top: 1.5rem;
		div {
			font-size: .25rem;
			color: #fff;
		}
	}
	.botBg{
		position: fixed;
		bottom: 0;
		height: 0.9rem;
		img{
			width: 100%;
			height: 0.9rem;
			padding: 0;
			display: inline-block;
		}
	}
	.progress {
		position: fixed;
		bottom: 1rem;
		padding: 0 .25rem;
		width: 100%;
		box-sizing: border-box;

		.progress-text {
			width: 100%;
			height: .4rem;
			line-height: .4rem;
			text-align: center;
			font-family: SourceHanSansSC, SourceHanSansSC;
			font-weight: 400;
			font-size: .13rem;
			color: #000;
			animation: flash;
			animation-duration: 10s;
		}

		.progress-container {
			width: 100%;
			height: .06rem;
			background: #DEDEDE;
			border-radius: .04rem;

			.progress-bar {
				height: 100%;
				background: #3B3B37;
				transition: width 0.5s ease;
				border-radius: .04rem;
			}
		}
	}
</style>