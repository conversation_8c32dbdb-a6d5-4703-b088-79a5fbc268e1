<template>
	<div class="setting">
		<div class="headf">
			<top :title="$t(text)"></top>
		</div>

		<ul class="ulk">
			<li style="position: relative;">
				<span style="line-height: 0.44rem;">{{ $t("setting").txt2 }}</span>
				<img src="../../assets/skin/mine/toux.png" style="margin-right: .1rem;" />
				<input class="inp" accept="image/*" type="file" @change="changeing($event)" v-if="false" />
			</li>
			<li>
				<span>{{ $t("setting").txt3 }}</span>
				<a>{{ userdata.account }}</a>
			</li>

			<li class="flex" @click="choose(1)">
				<span>{{ $t("setting").txt4 }}</span>
				<span class="jtr"></span>
			</li>

			<li class="flex" @click="choose(0)">
				<span>{{ $t("setting").txt5 }}</span>
				<span class="jtr"></span>
			</li>

			<!-- <li @click="guanbd">
				<span>{{ $t("setting").txt6 }}</span>
				<img class="tui" src="../../assets/skin/mine/tuichu.png" />
			</li> -->
		</ul>
	</div>
</template>
<script type="text/javascript">
	import Vue from "vue";
	import qs from "qs";
	import axios from "axios";
	import top from "../bar/toper.vue";
	import {
		Toast
	} from "vant";
	import imgurl from "../../lib/common.js";
	Vue.use(Toast);
	export default {
		name: "setting",
		data() {
			return {
				text: this.$t('setting').txt1,
				userdata: [],
				cardz: '',
				imgz: ''
			};
		},
		components: {
			top,
		},
		created() {},
		methods: {
			guanbd() {
				window.localStorage.removeItem("tokend");
				//window.localStorage.removeItem("account");
				axios.defaults.headers["token"] = "";
				//axios.defaults.headers["account"] = "";
				this.$router.push({
					path: "/login/login"
				});
			},
			getUser() {
				this.$server.post("/user/getUserinfo").then((str) => {
					if (str.data.status == 1) {
						this.userdata = str.data.data;
						this.userdata.account = this.userdata.account.replace(
							/(\d{3})\d{4}(\d{4})/,
							"$1****$2"
						);
						window.localStorage.setItem(
							"userdata",
							JSON.stringify(str.data.data)
						);
					} else {
						Toast({
							message: this.$t(str.data.msg),
							duration: 2000,
						});
					}
				});
			},
			changeing(e) {
				var file = e.target.files[0];
				var that = this;

				var formdata = new FormData();

				formdata.append("card", file);

				this.$server
					.post("/common/upload1", formdata)
					.then((res) => {
						if (res.data.status == 1) {
							that.cardz = res.data.data;
							Toast({
								message: this.$t("上传成功"),
								duration: 3000,
							});
							// this.imgz = "api/"+res.data.data;
							this.imgz = imgurl.imgUrls + res.data.data;
							window.localStorage.setItem('avatarUrl', this.imgz)
						}
					})
					.catch((data) => {});
			},
			choose(e) {
				if (e == 0) {
					this.$router.push({
						path: "/user/setPassword",
					});
				} else {
					this.$router.push({
						path: "/user/loginPassword",
					});
				}
			},
		},
		destroyed() {},
		mounted() {
			this.getUser();
		},
	};
</script>
<style type="text/css" lang="less" scoped="scoped">
	input::-webkit-input-placeholder,
	textarea::-webkit-input-placeholder {
		color: #999;
		font-size: 0.15rem;
	}

	.setting {
		background-color: #1B232A;
		min-height: 100vh;

		.headf {
			width: 100%;
			height: 0.44rem;
			//background: linear-gradient(233deg, #f36218, #f59934);
			background: #fff;
		}

		.inp {
			position: absolute;
			width: 100%;
			height: 100%;
			left: 0;
			top: 0;
			opacity: 0;
		}

		.jtr {
			width: .14rem;
			height: .26rem;
			background: url(../../assets/v2/arrow00.png) no-repeat center;
			background-size: 100%;
			// margin-top: 0.2rem;
			margin-right: 0.12rem;
		}

		.ulk {
			li {
				width: 100%;
				padding: 0.15rem 0;
				display: flex;
				justify-content: space-between;
				border-bottom: 0.01rem solid rgba(255, 255, 255, 0.16);
				img {
					width: .47rem;
				}

				span {
					color: #fff;
					font-size: 0.14rem;
					margin-left: 0.12rem;
				}

				a {
					color: #fff;
					font-size: 0.14rem;
					margin-right: 0.12rem;
				}

				.tui {
					margin-right: 0.12rem;
					width: 0.16rem;
					height: 0.16rem;
				}
			}
		}
	}
</style>