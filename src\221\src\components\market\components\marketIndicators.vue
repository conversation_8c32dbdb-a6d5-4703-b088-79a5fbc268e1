<template>
  <!-- 市场指标 -->
  <div>
    <div class="flex flex-b titles">
      <div class="flex-1">{{ $t("newt").t57 }}</div>
      <div class="flex-1 t-r">
        {{ type == "jixiu" ? $t("newt").t75 : $t("newt").t58 }}
      </div>
      <div class="flex-1 t-r">{{ $t("newt").t59 }}</div>
    </div>

    <div class="flex flex-b tab">
      <div
        class="tab-item"
        :class="{ active: type == item.type }"
        v-for="(item, i) in typeList"
        :key="i"
        @click="changeType(item.type)"
      >
        {{ $t(item.name) }}
      </div>
    </div>

    <div class="list">
      <no-data v-if="!list.length"></no-data>

      <div
        class="list-item flex flex-b"
        v-for="(item, i) in list"
        :key="i"
        @click="
              $toDetail(`/market/stockDetailzs?symbol=${item.stock_id}`, item)
            "
      >
        <div class="flex-1">{{ item.code.toLocaleUpperCase() }}</div>
        <div class="price flex flex-e flex-1 t-r">
          {{ $formatMoney(item.close) }}
          <div
            class="icon down animate__animated animate__fadeIn"
            :class="{
              up: item.gain > 0,
            }"
          ></div>
        </div>
        <div class="per flex-1 t-r" :class="item.gain >= 0 ? 'red' : 'green'">
          {{ item.gain < 0 ? "" : "+" }}{{ item.gain }}%
        </div>
      </div>
    </div>
    <loading ref="loading" />
  </div>
</template>

<script>
export default {
  name: "marketIndicators",
  data() {
    return {
      type: "jixiu",
      page: 0,
      typeList: [
        {
          name: "指数",
          type: "jixiu",
        },
        {
          name: "汇率",
          type: "huilv",
        },
        {
          name: "原料",
          type: "yuanliao",
        },
        {
          name: "数字货币",
          type: "szhb",
        },
      ],
      list: [],
    };
  },
  created() {},
  mounted() {
    this.getInfo();
  },
  methods: {
    changeType(type) {
      this.type = type;
      this.list = [];
      this.getInfo();
    },
    getInfo() {
      this.$refs.loading.open(); //开启加载

      this.$server.post("/transaction/sczb", { type: this.type }).then((res) => {
        this.$refs.loading && this.$refs.loading.close();

        if (res.status == 1) {
          let data = Object.values(res.data);
          let arr = [];
          data.forEach((item) => {
            arr.push({
              ko_name: item.code.toLocaleUpperCase(),
              // time: this.$formatDate('YYYY.MM.DD hh:mm:ss', new Date(item.dt).getTime()),
              time: this.$getTimeData(item.dt),
              gainValue: item.close - item.prev_close,
              gain: (
                ((item.close - item.prev_close) / item.prev_close) *
                100
              ).toFixed(2),
              ...item,
            });
          });

          this.list = [...arr];
        }
      });
    },
  },
};
</script>

<style scoped lang="less">
.titles {
  padding: 0 0.1rem 0.1rem;
  border-bottom: 0.01rem solid #f5f5f5;
  div {
    font-size: 0.12rem;
    color: #757575;
  }
}

.list {
  .list-item {
    padding: 0.1rem;
    // border-bottom: 0.02rem solid #ececec;
    border-bottom: 0.01rem solid #f5f5f5;
    .name {
      font-weight: bold;
      margin-left: 0.05rem;
    }
    .code {
      font-weight: 500;
      font-size: 0.12rem;
      color: #464646;
      margin-left: 0.05rem;
    }
    .price {
      font-weight: bold;
      // font-size: 0.18rem;
      .icon {
        margin-left: 0.05rem;
      }
    }
    .per {
      font-weight: bold;
      // font-size: 0.12rem;
    }
  }
}

.red {
  color: #c04649;
}
.green {
  color: #4f8672;
}

.tab {
  padding: 0.1rem;
  .tab-item {
    width: 23%;
    background: #ededed;
    border-radius: 0.04rem;
    color: #a0a0a0;
    text-align: center;
    font-size: 0.12rem;
    padding: 0.05rem 0.1rem;

    &.active {
      background: #6970af;
      color: #ffffff;
    }
  }
}
</style>
