<template>
	<div class="page ">
		<top-back :title="$t('競價拍賣詳情')"></top-back>
		<div class="cot">
			<div class="item-top">
				<div class="flex">
					<div class="name">{{ item.name || "-" }}</div>
					<div class="code">{{ item.symbol || "-" }}</div>
				</div>
				<div class="number">可投标张数：{{item.number}}</div>
				<!-- <div class="item-list">
					<div class="t3" :class="item.price - item.bprice < 0 ? 'green' : 'red'">{{ item.price - item.bprice > 0 ? "+" : ""}}{{ ((item.price - item.bprice) / item.bprice) * 100 == -100? "-" : (((item.price - item.bprice) / item.bprice) * 100).toFixed(2)}}%
					</div>
					<div class="t4 t-r">溢價差</div>
				</div> -->
			</div>
			<div class="item">
				<!-- <div class="jzrBox">
					<van-circle v-model="currentRate" :rate="30" :speed="100"  size="100px"layer-color="#ececec" stroke-width="80" color="#2a3e97" />
					<div class="jzr flex-column-item">
						<div class="t2 ">截止日</div>
						<div class="t3 flex flex-c">{{ $formatDate("MM-DD", item.end * 1000) }}</div>
					</div>
				</div> -->
				<div class="bottom" v-if="item.isKsg">
					<div class="flex flex-b" style="margin-top: 0.1rem;">
						<div class="t">申購數量</div>
						<div class="t">帳戶可用資金：<span>{{ $formatMoney(userInfo.twd) }}</span></div>
					</div>
					<input v-model="quantity" @input="quantity = quantity.replace(/[^0-9]/g, '')" placeholder="請輸入申購數量" type="number" />
				</div>
				<div class="info">
					<!-- <div class="title">基本訊息</div> -->
					<div class="info-item flex flex-b">
						<div class="t">股票名稱</div>
						<div class="t1" >{{ item.name || "-" }}</div>
					</div>
					<div class="info-item flex flex-b">
						<div class="t">發行市場</div>
						<span class="t1">{{ item.exchange }}</span>
					</div>
					<!-- <div class="info-item flex flex-b">
						<div class="t">市價</div>
						<div class="t1" :class="item.price - item.bprice < 0 ? 'green' : 'red'">{{ $formatMoney(item.price) }}
						</div>
					</div>
					<div class="info-item flex flex-b">
						<div class="t">總申購</div>
						<div class="t1">{{ $formatMoney(item.num, 0) }}</div>
					</div>
					<div class="info-item flex flex-b">
						<div class="t">差價</div>
						<div class="t1">{{ $formatMoney(item.price - item.bprice) }}</div>
					</div>
					<div class="info-item flex flex-b">
						<div class="t ">承銷價</div>
						<div class="t1">{{ $formatMoney(item.bprice) }}</div>
					</div> -->
					<!-- <div class="info-item flex flex-b">
						<div class="t">申購期間</div>
						<div class="t1">{{ item.subdate }}</div>
					</div>
					<div class="info-item flex flex-b">
						<div class="t">截止日</div>
						<div class="t1">{{ item.endTime }}</div>
					</div>
					<div class="info-item flex flex-b">
						<div class="t3">撥券日</div>
						<div class="t1">{{ item.amtdate }}</div>
					</div> -->
					<div class="info-item flex flex-b">
						<div class="t">開始日</div>
						<span class="t1">{{ $formatDate("YYYY/MM/DD", item.start * 1000) }}</span>
					</div>
					<div class="info-item flex flex-b">
						<div class="t">截止日</div>
						<span class="t1">{{ $formatDate("YYYY/MM/DD", item.end * 1000) }}</span>
					</div>
					<div class="info-item flex flex-b">
						<div class="t">中籤日</div>
						<span class="t1">{{ $formatDate("YYYY/MM/DD", new Date(item.gb_date)) }}</span>
					</div>
					<div class="info-item flex flex-b">
						<div class="t">撥券日</div>
						<span class="t1">{{ $formatDate("YYYY/MM/DD", new Date(item.fq_date)) }}</span>
					</div>
					<div class="info-item flex flex-b">
						<div class="t">上市日</div>
						<span class="t1">{{ $formatDate("YYYY/MM/DD", new Date(item.ss_date)) }}</span>
					</div>
					<div class="info-item flex flex-b">
						<div class="t">實際承銷價</div>
						<div class="t1  num-font">{{ $formatMoney(item.price) || 0 }}</div>
					</div>
					<div class="info-item flex flex-b">
						<div class="t">報酬率</div>
						<span class="t1">{{ item.rate }}%</span>
					</div>
					<div class="info-item flex flex-b">
						<div class="t">主辦券商</div>
						<div class="t1">{{ item.name || "-" }}</div>
					</div>
					<div class="info-item flex flex-b">
						<div class="t">暫時承銷價</div>
						<span class="t1">{{ $formatMoney(item.bprice) || 0 }}</span>
					</div>
					<div class="info-item flex flex-b">
						<div class="t">帳戶可用資金</div>
						<span class="t1">{{ $formatMoney(userInfo.twd) }}</span>
					</div>
					<div class="tips">注意<br/>1.请在投标截止日前保证账户金额是否充足<br/>2.确认参与竞拍后，投标处理费不退，请谨慎操作</div>
					<div v-if="item.isKsg" @click="submitSg" class="defbtn animate__animated animate__fadeIn" style="margin: 0.12rem 0;">申購</div>
				</div>
			</div>


		</div>
		<!--点击按钮加载效果组件 -->
		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "newStockBuy",
		data() {
			return {
				item: this.$storage.get("itemTemp") || {},
				quantity: "",
				flag: false,
				type: 0,
				userInfo: {},
				currentRate:20
			};
		},
		created() {
			this.type = this.$route.query.type;
			this.getUserInfo();
			console.log(this.item,6666)
		},
		methods: {
			async getUserInfo() {
				const res = await this.$server.post("/user/getUserinfo", {
					type: "twd"
				});
				if (res.status == 1) {
					this.userInfo = res.data;
				}
			},
			submitSg() {
				if (!this.quantity) {
					this.$toast("請輸入申購數量");
					return;
				}
				if (this.flag) {
					return;
				}
				this.flag = true;
				this.$refs.loading.open(); //开启加载
				this.$server
					.post("/trade/buy_newstock", {
						symbol: this.item.symbol,
						zhang: this.quantity,
						type: "twd",
						buy_type: 1,
						id: this.item.id,
					})
					.then((res) => {
						this.$refs.loading.close(); //关闭加载
						this.$toast(this.$formText(res.msg));
						this.getUserInfo();
						setTimeout(() => {
							this.flag = false;
						}, 2000);
					});
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.6rem 0 0.5rem;
		min-height: 100vh;
	}

	.cot {
		.item-top {
			margin: 0 0.12rem;
			padding: 0.12rem 0.12rem 0.4rem;
			background: linear-gradient(45deg, #B2E56E, #EFFDB0);
			border-radius: 0.13rem 0.13rem 0rem 0rem;
			.name {
				font-family: PingFang SC;
				font-weight: 600;
				font-size: 0.15rem;
				color: #000000;
			}
			.code {
				font-family: PingFang SC;
				font-weight: 600;
				font-size: 0.13rem;
				color: #999;
				margin-left: 0.1rem;
			}
			.number{
				margin-top: 0.1rem;
				font-family: PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
				color: #666666;
			}
			.t3 {
				font-size: 0.16rem;
			}
			.t4 {
				font-size: 0.12rem;
				color: #fff;
				margin-top: 0.05rem;
			}
		}
		.item {
			margin: -0.25rem 0 0;
			background: #232429;
			border-radius: 0.22rem 0.22rem 0rem 0rem;
			padding: 0.2rem 0.12rem;
			.red {
				color: #cf2829;
			}

			.t3 {
				font-size: 0.12rem;
			}
			.jzrBox{
				position: relative;
				margin-right: 0.1rem;
				.jzr{
					position: absolute;
					top: 50%;
					left: 50%;
					transform: translate(-50%,-50%);
					.t2{
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 0.12rem;
						color: #393939;
					}
					.t3{
						margin-top: 0.05rem;
						font-family: PingFang SC, PingFang SC;
						font-weight: 600;
						font-size: 0.16rem;
						color: #000000;
					}
				}
			}
			.btn {
				height: 0.3rem;
				background: linear-gradient( 90deg, #1B167A 0%, #4383C7 100%);
				border-radius: 0.04rem 0.04rem 0.04rem 0.04rem;
				padding: 0 0.1rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 0.13rem;
				color: #FFFFFF;
				line-height: 0.28rem;
			}
			.btn3 {
				height: 0.3rem;
				background: #ABB5BF;
				border-radius: 0.04rem;
				padding: 0 0.1rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 0.13rem;
				color: #FFFFFF;
				line-height: 0.28rem;
			}
		}

		.info {
			background: #434446;
			border-radius: 0.09rem;
			margin-top: 0.15rem;
			padding: 0.12rem;
			.title {
				padding: 0.1rem 0.12rem;
				font-family: PingFang SC, PingFang SC;
				font-weight: 600;
				font-size: 0.14rem;
				color: #000000;
			}

			.info-item {
				padding: 0.1rem 0;
				.t {
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 0.13rem;
					color: #999999;
				}

				.t1 {
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 0.13rem;
					color: #FFFFFF;
				}
			}
		}
		.tips{
			font-family: PingFang SC;
			font-weight: 400;
			font-size: 0.13rem;
			color: #FF5683;
		}
		.bottom {
			.t {
				font-family: PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
				color: #999999;
				span{
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 0.13rem;
					color: #FF5683;
				}
			}
			input {
				text-align: center;
				width: 100%;
				height: 0.46rem;
				background: #434446;
				border-radius: 0.09rem;
				padding: 0 0.1rem;
				margin-top: 0.1rem;
				font-family: PingFang SC;
				font-weight: 400;
				font-size: 0.14rem;
				color: #FFFFFF;
				&::placeholder {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.12rem;
					color: #999999;
				}
			}
		}
	}
</style>