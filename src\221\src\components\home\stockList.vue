<template>
  <div class="page ">
    <top-menu :isList="true"></top-menu>

    <van-pull-refresh
      v-model="isLoading"
      @refresh="onRefresh"
      :loosing-text="$t('new').t3"
      :loading-text="$t('new').t4"
      :pulling-text="$t('new').t5"
    >
      <!-- 综合市场 -->
      <van-skeleton
        title
        :row="26"
        :loading="loading"
        v-if="!chooseList.length"
      />
      <van-list
        v-model="loading1"
        :finished="finished"
        :finished-text="$t('new').a51"
        :loading-text="$t('new').a"
        @load="onLoad"
      >
        <div class="cot">
          <div class="list-tab flex flex-b">
            <div
              class="tab-item"
              :class="{ active: item.type == current }"
              @click="changeType(item.type)"
              v-for="(item, i) in tabs"
              :key="i"
            >
              {{ $t(item.name) }}
            </div>
          </div>

          <!-- 股票列表 -->
          <div class="item">
            <no-data v-if="!chooseList.length"></no-data>

            <div class="titles flex flex-b" v-if="chooseList.length">
              <div class="flex-2">{{ $t("市场") }}</div>
              <div class="flex-1 t-c">{{ $t("卖") }}</div>
              <div class="flex-1 t-c">{{ $t("买") }}</div>
            </div>

            <div class="list">
              <div
                class="list-item flex flex-b"
                v-for="(item, idx) in chooseList"
                :key="idx"
                @click="
                  $toDetail(`/market/stockDetail?symbol=${item.symbol}`, item)
                "
              >
                <div class="flex flex-2">
                  <div class="logo">
                    <img :src="item.logo" />
                  </div>

                  <div>
                    <div class="">
                      <span class="name">
                        {{ item.name }}
                      </span>
                      <span class="code">
                        {{ item.symbol }}
                      </span>
                    </div>

                    <div
                      class=" per red"
                      :class="{
                        green: item.gain < 0,
                      }"
                    >
                      <span class="t" v-if="item.gainValue">
                        {{ item.gainValue > 0 ? "+" : ""
                        }}{{ item.gainValue.toFixed(2) }}</span
                      >
                      <span
                        >{{ item.gain > 0 ? "+" : ""
                        }}{{ item.gain.toFixed(2) }}%</span
                      >
                    </div>
                  </div>
                </div>

                <div class="price center flex-1 t-c">
                  <div :class="{ 'red-bg': item.gain > 0 }">
                    {{ Number(item.sells[0].price).toFixed(2) }}
                  </div>
                </div>

                <div class="price flex-1 t-c">
                  <div :class="{ 'green-bg': item.gain <= 0 }">
                    {{ Number(item.buys[0].price).toFixed(2) }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </van-list>
    </van-pull-refresh>
    <!--点击按钮加载效果组件 -->
    <loading ref="loading" />
  </div>
</template>

<script>
export default {
  name: "stockList",
  props: {},
  data() {
    return {
      indexList: [],
      loading: true,
      loading1: false,
      finished: false,
      kfUrl: "",
      isLoading: false,
      chooseList: [],
      tabs: [
        {
          type: 0,
          name: "涨幅榜",
        },
        {
          type: 1,
          name: "跌幅榜",
        },
        {
          type: 2,
          name: "最多关注",
        },
      ],
      current: 0,
      allList: [],
      type: 0,
      page: 1,
    };
  },
  components: {},
  created() {
    if (this.$route.query.type) {
      this.type = this.$route.query.type;
      this.current = this.$route.query.type;

      if (this.type == 0) {
        this.changeType(0);
      } else {
        this.changeType(2);
      }
    }
  },
  mounted() {
    this.getList();
  },
  methods: {
    onLoad() {
      this.page += 1;
      this.getList();
    },
    changeType(type) {
      this.current = type;
      let arr = [];
      let arr1 = [];
      this.$refs.loading && this.$refs.loading.open();

      this.allList.forEach((item) => {
        if (item.gain > 0) {
          arr.push(item); //涨幅显示
        } else {
          arr1.push(item); //跌幅显示
        }
      });

      switch (type) {
        case 0:
          this.chooseList = arr;
          break;
        case 1:
          this.chooseList = arr1;
          break;
        case 2:
          this.chooseList = this.allList; //关注最多显示
          break;
        default:
          break;
      }
      this.$refs.loading && this.$refs.loading.close();

      this.chooseList.forEach((item) => {
        item.gainValue == "-" ? (item.gainValue = 0) : item.gainValue;
        item.gain == "-" ? (item.gain = 0) : item.gain;
        // console.log("item.gainValue", item.gainValue);
        // console.log("item.gain", item.gain);
      });
    },
    // 下拉刷新
    onRefresh() {
      this.page = 1;
      this.chooseList = [];
      this.getList();
    },
    async getList() {
      const res = await this.$server.post("/transaction/allstocks", {
        page: this.page,
        size: 40,
        market: "ALL",
      });
      this.isLoading = false;
      this.loading = false;
      this.loading1 = false;

      if (res.status == 1) {
        let arr = [];
        res.data.forEach((item) => {
          if (item.symbol) {
            arr.push(item);
          }
        });

        this.allList = [...this.allList, ...arr];
        // console.log("allList", this.allList);

        if (arr.length == 0) {
          this.finished = true; //结束列表加载
        }
        this.changeType(this.current);
      }
    },
  },
};
</script>

<style scoped lang="less">
::-webkit-scrollbar {
  display: none;
}

.page {
  padding: 0.6rem 0;
}
::v-deep .van-skeleton__row{
  background-color:transparent !important;
}
::v-deep .van-skeleton__title{
  background-color:transparent !important;
}
.cot {
  padding: 0 0.1rem;

  .mtb20 {
    margin: 0.2rem 0 0;
  }

  .list-tab {
    padding: 0 0 0.1rem;
    .tab-item {
      width: 30%;
      border-radius: 0.3rem;
      border: 0.01rem solid #c4c4c4;
      padding: 0.05rem;
      font-size: 0.12rem;
      color: #c4c4c4;
      text-align: center;
      &.active {
        background: #000000;
      }
    }
  }

  .item {
    .title {
      font-size: 0.16rem;
    }

    .titles {
      margin-bottom: 0.05rem;
      div {
        font-size: 0.12rem;
        color: #535353;
      }
    }

    .list {
      .list-item {
        padding: 0.1rem 0;
        .logo {
          margin-right: 0.1rem;
          img {
            width: 0.32rem;
            height: 0.32rem;
            border-radius: 0.04rem;
          }
        }
        .name {
          font-size: 0.12rem;
          color: #000000;
        }
        .code {
          font-size: 0.1rem;
          color: #c4c4c4;
          margin-left: 0.05rem;
        }
        .per {
          span {
            font-size: 0.1rem;
            margin-right: 0.1rem;
          }
        }
        .price {
          &.center {
            margin: 0 0.1rem;
          }
          div {
            font-size: 0.1rem;
            color: #0c0c0c;
            background: #f3f3f3;
            border-radius: 0.02rem;
            padding: 0.05rem 0;
          }
        }
      }
    }
    .more {
      padding: 0.1rem;
      div {
        font-size: 0.12rem;
        color: #7f7f7f;
      }
      .icon {
        margin-left: 0.05rem;
      }
    }

    .news-list {
      .news-item {
        padding: 0.05rem 0.1rem;
        border-top: 0.01rem solid #f5f5f5;
        border-bottom: 0.01rem solid #f5f5f5;
        .num {
          width: 0.03rem;
          height: 0.4rem;
          background: #6970af;
          margin-right: 0.1rem;
        }

        .t {
          font-weight: 500;
          font-size: 0.12rem;
          color: #404040;
        }
        .time {
          //text-align: right;
          font-size: 0.12rem;
          color: #909090;
          margin-top: 0.1rem;
        }
      }
    }
  }
}

.top {
  padding: 0 0.1rem;
  .search {
    border-radius: 0.3rem;
    background: #f3f3f3;
    height: 0.3rem;
    padding: 0 0.1rem;
    margin: 0 0.1rem;
    div {
      font-size: 0.11rem;
      color: #8f8f8f;
    }
  }
}

.func {
  .title {
    font-weight: 500;
    color: #1e1e1e;
    padding: 0.1rem 0;
  }
  .tab {
    flex-wrap: wrap;
    .tab-item {
      width: 20%;
      align-self: flex-start;
      text-align: center;
      .icon {
        margin: 0 auto 0.05rem;
      }
      .t {
        font-size: 0.1rem;
        color: #000000;
      }
    }
  }
}

.red {
  color: #ba3b3a;
}
.green {
  color: #39B44C;
}

.red-bg {
  background-color: #af1d1c !important;
  color: #fff !important;
}
.green-bg {
  background-color: #4f911d !important;
  color: #fff !important;
}
</style>
