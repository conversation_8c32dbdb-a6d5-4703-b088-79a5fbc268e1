<template>
	<div class="numcom">
		<div class="flex_c_s" v-show="show" style="flex: 1;">
			<input class="input text-right" v-model="inputVal" @input="onChg" />
			<!--    <div class="btn-type2" @click="onPlus">-</div>-->
			<!--    <div class="btn-type" @click="onAdd">+</div>-->
		</div>

		<div class="flex_c_s" v-show="!show" style="flex: 1;">
			<div class="btn-type"> </div>
		</div>
		<div style="font-size:0.8rem">{{$t(ext,'num')}}</div>
	</div>
</template>

<script>
	export default {
		components: {},
		props: {
			show: {
				type: Boolean
			}
		},
		data() {
			return {
				inputVal: "",
				step: 0.1,
				ext: '',

				len: 0,
			};
		},



		methods: {

			onFix(val) {
				return val.toFixed(this.len);
			},

			onPlus() {
				this.inputVal = this.onFix(parseFloat(this.inputVal) - this.step);
				if (this.inputVal < 1) {
					this.inputVal = 1
				}
				// console.log("onPlus", this.inputVal)
				this.$emit("change", {
					value: this.inputVal
				})
			},
			onAdd() {

				this.inputVal = this.onFix(parseFloat(this.inputVal) + this.step);
				if (this.inputVal < 1) {
					this.inputVal = 1
				}
				// console.log("onAdd", this.inputVal)
				this.$emit("change", {
					value: this.inputVal
				})
			},
			setVal(val, step, ext) {
				this.inputVal = val;
				if (step == 0) {
					let ss = ("" + val).match(/\.\d*/);
					if (ss) {
						step = Math.pow(10, -(ss[0].length - 1));
						this.len = ss[0].length - 1;
					} else {
						step = 1;
					}

				}
				this.step = step;
				this.ext = ext;
				this.onChg();
			},
			onChg(e) {
				// console.log(e,this.inputVal );
				this.$emit("change", {
					value: this.inputVal
				})
			}

		},
	};
</script>


<style scoped lang="less">
	.numcom{
		width: 100%;
		background: #000000;
		border-radius: 0.1rem;
		.flex_c_s {
			display: flex;
			align-items: center;
			justify-content: space-between;
			overflow: hidden;
		}
		
		.flex_c {
			display: flex;
			align-items: center;
		}
		
		.btn-type {
			width: .4rem;
			height: .4rem;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: .2rem;
			font-family: Source Han Sans CN-Regular, Source Han Sans CN;
			font-weight: 400;
			color: #848484;
			margin-left: .01rem;
		}
		
		.btn-type2 {
			width: .4rem;
			height: .4rem;
			//background: #2A4160;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: .2rem;
			font-family: Source Han Sans CN-Regular, Source Han Sans CN;
			font-weight: 400;
			color: #FFFFFF;
			margin-left: .01rem;
		}
		
		.input {
			width: 98%;
			height: 0.46rem;
			background: #000000;
			border-radius: 0.1rem;
			line-height: 0.46rem;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 0.14rem;
			color: #999999;
			text-align: left;
			padding-left: 0.15rem;
		}
	}
	::v-deep .uni-input-input {
		font-size: .14rem;
		font-family: PingFangSC-Medium, PingFang SC;
		font-weight: 500;
		color: #24272C;
		background: none;
		text-align: right;
	}
</style>