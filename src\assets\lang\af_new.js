module.exports = {
  // 通用组件
  common: {
    confirm: 'Bevestig',
    cancel: '<PERSON><PERSON><PERSON><PERSON>',
    submit: 'Indien',
    save: '<PERSON><PERSON>',
    delete: 'Verwy<PERSON>',
    edit: 'Wysig',
    back: 'Terug',
    next: 'Volgende',
    previous: 'Vorige',
    complete: 'Volto<PERSON>',
    search: 'Soek',
    loading: '<PERSON><PERSON>...',
    noData: 'Geen Data',
    success: 'Suksesvol',
    failed: 'Misluk',
    processing: 'Verwerk',
    waiting: 'Wag',
    today: 'Vandag',
    yesterday: 'Gister',
    time: 'Tyd'
  },

  // 菜单导航
  menu: {
    home: 'Tuis',
    market: 'Mark',
    trade: '<PERSON>',
    favorite: 'Gunsteling',
    mine: 'Myne'
  },

  // 交易页面
  trade: {
    title: 'Handel',
    profitFunds: 'Wins Fondse',
    availableFunds: 'Beskikbare Fondse',
    positionValue: 'Posisie Waarde',
    totalProfitLoss: 'Totale Wins/Verlies',
    returnRate: 'Opbrengs Koers',
    totalShares: 'Totale Aandele',
    buyPrice: 'Koop Prys',
    marketValue: '<PERSON>',
    cost: 'Koste',
    totalFees: 'Totale Fooie',
    details: 'Besonderhede',
    holding: 'Besit',
    pending: 'Hangend',
    sell: 'Verkoop',
    closePosition: 'Sluit Posisie'
  },

  // 持仓详情页面
  positionDetail: {
    title: 'Posisie Besonderhede',
    profitLoss: 'Wins en Verlies',
    returnRate: 'Opbrengs Koers',
    totalShares: 'Totale Aandele',
    buyPrice: 'Koop Prys',
    marketValue: 'Mark Waarde',
    cost: 'Koste',
    tradeType: 'Handel Tipe',
    buyLong: 'Koop Styg',
    buyShort: 'Koop Daal',
    orderNumber: 'Bestelling Nommer',
    buyShares: 'Koop Aandele',
    buyTime: 'Koop Tyd',
    sellTime: 'Verkoop Tyd',
    sellPrice: 'Verkoop Prys',
    type: 'Tipe',
    marketPrice: 'Mark Prys',
    pairTrade: 'Paar Handel',
    buyFee: 'Koop Fooi',
    sellFee: 'Verkoop Fooi'
  },

  // 个人中心
  mine: {
    totalAssets: 'Totale Bates',
    availableFunds: 'Beskikbare Fondse',
    profitableFunds: 'Winsgewende Fondse',
    freezeFunds: 'Vries Fondse',
    recharge: 'Herlaai',
    withdraw: 'Onttrek',
    bankCard: 'Bank Kaart',
    realNameAuth: 'Werklike Naam Verifikasie',
    fundRecords: 'Fonds Rekords',
    privacyPolicy: 'Privaatheid Beleid',
    changeLoginPassword: 'Verander Aanmeld Wagwoord',
    changeFundPassword: 'Verander Fonds Wagwoord',
    languageSwitch: 'Taal Wissel',
    service: 'Diens',
    logout: 'Meld Af'
  },

  // 存股借券
  stockLending: {
    title: 'Aandeel Leen',
    lendingList: 'Leen Lys',
    lendingPositions: 'Leen Posisies',
    placeOrder: 'Plaas Bestelling',
    referencePrice: 'Verwysing Prys',
    referenceRate: 'Verwysing Koers',
    lendingDays: 'Leen Dae',
    noThreshold: 'Geen Drempel',
    tenThousand: 'Tienduisend',
    minLendingAmount: 'Minimum Leen Bedrag',
    requiredShares: 'Vereiste Sertifikate',
    closedType: 'Geslote Tipe',
    type: 'Tipe',
    ended: 'Beëindig',
    lending: 'Besig met Leen',
    lendingIncome: 'Leen Opbrengs',
    lendingRate: 'Leen Rente',
    lendingShares: 'Leen Sertifikate',
    lendingMarketValue: 'Leen Mark Waarde',
    lendingTime: 'Leen Tyd',
    lend: 'Leen',
    buyShares: 'Koop Sertifikate',
    enterBuyShares: 'Voer Koop Sertifikate In'
  },

  // 新股申购
  newStock: {
    title: 'Nuwe Aandeel Inskrywing',
    pending: 'Wag vir Inskrywing',
    subscribing: 'Besig met Inskrywing',
    ended: 'Inskrywing Beëindig',
    enterQuantity: 'Voer Inskrywing Hoeveelheid In'
  },

  // 大宗交易
  blockTrade: {
    title: 'Groothandel Transaksies',
    stockList: 'Aandeel Lys',
    purchaseRecords: 'Koop Rekords',
    enterBuyShares: 'Voer Koop Sertifikate In'
  },

  // 下拉刷新
  refresh: {
    pullToRefresh: 'Trek af om te herlaai...',
    releaseToRefresh: 'Los om te herlaai...',
    loading: 'Laai...'
  },

  // 服务端返回的常见消息（保持中文键名以便服务端消息翻译）
  '查询成功': 'Navraag suksesvol',
  '参数错误': 'Parameter fout',
  '操作成功': 'Operasie suksesvol',
  '操作失败': 'Operasie misluk',
  '余额不足': 'Onvoldoende balans',
  '请输入购买张数': 'Voer koop sertifikate in'
}
