<template>
	<!-- 市场 -->
	<div class="page ">
		<div class="header flex flex-b">
			<div class="icon logo animate__animated animate__fadeIn">
				<img :src="$cfg.logo" />
			</div>

			<div class="flex">
				<div class="icon sou2 animate__animated animate__fadeIn" @click="$toPage('/favorite/search')"></div>
				<div class="icon tz animate__animated animate__fadeIn" @click="$toPage('/information/userInfo')"></div>
			</div>
		</div>

		<!-- 切换列表内容显示 -->
		<div class="tabs flex flex-b">
			<div class="tab-item flex flex-c" v-for="(item, i) in tabList" :key="i" @click="changePage(item.type)"
				:class="{ active: currentIndex == item.type }">
				{{ $t(item.name) }}
				<van-icon class="more animate__animated animate__fadeIn" name="arrow-down" color="#6970af"
					size="0.14rem" @click.stop="showList" v-show="currentIndex == 1 && i == 1" />
			</div>

			<!-- 下拉切换显示 -->
			<div class="xl-list animate__animated animate__fadeInDown" v-if="show">
				<div class="xl-item" :class="{ active: type == item.type }" v-for="(item, i) in typeList1" :key="i"
					@click="changeType(item.type)">
					{{ $t(item.name) }}
				</div>
			</div>
		</div>

		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<template v-if="currentIndex == 0">
				<van-skeleton title :row="5" :loading="loading">
					<!-- 指数列表 -->
					<div class="index">
						<!-- <div class="t">{{ $t("newt").t47 }}</div> -->
						<div class="nums flex flex-b">
							<div class="nums-item" :class="{ center: i == 1 }" v-for="(item, i) in list" :key="i" @click=" $toDetail( `/market/stockDetailzs?symbol=${item.stock_id}`,  item )">
								<div class="name">
									{{ item.ko_name }}
								</div>
								<div class="t flex flex-c" :class="{ die: item.gain < 0 }">
									{{ $formatMoney(item.close, 2) }}
									<div class="icon animate__animated animate__fadeIn"
										:class="item.gain < 0 ? 'down1' : 'up1'"></div>
								</div>
								<div class="t1" :class="{ die: item.gain < 0 }">
									{{ item.gain <= 0 ? "" : "+"
                  }}{{ $formatMoney(item.gainValue, 2) }} ({{
                    item.gain <= 0 ? "" : "+"
                  }}{{ item.gain }}%)
								</div>
							</div>
						</div>
					</div>
				</van-skeleton>
				<!-- 市场特色列表 -->
				<teShe />
				<!-- 净交易列表 -->
				<!-- <clearDeal /> -->
			</template>

			<template v-if="currentIndex == 1">
				<featuredItems :isType="type" />
			</template>
			<template v-if="currentIndex == 2">
				<marketIndicators />
			</template>
			<template v-if="currentIndex == 3">
				<marketProblem />
			</template>
		</van-pull-refresh>

		<loading ref="loading" />
		<tab-bar :current="1"></tab-bar>
	</div>
</template>

<script>
	import teShe from "./components/teShe";
	import clearDeal from "./components/clearDeal";
	import marketProblem from "./components/marketProblem";
	import marketIndicators from "./components/marketIndicators";
	import featuredItems from "./components/featuredItems";

	export default {
		name: "",
		props: {},
		data() {
			return {
				type: 0,
				show: false,
				currentIndex: 0,
				tabList: [{
						name: "自选行情",
						type: 0
					},
					{
						name: "特色项目",
						type: 1
					},
					{
						name: "市场指标",
						type: 2
					},
					{
						name: "市场问题",
						type: 3
					},
				],
				loading: true, //控制显示骨架屏占位显示
				isLoading: false,
				isShow: false,
				list: [],
				typeList1: [{
						name: "股票价格",
						type: "0",
					},
					{
						name: "交易量",
						type: "1",
					},
					{
						name: "投资者",
						type: "2",
					},
					{
						name: "金融",
						type: "3",
					},
					{
						name: "利润",
						type: "4",
					},
					{
						name: "股票",
						type: "5",
					},
					{
						name: "卖空",
						type: "6",
					},
				],
			};
		},
		components: {
			teShe,
			clearDeal,
			marketProblem,
			marketIndicators,
			featuredItems,
		},
		created() {
			if (this.$route.query.type) {
				this.currentIndex = this.$route.query.type;
			}
		},
		mounted() {
			this.getIndexList();
		},
		computed: {},
		methods: {
			showList() {
				this.show = !this.show;
			},
			changeType(type) {
				this.type = type;
				this.show = false;
			},
			changePage(index) {
				this.currentIndex = index;
				this.show = false;
			},
			// 下拉刷新
			onRefresh() {
				this.currentIndex = 0;
				this.getIndexList();
			},
			getIndexList() {
				// 这里默认展示韩国的前三个
				this.$server.post("/transaction/gszk", {
					type: "gngs"
				}).then((res) => {
					this.loading = false;
					this.isLoading = false;
					if (res.status == 1) {
						let arr = [];
						res.data.forEach((item) => {
							arr.push({
								ko_name: item.details ?
									item.details.ko_name : item.code.toLocaleUpperCase(),
								time: this.$formatDate(
									"YYYY.MM.DD hh:mm:ss",
									new Date().getTime()
								),
								gainValue: item.close - item.prev_close,
								gain: (
									((item.close - item.prev_close) / item.prev_close) *
									100
								).toFixed(2),
								...item,
							});
						});
						this.list = arr;
					}
				});
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.96rem 0 0.6rem;
		min-height: 100vh;
	}

	::v-deep .van-skeleton__row {
		background-color: transparent !important;
	}

	::v-deep .van-skeleton__title {
		background-color: transparent !important;
	}

	.tabs {
		padding: 0 0.1rem;
		background: #ffffff;
		box-shadow: -0.01rem 0.02rem 0.02rem 0rem rgba(175, 175, 175, 0.25);
		position: fixed;
		width: 100%;
		top: 0.5rem;
		left: 0;
		z-index: 999;

		.xl-list {
			position: absolute;
			width: 100%;
			background-color: #fff;
			top: 0.36rem;
			left: 0;

			.xl-item {
				padding: 0.1rem;

				&.active {
					background: #6970af;
					color: #fff;
				}
			}
		}

		.tab-item {
			padding: 0.1rem 0;
			flex: 1;
			font-size: 0.12rem;
			color: #a1a1a1;
			text-align: center;
			position: relative;

			.more {
				margin-left: 0.05rem;
			}

			&::after {
				content: "";
				width: 50%;
				height: 0.02rem;
				position: absolute;
				bottom: 0;
				left: 50%;
				transform: translateX(-50%);
				background: transparent;
			}

			&.active {
				color: #6970af;

				&::after {
					background: #6970af;
				}
			}
		}
	}

	.index {
		.t {
			font-weight: 500;
			color: #1e1e1e;
			padding: 0 0.1rem 0.1rem;
		}
	}

	.nums {
		text-align: center;
		padding: 0.1rem 0;
		// border-top: 0.01rem solid #f5f5f5;
		border-bottom: 0.01rem solid #f5f5f5;

		.nums-item {
			width: 32%;

			&.center {
				border-left: 0.02rem solid #bbc5c1;
				border-right: 0.02rem solid #bbc5c1;
			}

			.name {
				font-weight: 600;
				font-size: 0.12rem;
				margin-bottom: 0.05rem;
			}

			.icon {
				margin-left: 0.05rem;
			}

			.t {
				font-weight: 500;
				font-size: 0.16rem;
				color: #c5585e;
				margin: 0.1rem 0;

				&.die {
					color: #4f8672;
				}
			}

			.t1 {
				font-size: 0.12rem;
				color: #c5585e;

				&.die {
					color: #4f8672;
				}
			}
		}
	}

	.header {
		width: 100vw;
		height: 0.5rem;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 999;
		background: #fff;
		padding: 0 0.1rem;

		.t {
			font-weight: 500;
			font-size: 0.18rem;
			color: #000000;
			text-align: center;
			line-height: 0.5rem;
		}

		.sou2 {
			margin-right: 0.1rem;
		}
	}
</style>