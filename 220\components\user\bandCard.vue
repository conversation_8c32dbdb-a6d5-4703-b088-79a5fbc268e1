<template>
	<div class="bandCard">
		<div class="hesadf">
			<top :title="$t(text)"></top>
		</div>
		<div v-if="isEdit==false">
			<div class="finish text-center">
				<img src="../../assets/v2/sfz.png" />
				<!-- <div v-if="userdata.is_true==1">{{$t('bandCard').txt9}}</div>
				<div v-else-if="userdata.is_true==3">{{$t('bandCard').txt10}}</div> -->
				<div class="userInfo">
					<div class="item">
						<div>{{$t('bandCard').txt11}}</div>
						<span>{{userdata.realname}}</span>
					</div>
					<div class="item">
						<div>{{$t('bandCard').txt12}}</div>
						<span>{{hideMiddleDigits(userdata.id_card)}}</span>
					</div>
				</div>
			</div>
		</div>
		<div v-else>
			<div class="infoList">
				<div class="list">
					<div class="txt">{{$t('bandCard').txt1}}</div>
					<div class="input-list flex">
						<input v-model="form.true_name" :placeholder="$t('bandCard').txt2" />
					</div>
					<div class="txt">{{$t('bandCard').txt3}}</div>
					<div class="input-list flex">
						<input v-model="form.card_id" :placeholder="$t('bandCard').txt4" />
					</div>
					<div class="upload">
						<div>{{$t('bandCard').txt5}}</div>
						<div class="flex flex-b">
							<div class="upload-box text-center">
								<div class="img">
									<img :src="imgList.zheng" v-if="form.frontcard" />
									<img src="../../assets/skin/mine/bandCardImg2.png" v-if="!form.frontcard" />
									<input accept="image/*" type="file" @change="changeing($event)" />
								</div>
								<div class="txt" v-if="!form.frontcard">{{$t('bandCard').txt6}}</div>
							</div>
							<div class="upload-box text-center">
								<div class="img">
									<img :src="imgList.fan" v-if="form.backcard" />
									<img src="../../assets/skin/mine/bandCardImg2.png" v-if="!form.backcard" />
									<input accept="image/*" type="file" @change="changeingf($event)" />
								</div>
								<div class="txt" v-if="!form.backcard">{{$t('bandCard').txt7}}</div>
							</div>
						</div>
					</div>
					<div class="btn-big" @click="goUrl">{{ $t('bandCard').txt8 }}</div>
				</div>
			</div>
		</div>
		<div class="note">{{$t('bandCard').note}}</div>
	</div>
</template>

<script>
	import Vue from "vue";
	import qs from "qs";
	import axios from "axios";
	import top from "../bar/toper.vue";
	import imgurl from "../../lib/common.js";
  import {compress} from "../../lib/imgutils"
	import {
		Toast
	} from "vant";
	Vue.use(Toast);
	export default {
		name: "Finsh",
		data() {
			return {
				text: this.$t('bandCard').title,
				userdata: [],
				isEdit: false,
				form: {
					frontcard: '',
					backcard: '',
					true_name: '',
					card_id: ''
				},
				imgList: {
					fan: '',
					zheng: ''
				}
			};
		},
		components: {
			top,
		},
		computed: {
			hideMiddleDigits() {
				return (str) => {
					if (!str) return;
					if (str.length == 11) {
						str = str.replace(/(\d{3})\d{6}(\d{2})/, '$1******$2');
					} else {
						str = str.replace(/(\d{3})\d{4}(\d{0})/, '$1****$2');
					}
					return str;
				}
			}
		},
		methods: {
			getUser() {
				this.$server.post("/user/getUserinfo").then((str) => {
					if (str.data.status == 1) {
						this.userdata = str.data.data;
						this.isEdit = false;
						//1 已实名 2审核失败 3审核中
						if (str.data.data.is_true === 3 || str.data.data.is_true == 1) {
							this.isEdit = false;
						} else {
							this.isEdit = true;
						}
					}
				});
			},
			goUrl() {
				if (!this.form.true_name) {
					Toast({
						message: this.$t('bandCard').tip1,
						duration: 3000,
					});
					return;
				}
				if (!this.form.card_id || this.form.card_id.length < 12) {
					Toast({
						message: this.$t('bandCard').tip2,
						duration: 3000,
					});
					return;
				}
				if (!this.form.frontcard) {
					Toast({
						message: this.$t('bandCard').tip3,
						duration: 3000,
					});
					return;
				}
				if (!this.form.backcard) {
					Toast({
						message: this.$t('bandCard').tip4,
						duration: 3000,
					});
					return;
				}
				this.$server.post('/user/shiming', {
						id_card: this.form.card_id,
						realname: this.form.true_name,
						frontcard: this.form.frontcard,
						backcard: this.form.backcard
					})
					.then(res => {
						if (res.data.status == 1) {
							Toast({
								message: this.$t(res.data.msg),
								duration: 2000,
							});
							setTimeout(() => {
								this.$router.go(-1);
							}, 1500);
						} else {
							Toast({
								message: this.$t('bandCard').tip8,
								duration: 3000,
							});
						}
					});
			},
			changeing(e) {
				var file = e.target.files[0];
				var that = this;
        compress(file,{maxWidth:640},file=>{
          var formdata = new FormData();

          formdata.append("card", file);

          this.$server.post("/common/upload1", formdata).then((res) => {
            if (res.data.status == 1) {
              that.form.frontcard = res.data.data;
              Toast({
                message: this.$t("bandCard").tip6,
                duration: 3000,
              });
              // this.imgz = "api/"+res.data.data;
              this.imgList.zheng = imgurl.url.imgUrls + res.data.data;
            }
          })
        })


			},
			changeingf(e) {
				var file = e.target.files[0];
				var that = this;
        compress(file,{maxWidth:640},file=>{
          var formdata = new FormData();

          formdata.append("card", file);

          this.$server.post("/common/upload1", formdata).then((res) => {
            if (res.data.status == 1) {
              that.form.backcard = res.data.data;
              Toast({
                message: this.$t("bandCard").tip6,
                duration: 3000,
              });
              this.imgList.fan = imgurl.url.imgUrls + res.data.data;
            }
          })
        })


			},
		},
		destroyed() {},
		mounted() {
			this.getUser();
		},
	};
</script>

<style lang="less">
	.bandCard {
		background:#0f161c;
		min-height: 100vh;
		padding-top: .44rem;
		padding-bottom: .1rem;
		max-width: 100%;
		overflow: hidden;

		.hesadf {
			height: 0.44rem;
			width: 100%;
			background: #fff;
			position: fixed;
			top: 0;
			left: 0;
			z-index: 999;
		}

		.finish {
			font-weight: 400;
			font-size: .16rem;
			color: #fff;

			//height:calc(100vh - .65rem);
			img {
				width: 1.77rem;
				height: 1.14rem;
				margin: .4rem auto .23rem 0;
			}

			.userInfo {
				margin-top: .3rem;
				margin: .3rem .12rem 0 .12rem;
				.item {
					background: #000000;
					border-radius: 0.1rem;
					margin: .12rem 0;
					padding: 0.1rem;
					font-weight: 400;
					font-size: 0.14rem;
					color: #FFFFFF;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: flex-start;

          div{
            text-align: left;
          }

					span {
						color: #fff
					}
				}
			}
		}

		.infoList {
			position: relative;
			width: 100%;
			min-height: 1.5rem;

			.list {
				margin-top: 0.1rem;
				padding: .12rem;
				position: relative;
				z-index: 300;
				.txt {
					font-weight: 400;
					font-size: 0.12rem;
					color: #FFFFFF;
					margin-bottom: 0.1rem;
				}
				.input-list {
					font-weight: 500;
					font-size: .15rem;
					color: #fff;
					height: 0.46rem;
					background: #000000;
					border-radius: 0.1rem;
					padding: 0 0.15rem;
					margin-bottom: .17rem;
					input {
						width: 100%;
						margin: .1rem 0;
						background: none;
					}
				}

				.upload {
					font-weight: 400;
					font-size: 0.12rem;
					color: #FFFFFF;
					margin: .2rem 0;

					.upload-box {
						width: 1.61rem;
						position: relative;
						margin-top: 0.1rem;
						.img {
							width: 1.61rem;
							height: 1.06rem;
							border-radius: .04rem;
							margin: 0 auto .1rem auto;
							position: relative;

							input {
								width: 100%;
								height: 100%;
								opacity: 0;
								position: absolute;
								top: 0;
								left: 0;
							}
						}

						img {
							width: 100%;
							height: 100%;
							display: block;
						}

						.txt {
							position: absolute;
							bottom: .15rem;
							left: 0;
							width: 100%;
							font-weight: 400;
							font-size: 0.14rem;
							color: #000;
						}
					}
				}

			}
		}

		.note {
			font-weight: 500;
			font-size: .12rem;
			color: #999999;
			margin: .4rem .13rem;
		}
	}
</style>