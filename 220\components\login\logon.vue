<template>
	<div class="logon">
		<div class="logo text-center" :class="{show:logoShow}">
			<img src="../../assets/skin/start/img1.png" />
		</div>
		<div class="list" :class="{show:listShow}">
			<div class="title text-center">
				<div>{{$t('logon').txt1}}</div>
				<span>{{$t('logon').txt2}}</span>
			</div>
		</div>
		<div class="Btn flex" :class="{show:btnShow}">
			<button class="link1 flex1 flex flex-c" @click="clickSkip()">{{$t('logon').btn1}}</button>
			<button class="link2 flex1 flex flex-c" @click="clickNext('/login/logon2')">{{$t('logon').btn2}}</button>
		</div>
	</div>
</template>

<script>
	export default {
		name: "logon",
		data() {
			return {
				logoShow: false,
				listShow: false,
				btnShow: false,
				customer: '',
				logo: '../../assets/skin/logo2.png'
			};
		},
		mounted() {
			let _this = this
			//uni.setNavigationBarTitle({ title: _this.$t('start').title });
			setTimeout(function() {
				_this.logoShow = true
				setTimeout(function() {
					_this.listShow = true
					setTimeout(function() {
						_this.btnShow = true
					}, 100)
				}, 300)
			}, 800)
		},
		methods: {
			clickSkip() {
				let token = window.localStorage.getItem("tokend") || false;
				if (token) {
					this.$router.push({
						path: "/home/<USER>",
					});
				} else {
					this.$router.push({
						path: "/login/login",
					});
				}
			}
		}
	}
</script>

<style scoped lang="less">
	.logon {
		background: #fff;
		overflow-x: hidden;
		height: 100vh;
		color: #333333;

		.logo {
			opacity: 0;
			-webkit-transition-duration: 1s;
			-moz-transition-duration: 1s;
			-o-transition-duration: 1s;
			margin-left: -3rem;
			width: 100%;
			padding-top: 1.16rem;

			img {
				width: 1.44rem;
				height: 1.78rem;
			}

			&.show {
				margin-left: 0;
				opacity: 1;
			}
		}

		.list {
			width: 100%;
			opacity: 0;
			-webkit-transition-duration: 1s;
			-moz-transition-duration: 1s;
			-o-transition-duration: 1s;
			margin-left: -3rem;

			.title {
				font-size: .24rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				color: #212121;
				margin-top: .61rem;
				display: flex;
				flex-direction: column;

				span {
					margin: .1rem .15rem;
					font-size: .12rem;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					color: #666666;
				}
			}

			&.show {
				margin-left: 0;
				opacity: 1;
			}
		}

		.Btn {
			position: fixed;
			bottom: 0;
			left: 0;
			width: 100%;
			background: #FFFFFF;
			padding: .15rem 0;
			opacity: 0;
			-webkit-transition-duration: 1s;
			-moz-transition-duration: 1s;
			-o-transition-duration: 1s;
			margin-left: -3rem;

			button {
				height: .44rem;
				border-radius: .27rem;
				font-size: .15rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				color: #333333;
				border: 0;

				&.link1 {
					border: .01rem solid #DCDCDC;
					background: #fff;
					margin-left: .13rem;
					margin-right: .01rem;
				}

				&.link2 {
					background: #7EAEF2;
					margin-right: .13rem;
					margin-left: .1rem;
					color: #F5F5F5;
				}
			}

			&.show {
				margin-left: 0;
				opacity: 1;
			}
		}
	}
</style>