<template>
	<!-- 存股借券 -->
	<div class="page">
		<top-back title="存股借券"></top-back>
		<div class="nav-box flex">
			<div class="nav-item" v-for="(item, index) in navList" :key="index"
				:class="{ active: currmentIndex == item.type }" @click="changeNav(item.type)">
				{{ item.name }}
			</div>
		</div>
		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<van-skeleton title :row="26" :loading="loading">
				<div class="cot">
					<div class="list" v-show="currmentIndex == 1">
						<div class="list-item" v-for="(item, index) in scrollList" :key="index" @click="xd(item)">
							<div class="flex flex-b name">
								<div class="flex">
									<div class="t">{{ item.name }}</div>
									<div class="code">{{ item.symbol }}</div>
								</div>
								<div class="but">下單</div>
							</div>
							<!-- <div class="middle flex flex-b">
								<div class="flex-xolumn-item">
									<div class="t2">{{ $formatMoney(item.price) }}</div>
									<div class="t1">參考價格</div>
								</div>
								<div class="flex-xolumn-item">
									<div class="t2">{{ item.lx }}%</div>
									<div class="t1">參考費率</div>
								</div>
							</div> -->
							<div class="items flex flex-wrap">
								<div class="item">
									<div class="t4">{{ $formatMoney(item.price) }}</div>
									<div class="t3">參考價格</div>
								</div>
								<div class="item">
									<div class="t4">{{ item.lx }}%</div>
									<div class="t3">參考費率</div>
								</div>
								<div class="item">
									<div class="t4">{{ item.day }}</div>
									<div class="t3">出借天數</div>
								</div>
								<div class="item">
									<div class="t4">
										{{ item.min == 0 ? "無門檻" : item.min / 10000 + "萬" }}
									</div>
									<div class="t3">最小借券金額</div>
								</div>
								<div class="item">
									<div class="t4">{{ item.num }}</div>
									<div class="t3">需求張數</div>
								</div>
								<div class="item">
									<div class="t4 green">封閉型</div>
									<div class="t3">類型</div>
								</div>
							</div>
						</div>
						<no-data v-if="!scrollList.length"></no-data>
					</div>

					<div class="list" v-show="currmentIndex == 2">
						<div class="list-item" v-for="(item, index) in stockList" :key="index">
							<div class="flex flex-b name">
								<div class="flex">
									<div class="t">{{ item.stock_name }}</div>
									<div class="code">{{ item.stock_code }}</div>
								</div>
								<div class="but">{{ item.status == 1 ? '已結束': '借券中' }}</div>
							</div>
							<!-- <div class="middle">
								<div class="flex flex-b">
									<div class="t1">借券收益</div>
									<div class="t2">
										{{ $formatMoney((item.credit * item.lx) / 100) }}
									</div>
								</div>

								<div class="flex flex-b">
									<div class="t1">借券利率</div>
									<div class="t2">{{ item.lx }}%</div>
								</div>
							</div> -->
							<div class="items flex flex-wrap">
								<div class="item">
									<div class="t4">
										{{ $formatMoney((item.credit * item.lx) / 100) }}
									</div>
									<div class="t3">借券收益</div>
								</div>
								<div class="item">
									<div class="t4">{{ item.lx }}%</div>
									<div class="t3">借券利率</div>
								</div>
								<div class="item">
									<div class="t4">{{ item.day }}</div>
									<div class="t3">借券天數</div>
								</div>
								<div class="item">
									<div class="t4">{{ $formatMoney(item.zhang, 0) }}</div>
									<div class="t3">借券張數</div>
								</div>
								<div class="item">
									<div class="t4">{{ $formatMoney(item.credit) }}</div>
									<div class="t3">借券市值</div>
								</div>
								<div class="item">
									<div class="t4">{{ changeDate(item.buy_time) || "-" }}</div>
									<div class="t3">借券時間</div>
								</div>
							</div>
						</div>
						<no-data v-if="!stockList.length"></no-data>
					</div>
				</div>
			</van-skeleton>
		</van-pull-refresh>

		<!-- 弹出-->
		<van-popup v-model="show" position="center" round :style="{ width: '90%' }">
			<div class="pop">
				<div class="pop-price t-c">借券</div>
				<div class="pad">
					<div class="ipt">
						<div class="tt">買入張數</div>
						<input class="ipt" type="number" placeholder="請輸入購買張數" v-model="quantity" />
					</div>
					<div class="defbtn" @click="qrXd" style="">確定</div>
				</div>
			</div>
			<!-- <div class="icon close" @click="show = false"></div> -->
		</van-popup>
		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "coupons",
		data() {
			return {
				loading: true,
				isLoading: false,
				show: false,
				navList: [{
						name: "借券列表",
						type: 1
					},
					{
						name: "借券持倉",
						type: 2
					},
				],
				currmentIndex: 1,
				stockList: [
					// {
					//   stock_name: "name",
					//   stock_code: "code",
					//   xgstate: "正在派息",
					//   day: 5,
					//   zhang: 100,
					//   lx: 0.1,
					//   credit: 1,
					//   buy_time: 123,
					//   price: 1000,
					// },
					// {
					//   stock_name: "name",
					//   stock_code: "code",
					//   xgstate: "正在派息",
					//   day: 5,
					//   zhang: 100,
					//   lx: 0.1,
					//   credit: 1,
					//   buy_time: 123,
					//   price: 100,
					// },
				],
				scrollList: [
					// {
					//   name: "name",
					//   code: "code",
					//   xgstate: "正在派息",
					//   day: 5,
					//   zhang: 100,
					//   lx: 0.1,
					//   credit: 1,
					//   buy_time: 123,
					//   price: 100,
					// },
					// {
					//   name: "name",
					//   code: "code",
					//   xgstate: "正在派息",
					//   day: 5,
					//   zhang: 100,
					//   lx: 0.1,
					//   credit: 1,
					//   buy_time: 123,
					//   price: 100,
					// },
				],
				stockId: null,
				quantity: null,
			};
		},
		computed: {
			formatTime() {
				return (val) => {
					let str = val.split(" ");
					return str[0];
				};
			},
		},
		created() {
			this.getScrollList();
		},
		methods: {
			changeDate(timestamp) {
				let date = new Date(timestamp * 1000);
				let Year = date.getFullYear();
				let Moth =
					date.getMonth() + 1 < 10 ?
					"0" + (date.getMonth() + 1) :
					date.getMonth() + 1;
				let Day = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
				let Hour = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
				let Minute =
					date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
				let Sechond =
					date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
				let GMT =
					Year +
					"/" +
					Moth +
					"/" +
					Day +
					"   " +
					Hour +
					":" +
					Minute +
					":" +
					Sechond;
				return GMT;
			},
			onRefresh() {
				this.currmentIndex = 1;
				this.getScrollList();
			},
			qrXd() {
				if (!this.quantity || this.quantity == 0)
					return uni.showToast({
						title: "請輸入購買張數",
						icon: "none",
					});
				this.$server
					.post("/trade/tojiequan", {
						id: this.stockId,
						zhang: this.quantity,
						type: "twd",
					})
					.then((res) => {
						if (res.status == 1) {
							this.show = false;
						}
						if (res.msg) {
							this.$toast(res.msg);
						}
					});
			},
			xd(item) {
				this.show = true;
				this.stockId = item.id;
			},
			changeNav(index) {
				this.currmentIndex = index;
				this.$refs.loading.open(); //开启加载

				if (index == 2) {
					this.getStockList();
				} else {
					this.getScrollList();
				}
			},

			getScrollList() {
				this.$server.post("/trade/cgjqlist", {
					type: "twd"
				}).then((res) => {
					this.$refs.loading && this.$refs.loading.close(); //开启加载
					this.loading = false;
					this.isLoading = false;
					if (res.status == 1) {
						this.scrollList = res.data;
					}
				});
			},
			getStockList() {
				this.$server.post("/trade/user_cgjqlist", {
					type: "twd"
				}).then((res) => {
					this.$refs.loading && this.$refs.loading.close(); //开启加载
					if (res.status == 1) {
						this.stockList = res.data;
					}
				});
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 1rem 0 0.1rem;
		min-height: 100vh;
		position: relative;
	}

	::v-deep .van-skeleton__row {
		background-color: transparent !important;
	}

	::v-deep .van-skeleton__title {
		background-color: transparent !important;
	}

	.nav-box {
		width: 92%;
		position: fixed;
		top: 0.5rem;
		left: 50%;
		transform: translateX(-50%);
		z-index: 999;
		height: 0.38rem;
		background: #232429;
		border-radius: 0.19rem;
		padding: 0.01rem;
		.nav-item {
			flex: 1;
			font-family: PingFang SC;
			font-weight: 500;
			font-size: 0.13rem;
			color: #999999;
			line-height: 0.36rem;
			text-align: center;
			position: relative;
	
			&.active {
				height: 0.36rem;
				background: #8DFD99;
				border-radius: 0.16rem;
				color: #010101;
				line-height: 0.36rem;
				position: relative;
				// &::after {
				// 	position: absolute;
				// 	content: '';
				// 	bottom: 0;
				// 	left: 50%;
				// 	transform: translateX(-50%);
				// 	width: 50%;
				// 	height: 0.02rem;
				// 	background-color: #E5C79F;
				// }
			}
		}
	}

	.van-popup {
		background-color: transparent;
	}

	.close {
		margin: 0.1rem auto 0;
	}

	.pop {
		background: #232429;
		border-radius: 0.12rem;
		height: 100%;
		padding-bottom: 0.1rem;
		position: relative;
		.pad {
			padding: 0.2rem 0.15rem 0;
		}

		.btips {
			padding: 0.15rem 0;
			line-height: 0.24rem;
			.t2 {
				color: rgba(254, 2, 0, 1);
			}
		}

		.pop-title {
			font-size: 0.16rem;
			text-align: center;
		}

		.pop-price {
			background: linear-gradient(90deg, #98EF86, #C7F377);
			padding: 0.12rem;
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 0.19rem;
			color: #000;
			text-align: center;

			.t1 {
				font-size: 0.16rem;
				color: #000000;
				margin-bottom: 0.05rem;
			}
		}

		.ipt {
			.tt {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.12rem;
				color: #B6B2AD;
			}

			input {
				margin-top: 10px;
				width: 100%;
				line-height: 0.44rem;
				padding: 0 0.1rem;
				height: 0.44rem;
				background: #434446;
				border-radius: 0.04rem 0.04rem 0.04rem 0.04rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.12rem;
				color: #fff;
				&::placeholder {
					font-weight: 500;
					font-size: 0.12rem;
					color: #9a9fa5;
				}
			}

			.t1 {
				font-size: 0.12rem;
				color: #9a9fa5;
			}

			.t2 {
				font-size: 0.12rem;
				color: #7da1ef;
			}

			.mt10 {
				margin-top: 0.05rem;
			}
		}

		.pop-num {
			margin-top: 0.15rem;

			input {
				margin: 0.05rem 0;
				width: 100%;
				background: #f8f8f8;
				border-radius: 0.06rem;
				border: 0.01rem solid #b8b8b8;
				height: 0.4rem;
				line-height: 0.4rem;
				padding: 0 0.1rem;

				&::placeholder {
					font-size: 0.12rem;
					color: #606060;
				}
			}
		}

		.txt {
			font-size: 0.12rem;
			color: #9a9fa5;

			span {
				font-size: 0.12rem;
				color: #c5585e;
			}
		}

		.b-btn {
			width: 100%;
		}
	}

	.cot {
		margin: 0 0.12rem;

		.list {
			.list-item {
				margin-bottom: 0.1rem;
				padding-bottom: 0.1rem;
				width: 100%;
				background: #232429;
				border-radius: 0.13rem;
				.but {
					padding: 0 0.2rem;
					background: #8EFE99;
					border-radius: 0.13rem;
					text-align: center;
					font-family: PingFang SC;
					font-weight: 500;
					font-size: 0.13rem;
					color: #000000;
					line-height: 0.3rem;
				}

				.name {
					padding: 0.12rem;
					.t {
						font-family: PingFang SC;
						font-weight: 500;
						font-size: 0.15rem;
						color: #FFFFFF;
					}
					.code {
						margin-left: 0.1rem;
						font-family: PingFang SC;
						font-weight: 400;
						font-size: 0.11rem;
						color: #999999;
					}
				}

				.middle {
					padding: 0.12rem;
					.t1 {
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 0.12rem;
						color: #B6B2AD;
					}

					.t2 {
						font-family: PingFangSC, PingFang SC;
						font-weight: 500;
						font-size: 0.12rem;
						color: #E1E5ED;
					}
				}

				.items {
					margin: 0 0.12rem;
					padding: 0.12rem;
					background: #434446;
					border-radius: 0.09rem;
					.item{
						width: 25%;
						text-align: center;
						padding: 0.05rem 0;
					}
					.t3 {
						font-family: PingFang SC;
						font-weight: 400;
						font-size: 0.11rem;
						color: #999999;
					}

					.t4 {
						margin-bottom: 0.05rem;
						font-family: PingFang SC;
						font-weight: 500;
						font-size: 0.14rem;
						color: #FFFFFF;
						&.green {
							color: #FF5683;
						}
					}
				}
			}
		}
	}
</style>