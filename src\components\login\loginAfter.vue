<template>
	<div class="pages">
		<!-- <div class="info nflex nflex-lc">
			<img :src="$config.baseUrl + config.logo" alt="logo">
			<span v-show="config.title">{{ config.title }}</span>
		</div> -->
		<div class="flex-column-item logo" style="padding-top:.5rem;">
<!--      <div style="display: flex;align-items: center;background: #212121;width: 2.86rem;height: .9rem;justify-content: center;border-radius: .1rem;">-->
<!--        <img src="../../assets/v2/newlogo.png" style="width: .5rem;height: .5rem;">-->
<!--        <div style="font-size: .35rem;color: #dc183b;margin-left: .1rem;font-weight: bold;">證券櫃買中心</div>-->
<!--      </div>-->
      <div style="display: flex;align-items: center;background: #212121;width: 3rem;height: .9rem;justify-content: center;border-radius: .1rem;">
        <img :src="config.logo" style="width: .5rem;height: .5rem;">
        <div style="font-size: .3rem;color: #dc183b;margin-left: .1rem;font-weight: bold;">{{config.title}}</div>
      </div>
		</div>
		<div class="texts">
			<span class="texts-i" v-for="(item, index) in textList" :key="index">{{ item }}</span>
		</div>
	</div>
</template>

<script>
	export default {
		name: 'Connect',
		data() {
			return {
				config: {},
				progress: 0,
				textList: [],
				textLists: [
					this.$t('伺服器登入成功'),
					this.$t('開始獲取使用者權限檔案(完成)'),
					this.$t('即時伺服器：TFN機房，連結中'),
					this.$t('………')
				]
			}
		},
		created() {
			this.getConfig()
			this.updateProgress()
		},
		methods: {
			getConfig() {
				this.$server.post('/common/config',{
          type:'all'
        }).then(res => {
					if (res && res.status == 1) {
						const arr = res.data
						const obj = {}
						arr.forEach(item => {
							obj[item.name] = item.value
						})
						this.config = obj
					}
				})
			},
			updateProgress() {
				setTimeout(() => {
					this.progress += 25
					const idx = (this.progress / 25) - 1
					this.textList.push(this.textLists[idx])
					if (this.progress < 100) {
						this.updateProgress()
					} else {
						setTimeout(() => {
							// this.$toPage('/home/<USER>')
              this.$router.replace('/home/<USER>');
						}, 1000)
					}
				}, 1000)
			}
		}
	}
</script>

<style lang="less" scoped>
	.pages {
		width: 100%;
		height: 100vh;
	}

	.info {
		width: 100%;
		height: 350px;

		img {
			width: 90px;
			height: 90px;
		}

		span {
			padding-left: 20px;
			font-family: PangMenZhengDaoBiaoTiTiMianFeiBan, PangMenZhengDaoBiaoTiTiMianFeiBan;
			font-weight: normal;
			font-size: 60px;
			color: #FFFFFF;
		}
	}

	.texts {
		padding: .15rem .35rem 0;

		.texts-i {
			padding: .05rem 0;
			display: block;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: .13rem;
			color: #999;
		}
	}
</style>
