
// 导入语言包
let en = require("./lang/en")
let tw = require("./lang/tw")
let af = require("./lang/af")
let zu = require("./lang/zu")
let xh = require("./lang/xh")

// 语言列表配置
export const langList = [
    {
        name: 'Afrikaans',
        key: 'af',
        nativeName: 'Afrikaans'
    },
    {
        name: 'English',
        key: 'en',
        nativeName: 'English'
    },
    {
        name: 'Zulu',
        key: 'zu',
        nativeName: 'isiZulu'
    },
    {
        name: 'Xhosa',
        key: 'xh',
        nativeName: 'isiXhosa'
    }
]

// 获取当前语言设置，默认为南非荷兰语
export function getCurrentLang() {
    return localStorage.getItem('language') || 'af';
}

// 设置语言
export function setLang(lang) {
    localStorage.setItem('language', lang);
}

// 获取语言包
function getLangPack(lang) {
    switch (lang) {
        case 'en':
            return en;
        case 'tw':
            return tw;
        case 'af':
            return af;
        case 'zu':
            return zu;
        case 'xh':
            return xh;
        default:
            return af; // 默认返回南非荷兰语
    }
}

let didnot = [];
export function initLang(Vue) {
    Vue.prototype.$t = (key) => {
        if (!key) {
            return '';
        }

        const currentLang = getCurrentLang();
        const langPack = getLangPack(currentLang);

        // 支持嵌套访问，如 'page.favorite.edit'
        if (key.includes('.')) {
            const keys = key.split('.');
            let value = langPack;

            for (const k of keys) {
                if (value && typeof value === 'object' && value[k] !== undefined) {
                    value = value[k];
                } else {
                    value = null;
                    break;
                }
            }

            if (value !== null) {
                return value;
            }

            // 如果当前语言不是英语，尝试使用英语作为后备
            if (currentLang !== 'en') {
                let enValue = en;
                for (const k of keys) {
                    if (enValue && typeof enValue === 'object' && enValue[k] !== undefined) {
                        enValue = enValue[k];
                    } else {
                        enValue = null;
                        break;
                    }
                }
                if (enValue !== null) {
                    return enValue;
                }
            }
        } else {
            // 兼容旧的直接key访问方式（主要用于API返回的消息）
            if (langPack[key]) {
                return langPack[key];
            }

            // 如果当前语言不是英语，尝试使用英语作为后备
            if (currentLang !== 'en' && en[key]) {
                return en[key];
            }
        }

        // 如果都没找到，返回原始key
        return key;
    }

    // 添加语言切换方法
    Vue.prototype.$setLang = (lang) => {
        setLang(lang);
        // 重新加载页面以应用新语言
        window.location.reload();
    }

    // 添加获取当前语言方法
    Vue.prototype.$getCurrentLang = getCurrentLang;

    // 添加获取语言列表方法
    Vue.prototype.$getLangList = () => langList;

    // 添加服务端返回文字的国际化处理方法
    Vue.prototype.$translateServerText = (text) => {
        if (!text) return '';

        const currentLang = getCurrentLang();
        const langPack = getLangPack(currentLang);

        // 首先尝试直接翻译
        if (langPack[text]) {
            return langPack[text];
        }

        // 如果当前语言不是英语，尝试使用英语作为后备
        if (currentLang !== 'en' && en[text]) {
            return en[text];
        }

        // 如果都没找到，返回原文
        return text;
    }

    let findObj = (obj, lv) => {
        for (let key in obj) {
            if (typeof obj[key] == 'object') {
                findObj(obj[key], lv + 1)
            } else if (lv > 0 && typeof obj[key] == 'string') {
                const currentLang = getCurrentLang();
                const langPack = getLangPack(currentLang);
                if (!langPack[obj[key]]) {
                    didnot.push(obj[key])
                }
            }
        }
    }
}
