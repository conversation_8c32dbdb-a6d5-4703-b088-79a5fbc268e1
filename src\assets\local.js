
// 导入语言包
let tw = require("./lang/tw")
let af = require("./lang/af")
let en = require("./lang/en")
let zu = require("./lang/zu")
let xh = require("./lang/xh")

// 支持的语言列表
const supportedLanguages = ['af', 'en', 'zu', 'xh'];
const defaultLanguage = 'af'; // 默认南非荷兰语

// 获取当前语言
const getCurrentLang = () => {
    const savedLang = uni.getStorageSync('language');
    return savedLang && supportedLanguages.includes(savedLang) ? savedLang : defaultLanguage;
};

// 获取语言包
const getLangPack = (lang) => {
    switch (lang) {
        case 'af': return af; // 南非荷兰语
        case 'en': return en; // 英语
        case 'zu': return zu; // 祖鲁语
        case 'xh': return xh; // 科萨语
        default: return af; // 默认使用南非荷兰语
    }
};

// 设置语言
const setLanguage = (lang) => {
    if (supportedLanguages.includes(lang)) {
        uni.setStorageSync('language', lang);
        return true;
    }
    return false;
};

// 获取语言列表
const getLanguageList = () => {
    return [
        { code: 'af', name: 'Afrikaans', nativeName: 'Afrikaans' },
        { code: 'en', name: 'English', nativeName: 'English' },
        { code: 'zu', name: 'Zulu', nativeName: 'isiZulu' },
        { code: 'xh', name: 'Xhosa', nativeName: 'isiXhosa' }
    ];
};

let didnot = [];

export function initLang(Vue) {
    Vue.prototype.$t = (key) => {
        if (!key) {
            return '';
        }

        const currentLang = getCurrentLang();
        const langPack = getLangPack(currentLang);

        // 简单的翻译逻辑
        if (langPack[key]) {
            return langPack[key];
        }

        // 如果当前语言包没有，尝试使用繁体中文作为后备
        if (tw[key]) {
            return tw[key];
        }

        return key;
    }

    // 服务端消息翻译函数
    Vue.prototype.$translateServerText = (text) => {
        if (!text) return '';

        const currentLang = getCurrentLang();
        const langPack = getLangPack(currentLang);

        // 服务端消息翻译
        if (langPack[text]) {
            return langPack[text];
        }

        // 后备翻译
        if (tw[text]) {
            return tw[text];
        }

        return text;
    }

    // 语言管理函数
    Vue.prototype.$getCurrentLang = getCurrentLang;
    Vue.prototype.$setLanguage = setLanguage;
    Vue.prototype.$getLanguageList = getLanguageList;

    let findObj = (obj, lv) => {
        for (let key in obj) {
            if (typeof obj[key] == 'object') {
                findObj(obj[key], lv + 1)
            } else if (lv > 0 && typeof obj[key] == 'string') {
                if (!tw[obj[key]]) {
                    didnot.push(obj[key])
                }
            }
        }
    }
}
