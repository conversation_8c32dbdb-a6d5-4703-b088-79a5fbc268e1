<template>
  <div class="dabg">
    <div class="gebgh">
      <top :title="$t(text)"></top>
    </div>

    <div class="jilkm" v-for="(item, i) in daty" :key="i">
      <div class="plkm">
        <p>
          <span>{{ $t("状态") }}</span>
          <a>{{ $t(item.stat) }}</a>
        </p>
        <p>
          <span>{{ $t("借款金额") }}</span>
          <a>{{ item.money }}</a>
        </p>
        <p>
          <span>{{ $t("订单编号") }}</span>
          <span>{{
            String(item.order_number).substring(
              0,
              String(item.order_number).length - 6
            )
          }}</span>
        </p>
        <p>
          <span>{{ $t("借款时间") }}</span>
          <span>{{ item.create_time }}</span>
        </p>
      </div>
    </div>

    <template v-if="!daty.length">
      <div class="no-data">
        {{ $t("暂无数据") }}
      </div>
    </template>
  </div>
</template>
<script type="text/javascript">
import Vue from "vue";
import qs from "qs";
import axios from "axios";
import { Toast } from "vant";
Vue.use(Toast);

import top from "../bar/toper.vue";

export default {
  name: "debtRecord",
  data() {
    return {
      text: "借款记录",
      daty: [],
    };
  },
  components: {
    top,
  },
  methods: {
    getdata() {
      this.$server.post("/user/loanlist", {type: "riyuan"}).then((str) => {
        if (str.data.status == 1) {
          function timestampToTime(timestamp) {
            let date = new Date(parseInt(timestamp) * 1000);
            let y = date.getFullYear();
            let m = date.getMonth() + 1;
            m = m < 10 ? "0" + m : m;
            let d = date.getDate();
            d = d < 10 ? "0" + d : d;
            let h = date.getHours();
            h = h < 10 ? "0" + h : h;
            let minute = date.getMinutes();
            let second = date.getSeconds();
            minute = minute < 10 ? "0" + minute : minute;
            second = second < 10 ? "0" + second : second;
            // console.log( y + '-' + m + '-' + d + ' ' + '　' + h + ':' + minute + ':' + second)
            let dates = m + "-" + d + " " + h + ":" + minute + ":" + second;
            return dates;
          }
          for (let i = 0; i < str.data.data.length; i++) {
            str.data.data[i].create_time = timestampToTime(
              str.data.data[i].create_time
            );
          }
          this.daty = str.data.data;
        } else {
          Toast({
            message: this.$t(str.data.msg),
            duration: 2000,
          });
        }
      });
    },
  },
  destroyed() {},
  mounted() {
    this.getdata();
  },
};
</script>
<style type="text/css" lang="less" scoped="scoped">
.no-data {
  font-size: 0.14rem;
  text-align: center;
  margin-top: .3rem;
}
input::-webkit-input-placeholder {
  color: #ccc;
  font-size: 0.14rem;
}
.dabg {
  background: #fff;
}
.gebgh {
  height: 0.44rem;
  //background: linear-gradient(233deg, #f36218, #f59934);
  background: linear-gradient(90deg, #4b9dd6, #2f38d5);
}
.jilkm {
  padding-top: 0.15rem;
  border-bottom: 0.05rem solid #f5f5f5;
  .namkl {
    width: 3.5rem;
    margin: 0 auto;
    margin-bottom: 0.15rem;
    display: flex;
    justify-content: space-between;
    .nlt {
      h6 {
        color: #333333;
        font-size: 0.15rem;
        font-weight: 500;
        span {
          color: #ea3544;
          font-size: 0.14rem;
          margin-left: 0.04rem;
        }
      }
      p {
        color: #333;
        font-size: 0.12rem;
        margin-top: 0.05rem;
        span {
          width: 0.15rem;
          height: 0.15rem;
          background: #3b4fde;
          border-radius: 0.02rem;
          text-align: center;
          line-height: 0.15rem;
          color: #fff;
          font-size: 0.1rem;
          display: inline-block;
          &.sh {
            background: #aa3bde;
          }
          &.bj {
            background: #ea6248;
          }
        }
        a {
          display: inline-block;
          height: 0.15rem;
          line-height: 0.15rem;
          padding: 0 0.04rem;
          background: rgba(59, 79, 222, 0.1);
          border-radius: 0.02rem;
          color: #3b4fde;
          font-size: 0.12rem;
          vertical-align: middle;
          &.sh {
            color: #aa3bde;
            background: rgba(170, 59, 222, 0.1);
          }
          &.bj {
            color: #ea6248;
            background: rgba(234, 98, 72, 0.1);
          }
        }
      }
    }
    .rlt {
      font-size: 0.14rem;
      color: #666666;
      margin-top: 0.1rem;
      span {
        color: #d73d3d;
        margin-left: 0.03rem;
      }
    }
  }
  .gnj {
    height: 0.01rem;
    width: 100%;
    background: #e0e0e0;
  }
  .plkm {
    width: 3.5rem;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    padding-bottom: 0.15rem;
    p {
      width: 47%;
      display: flex;
      justify-content: space-between;
      margin-top: 0.15rem;
      span {
        color: #666;
        font-size: 0.12rem;
      }
      a {
        color: #d73d3d;
        font-size: 0.12rem;
      }
    }
  }
}
</style>
