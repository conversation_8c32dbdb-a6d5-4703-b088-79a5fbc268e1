<template>
	<div class="page ">
		<top-back :title="$t('保密措施')"></top-back>

		<div class="list">
			<div class="item">
			保密措施
		<br/><br/>	金和順投資有限公司暨子公司客戶資料保密措施
		<br/><br/>	金和順投資有限公司（下稱本公司）暨後列子公司，為善盡對您的個人資料保密職責，特依據金融控股公司法、金融控股公司子公司間共同行銷管理辦法及主管機關相關法令規範，訂定本保密措施，以維護您的隱私權。
		<br/><br/>	一、客戶資料蒐集方式：
		<br/><br/><br/><br/>	本公司及子公司擁有您的個人資料，係因您已為各子公司之既有客戶，或您參與各子公司行銷活動時所提供之資料，或從其他合法且公開管道而取得之資料。
		<br/><br/>	二、客戶資料儲存及保管方式：
		<br/><br/>	您的個人資料會被嚴密保存於本公司或子公司或我們所委託具有高度安全性、穩定性之資訊保管公司的資料儲存系統中；要取得您的任何資料，都必須在我們訂定之資料授權管理辦法下，始可進行資料之取得與使用。
		<br/><br/>	三、資料安全及保護方式：
		<br/><br/>	為確保您的個人資料不會被他人直接或間接取得，我們採用嚴格的內部IP控管模式，本公司與各子公司間採用區域虛擬網路系統，以分層管理您的個人資料，另一方面，我們亦採用先進之防火牆系統防止未經授權者入侵。
		<br/><br/>	四、資料分類：
		<br/><br/>	您的個人資料分類為基本資料、往來交易資料及其他相關資料：
		<br/><br/>	（一）基本資料：包括姓名、出生年月日、身分證統一編號、電話、電子郵件及地址等資料。
		<br/><br/>	（二）往來交易資料及其他相關資料：
		<br/><br/>	1.帳務資料：包括帳戶號碼或類似功能號碼、信用卡帳號、存款帳號、交易帳戶號碼、存借款及其他往來交易資料及財務情況等資料。
		<br/><br/>	2.信用資料：包括退票紀錄、註銷紀錄、拒絕往來紀錄及業務經營狀況等資料。
		<br/><br/>	3.投資資料：包括投資或出售投資之標的、金額及時間等資料。
		<br/><br/>	4.保險資料：包括投保保險種類、年期、保額及繳費方式等相關資料。
		<br/><br/>	五、客戶資料利用目的：
		<br/><br/>	為了讓您得到更完整多元及專業的金融服務，我們將積極開發新種金融商品及服務，整合運用您的個人資料進行行銷，以滿足您所有的金融需求。
		<br/><br/>	六、客戶資料利用範圍及揭露對象：
		<br/><br/>	本公司之子公司為客戶資料之處理、儲存、整合或交互運用資料進行行銷時，將在法令許可範圍內，依循金融控股公司法、個人資料保護法、金融控股公司子公司間共同行銷管理辦法及主管機關相關法令規範，揭露您的個人資料予本公司之其他子公司。收受並運用資料之其他子公司，不得再向其他第三人揭露該等資料。本公司之子公司亦可能在法令及主管機關相關規定許可範圍內，揭露您的個人資料予財團法人金融聯合徵信中心、票據交換所、聯合信用卡處理中心、金融資訊服務中心、財團法人中小企業信用保證基金或其他法令許可之第三人。如有必要，本公司之子公司亦可能將您的個人資料揭露予訂有契約之第三人為特定目的之利用。除前述情形或經您的書面同意外，我們不會再向任何第三人揭露您的個人資料。
		<br/><br/>	七、客戶資料變更修改方式：
		<br/><br/>	為保持您個人資料的完整性及正確性，如果您的個人資料有變更，您可透過書面、電話、傳真或網路及其他我們同意的管道方式申請變更您的個人資料。
		<br/><br/>	八、客戶選擇資料停止使用方式：
		<br/><br/>	如果您不願再讓我們於辦理行銷時揭露、轉介或交互運用您的個人資料，您可直接通知往來之子公司，我們將於接獲通知並確認您的身分後立即受理，並於系統及作業合理期間內停止使用您的個人資料。為確保客戶隱私，我們將隨時因應社會環境及法令的變遷，修正本保密措施，並將透過網站公告及主管機關指定之方式揭露或公告予客戶週知。
		<br/><br/>	九、本公司交互運用客戶資料之子公司名稱：
		<br/><br/>	金和順企業有限公司（統一編號：24525272）
		<br/><br/>	金和順股份有限公司（統一編號：16408525）

			</div>
		</div>

	</div>
</template>

<script>
	export default {
		name: "aboutUs",
		props: {},
		data() {
			return {
				info: "",
			};
		},
		components: {},
		methods: {
			getInfo() {
				this.$server
					.post("/common/wenben", {
						name: "关于我们",
						type: "twd"
					})
					.then((res) => {
						this.info = res.data.content;
					});
			},
		},
		created() {
			this.getInfo();
		},
		computed: {},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.6rem 0.1rem 0;
		min-height: 100vh;
	}

	.list {
		.item {
			line-height: 0.2rem;
			margin-bottom: 0.1rem;
			color: #fff;
		}
	}
</style>