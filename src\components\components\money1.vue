<template>
  <div class="moneyBox">
    <div class="nums flex flex-b">
      <div class="item flex-column-item">
        <div style="height: .15rem;margin-bottom: .05rem;"></div>
        <div class="t2">持倉市值</div>
        <div class="t1">{{ show ? $formatMoney(szAssets) || 0 : "****" }}</div>
      </div>
      <div class="item flex-column-item">
        <img src="../../assets/zy.png" @click="show=!show" v-if="show" style="width:.15rem;height: .15rem;margin-bottom: .05rem;">
        <img src="../../assets/by.png" @click="show=!show" v-else style="width:.15rem;height: .15rem;margin-bottom: .05rem;">
        <div class="t2">可用資金</div>
        <div class="t1">{{ show ? $formatMoney(userInfo.twd) || 0 : "****" }}</div>
      </div>
      <div class="item flex-column-item">
        <div style="height: .15rem;margin-bottom: .05rem;"></div>
        <div class="t2">使用中資金</div>
        <div class="t1">{{ show ? $formatMoney(freezeAssets) || 0 : "****" }}</div>
      </div>
      <!-- <div class="item">
        <div class="t1">{{ $formatMoney(userInfo.dollar) }}</div>
        <div class="t2">{{ $t("new").a36 }} (USD)</div>
      </div> -->
    </div>
  </div>
</template>

<script>
export default {
  name: "information",
  props: {},
  data() {
    return {
      currentRate: 10,
      rate: 20,
      show: false,
      loading: true,
      isLoading: false,
      userInfo: {},
      freezeAssets: 0,
      totalAssets: 0,
      totalProfit: 0,
      szAssets: 0,
    };
  },
  computed: {
    text() {
      return this.currentRate.toFixed(0) + '%';
    },
  },
  mounted() {
    this.getTotalProfit();
    this.getTotalAssets();
    this.initData()
  },
  methods: {
    initData() {
      this.$server.post('/trade/userstocklist', {
        type: "twd"
      }).then(async (res) => {
        if (res.status == 1) {
          let arr = res.data;

          let szArr = []; //列表市值
          let ykArr = []; //列表盈亏
          let arr1 = []; //認繳的资金  买入本金+盈利(持仓冻结)
          // let arr2 = [];

          arr.forEach((item) => {
            szArr.push(Number(item.market_value) + Number(item.yingkui));
            ykArr.push(Number(item.yingkui));
            arr1.push(
                Number(item.buy_price) * Number(item.stock_num) +
                Number(item.yingkui)
            );
            // arr2.push(Number(item.buy_price) * Number(item.stock_num));

            item.yingkuiBi = ((item.yingkui / item.market_value) * 100).toFixed(
                2
            );
          });

          this.szAssets = szArr.reduce((a, b) => a + b, 0);
        }
      });
    },
    //盈利資金
    async getTotalProfit() {
      const res = await this.$server.post("/trade/userstocklist", {
        type: "twd",
      });
      this.isLoading = false;
      let fdyk = 0;
      let arr = [];
      if (res.status == 1) {
        res.data.forEach((item) => {
          arr.push(Number(item.yingkui));
        });

        fdyk = arr.reduce((a, b) => a + b, 0);
      }
      this.totalProfit = fdyk;
    },
    // 获取总资产
    async getTotalAssets() {
      const res = await this.$server.post("/user/getUserinfo", {
        type: "twd"
      });
      if (res.status == 1) {
        this.userInfo = res.data;
      }
      let abalance = Number(res.data.twd || 0); //用户可用余额
      // 获取跟单盈亏
      const res1 = await this.$server.post("/trade/userproductlist", {
        type: "twd",
      });
      let followingFreeze = 0; //量化跟单的认缴冻结
      if (res1.status == 1 && res1.data && Array.isArray(res1.data)) {
        let arr = [];
        res1.data.forEach((item) => {
          if (item.status == 0) {
            arr.push(Number(item.money)); //跟单冻结的资金
          }
        });
        let total = arr.reduce((a, b) => a + b, 0);
        followingFreeze = total;
      }

      // 申購列表的投資
      const res2 = await this.$server.post("/trade/usernewstocklist", {
        type: "twd",
      });
      let subscriptionProfit = 0; //新股申请的认缴冻结
      if (res2.status == 1 && res2.data && Array.isArray(res2.data)) {
        let arr = [];
        let arr1 = [];
        res2.data.forEach((item) => {
          if (item.status == 1) {
            arr.push(Number(item.rjmoney == "-" ? 0 : item.rjmoney)); //認繳的资金
          }
          if (item.status == 0) {
            arr1.push(Number(item.rjmoney == "-" ? 0 : item.rjmoney)); //计算申购认缴等于0的
          }
        });
        let total = arr.reduce((a, b) => a + b, 0);
        let total1 = arr1.reduce((a, b) => a + b, 0);

        subscriptionProfit = total + total1;
      }

      // 競拍列表的投資 ??? 還沒有對接口
      // const res3 = await this.$server.post("/trade/newstocklists", {type: "twd"});
      // let subscriptionProfit1 = 0; //新股申请的认缴冻结
      // if (res3.status == 1 && res3.data && Array.isArray(res3.data)) {
      //   let arr = [];
      //   let arr1 = [];
      //   res3.data.forEach((item) => {
      //     if (item.status == 1) {
      //       arr.push(Number(item.rjmoney == "-" ? 0 : item.rjmoney)); //認繳的资金
      //     }
      //     if (item.status == 0) {
      //       arr1.push(Number(item.rjmoney == "-" ? 0 : item.rjmoney)); //计算申购认缴等于0的
      //     }
      //   });
      //   let total = arr.reduce((a, b) => a + b, 0);
      //   let total1 = arr1.reduce((a, b) => a + b, 0);

      //   subscriptionProfit1 = total + total1;
      // }

      // 持仓中的申请冻结
      const res4 = await this.$server.post("/trade/userstocklist", {
        type: "twd",
      });
      let positionFreeze = 0;
      delete res4.data.ccsz;
      delete res4.data.fdyk;
      let dataArr = Object.values(res4.data);
      if (dataArr.length) {
        let arr = [];
        dataArr.forEach((item) => {
          if (item.status == 0) {
            // arr.push(Number(item.credit)); //認繳的资金
            arr.push(
                Number(item.buy_price) * Number(item.stock_num) + item.yingkui
            ); //認繳的资金  买入本金+盈利
          }
        });

        let total = arr.reduce((a, b) => a + b, 0);
        positionFreeze = total;
      }

      // 大宗交易申请冻结
      const res5 = await this.$server.post("/trade/ustockslist", {
        type: "twd",
      });
      let bigDealFreeze = 0;
      if (res5.status == 1 && res5.data && Array.isArray(res5.data)) {
        let arr = [];
        res5.data.forEach((item) => {
          if (item.status == 0) {
            arr.push(Number(item.credit)); //認繳的资金
          }
        });
        let total = arr.reduce((a, b) => a + b, 0);
        bigDealFreeze = total;
      }

      // 冻结资产
      this.freezeAssets =
          subscriptionProfit + followingFreeze + bigDealFreeze + positionFreeze;
      //  +subscriptionProfit1;
      // 总资产
      this.totalAssets = abalance + this.freezeAssets;
    },
    goUrl(url) {
      if (url == "kefu") {
        this.getConfig();
      } else {
        this.$toPage(url);
      }
    },
    async getConfig() {
      const res = await this.$server.post("/common/config", {
        type: "twd"
      });
      let val = {};
      res.data.forEach((vo) => {
        val[vo.name] = vo.value;
      });
      this.$openUrl(val.kefu); //重新获取
    },
  },
};
</script>

<style scoped lang="less">
::v-deep .van-circle__text {
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  font-size: 0.14rem;
  color: #FFFFFF;
}

.moneyBox {
  margin: 0 0.12rem;
  background: #212121;
  border-radius: 0.06rem 0.06rem 0.06rem 0.06rem;

  .tops {
    padding: 0.1rem 0.2rem;
    border-bottom: 0.01rem solid #2D2D2D;

    .t {
      padding: 0.1rem 0;
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      font-size: 0.14rem;
      color: #FFFFFF;
    }

    .num {
      padding: 0.1rem 0;
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      font-size: 0.14rem;
      color: #FFFFFF;
    }
  }

  .nums {
    .item {
      padding: 0.15rem 0;
      width: 32%;
      background: #111111;
      border-radius: 0.02rem 0.02rem 0.02rem 0.02rem;

      &:last-child {
        border-right: none;
      }

      .t1 {
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        font-size: 0.14rem;
        color: #FFFFFF;
      }

      .t2 {
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 0.12rem;
        color: #FFFFFF;
        margin-bottom: 0.1rem;
      }
    }
  }
}
</style>
