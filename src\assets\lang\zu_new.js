module.exports = {
  // 通用组件
  common: {
    confirm: '<PERSON><PERSON><PERSON><PERSON>',
    cancel: '<PERSON><PERSON><PERSON>',
    submit: 'Thum<PERSON>',
    save: '<PERSON><PERSON><PERSON>',
    delete: 'Su<PERSON>',
    edit: '<PERSON><PERSON><PERSON>',
    back: '<PERSON><PERSON>',
    next: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    previous: 'Okwangaph<PERSON>bili',
    complete: '<PERSON><PERSON>',
    search: '<PERSON><PERSON>',
    loading: '<PERSON><PERSON><PERSON><PERSON>a...',
    noData: '<PERSON><PERSON><PERSON><PERSON>',
    success: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    failed: 'Kuhlulekile',
    processing: 'Kucutshungulwa',
    waiting: 'Kulindile',
    today: '<PERSON>uh<PERSON>',
    yesterday: 'I<PERSON><PERSON>',
    time: '<PERSON>ikhathi'
  },

  // 菜单导航
  menu: {
    home: 'I<PERSON>ya',
    market: 'Imakethe',
    trade: 'U<PERSON><PERSON><PERSON><PERSON>',
    favorite: '<PERSON><PERSON><PERSON>thi<PERSON>',
    mine: 'Ok<PERSON>'
  },

  // 交易页面
  trade: {
    title: 'Ukuhweba',
    profitFunds: 'Imali Yenzuzo',
    availableFunds: 'Imali Etholakalayo',
    positionValue: 'Inani Lesikhundl<PERSON>',
    totalProfitLoss: 'Inzu<PERSON>/<PERSON> Okuphelele',
    returnRate: '<PERSON><PERSON><PERSON>',
    totalShares: 'Izitoko Eziphelele',
    buyPrice: 'Intengo Yokuthenga',
    marketValue: 'Inani Lemakethe',
    cost: 'Izindleko',
    totalFees: 'Izimali Eziphelele',
    details: 'Imininingwane',
    holding: 'Ukubamba',
    pending: 'Kulindile',
    sell: 'Thengisa',
    closePosition: 'Vala Isikhundla'
  },

  // 持仓详情页面
  positionDetail: {
    title: 'Imininingwane Yesikhundla',
    profitLoss: 'Inzuzo Nokulahlekelwa',
    returnRate: 'Izinga Lokubuyela',
    totalShares: 'Izitoko Eziphelele',
    buyPrice: 'Intengo Yokuthenga',
    marketValue: 'Inani Lemakethe',
    cost: 'Izindleko',
    tradeType: 'Uhlobo Lokuhweba',
    buyLong: 'Thenga Isikhathi Eside',
    buyShort: 'Thenga Isikhathi Esifushane',
    orderNumber: 'Inombolo Ye-oda',
    buyShares: 'Thenga Izitoko',
    buyTime: 'Isikhathi Sokuthenga',
    sellTime: 'Isikhathi Sokuthengisa',
    sellPrice: 'Intengo Yokuthengisa',
    type: 'Uhlobo',
    marketPrice: 'Intengo Yemakethe',
    pairTrade: 'Ukuhweba Kwamapheya',
    buyFee: 'Imali Yokuthenga',
    sellFee: 'Imali Yokuthengisa'
  },

  // 个人中心
  mine: {
    totalAssets: 'Impahla Ephelele',
    availableFunds: 'Imali Etholakalayo',
    profitableFunds: 'Imali Enenzuzo',
    freezeFunds: 'Imali Eqandisiwe',
    recharge: 'Shaja',
    withdraw: 'Khipha',
    bankCard: 'Ikhadi Lebhange',
    realNameAuth: 'Ukuqinisekisa Igama Langempela',
    fundRecords: 'Amarekhodi Emali',
    privacyPolicy: 'Inqubomgomo Yobumfihlo',
    changeLoginPassword: 'Shintsha Iphasiwedi Yokungena',
    changeFundPassword: 'Shintsha Iphasiwedi Yemali',
    languageSwitch: 'Ukushintsha Ulimi',
    service: 'Isevisi',
    logout: 'Phuma'
  },

  // 存股借券
  stockLending: {
    title: 'Ukuboleka Izitoko',
    lendingList: 'Uhlu Lokuboleka',
    lendingPositions: 'Izikhundla Zokuboleka',
    placeOrder: 'Beka I-oda',
    referencePrice: 'Intengo Yokubhekisela',
    referenceRate: 'Izinga Lokubhekisela',
    lendingDays: 'Izinsuku Zokuboleka',
    noThreshold: 'Ayikho Imingcele',
    tenThousand: 'Izinkulungwane Eziyishumi',
    minLendingAmount: 'Imali Encane Yokuboleka',
    requiredShares: 'Izitifiketi Ezidingekayo',
    closedType: 'Uhlobo Oluvaliwe',
    type: 'Uhlobo',
    ended: 'Kuphele',
    lending: 'Kuyabolekwa',
    lendingIncome: 'Inzuzo Yokuboleka',
    lendingRate: 'Izinga Lenzalo Lokuboleka',
    lendingShares: 'Izitifiketi Zokuboleka',
    lendingMarketValue: 'Inani Lemakethe Yokuboleka',
    lendingTime: 'Isikhathi Sokuboleka',
    lend: 'Boleka',
    buyShares: 'Thenga Izitifiketi',
    enterBuyShares: 'Faka Inombolo Yezitifiketi Ozozithengayo'
  },

  // 新股申购
  newStock: {
    title: 'Ukubhalisa Izitoko Ezintsha',
    pending: 'Kulindele Ukubhalisa',
    subscribing: 'Kuyabhalisa',
    ended: 'Ukubhalisa Kuphele',
    enterQuantity: 'Faka Inani Lokubhalisa'
  },

  // 大宗交易
  blockTrade: {
    title: 'Ukuhweba Okukhulu',
    stockList: 'Uhlu Lwezitoko',
    purchaseRecords: 'Amarekhodi Okuthenga',
    enterBuyShares: 'Faka Inombolo Yezitifiketi Ozozithengayo'
  },

  // 下拉刷新
  refresh: {
    pullToRefresh: 'Donsa phansi ukuze uvuselele...',
    releaseToRefresh: 'Khulula ukuze uvuselele...',
    loading: 'Iyalayisha...'
  },

  // 服务端返回的常见消息（保持中文键名以便服务端消息翻译）
  '查询成功': 'Ukubuza kuphumelele',
  '参数错误': 'Iphutha leparameter',
  '操作成功': 'Ukusebenza kuphumelele',
  '操作失败': 'Ukusebenza kuhlulekile',
  '余额不足': 'Ibhalansi ayenele',
  '请输入购买张数': 'Faka inombolo yezitifiketi ozozithengayo'
}
