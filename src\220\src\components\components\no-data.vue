<template>
	<div class="animate__animated animate__fadeIn">
		<div class="cot">
			<div class="ic">
				<img src="../../assets/market/noData.png" />
			</div>
			<div class="t">{{ $t("new").t37 }}</div>
		</div>
		<!-- <van-empty  :description="$t('new').t37" /> -->
	</div>
</template>

<script>
	export default {
		name: "noData",
		props: {},
		data() {
			return {};
		},
		components: {},
		methods: {},
		created() {},
		computed: {},
	};
</script>

<style scoped lang="less">
	.cot {
		text-align: center;
		padding: 0.5rem 0;
		margin: 0 auto;

		.t {
			font-size: 0.12rem;
			margin-top: 0.1rem;
		}
		img{
			width:2.57rem;
			height: 1.89rem;
		}
	}
</style>