<template>
  <div class="page">
    <top-back :title="$t('语言设置')"></top-back>
    <div class="list">
      <div
        class="item flex flex-b"
        @click="change(item.key)"
        v-for="(item, i) in langList"
        :key="i"
      >
        <div class="lang-info">
          <span class="lang-name">{{ item.name }}</span>
          <span class="native-name">{{ item.nativeName }}</span>
        </div>
        <div
          class="icon xzl animate__animated animate__fadeIn"
          v-if="lang == item.key"
        ></div>
      </div>
    </div>
    <div class="tips">
      <p>{{ $t('语言设置提示') }}</p>
    </div>
    <loading ref="loading" />
  </div>
</template>

<script>
import { langList, getCurrentLang } from '../../assets/local'

export default {
  name: "ChangeLang",
  props: {},
  data() {
    return {
      lang: "",
      langList: langList,
    };
  },
  created() {
    this.lang = getCurrentLang();
  },
  methods: {
    change(type) {
      if (type === this.lang) return; // 如果选择的是当前语言，不做任何操作

      localStorage.setItem("language", type);
      this.$refs.loading.open(); //开启加载
      setTimeout(() => {
        this.$refs.loading.close();
        // 重新加载页面以应用新语言
        window.location.reload();
      }, 1500);
    },
  },
};
</script>

<style scoped lang="less">
.page {
  padding: 0.6rem 0rem 0.2rem;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.list {
  background-color: #fff;
  margin: 0.1rem;
  border-radius: 0.08rem;

  .item {
    padding: 0.2rem 0.15rem;
    font-size: 0.16rem;
    color: #0a0a0a;
    border-bottom: 0.01rem solid #e3e3e3;
    cursor: pointer;
    transition: background-color 0.2s;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background-color: #f8f8f8;
    }

    .lang-info {
      display: flex;
      flex-direction: column;

      .lang-name {
        font-size: 0.16rem;
        color: #0a0a0a;
        font-weight: 500;
        margin-bottom: 0.02rem;
      }

      .native-name {
        font-size: 0.14rem;
        color: #666;
      }
    }

    .icon {
      width: 0.2rem;
      height: 0.2rem;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
    }

    .xzl {
      background-image: url('../../assets/home/<USER>');
    }
  }
}

.tips {
  margin: 0.2rem 0.15rem;
  padding: 0.15rem;
  background-color: #fff3cd;
  border: 0.01rem solid #ffeaa7;
  border-radius: 0.08rem;

  p {
    font-size: 0.14rem;
    color: #856404;
    margin: 0;
    line-height: 1.4;
  }
}
</style>
