<template>
	<div class="searchPage">
		<div class="headf">
			<top :title="$t(text)"></top>
		</div>
		<div class="search">
			<div class="search-box">
				<!-- <img src="../../static/skin/market/search.png" /> -->
				<img src="../../assets/skin/home/<USER>" />
				<input @input="searchFn" type="text" v-model="keyword" :placeholder="$t('search').txt1" />
			</div>
		</div>
		
		<!-- 历史搜索 -->
		<div class="keyword-block" v-if="!!oldKeywordList.length">
			<div class="keyword-list-header">{{$t('search').txt2}}</div>
			<div class="keyword">
				<div v-for="(keyword,index) in oldKeywordList" @click="doSearch(keyword)" :key="index">{{keyword}}</div>
			</div>
		</div>
		<div class="nsearch-list" v-show="keyword" v-if="listData.length > 0">
			<div class="nsearch-list-item" v-for="(item, index) in listData" :key="index" @click="sharesDetails(item)" v-show="item.symbol">
				<div class="nsearch-item-title">
					{{item.local_name}} <span>{{item.symbol}}</span>
				</div>
				<div class="flex1 text-center" :class="{'red':Number(item.gain)>=0,'green':Number(item.gain)<0}">
					{{$formatMoney(item.price)||'-'}}円
				</div>
				<span class="flex1 text-right" :class="{'red':Number(item.gain)>=0,'green':Number(item.gain)<0}">
					{{$formatMoney(item.gainValue)||'-'}}円
				</span>
			</div>
		</div>
	</div>
</template>

<script>
	import Vue from "vue";
	import qs from "qs";
	import axios from "axios";
	import {
		Toast
	} from "vant";
	Vue.use(Toast);
	
	import top from "../bar/toper.vue";
	
	export default {
		name: "search",
		data() {
			return {
				text:this.$t('search').title,
				keyword: '',
				oldKeywordList: [],
				listData: [],
				
			};
		},
		components: {
			top,
		},
		destroyed() {},
		mounted() {
			let acc = window.localStorage.getItem('account')
			this.oldKeywordList = !!window.localStorage.getItem(acc + 'searchHistory') ? JSON.parse(window.localStorage.getItem(acc + 'searchHistory')) : [];
			if (this.keyword) {
				this.searchFn();
			}
		},
		methods: {
			doSearch(str) {
				this.keyword = str
				this.searchFn()
			},
			searchFn() {
				let that = this;
				this.$server.post('/trade/search', {
					content: this.keyword,
          type:'jpy'
				}).then(res => {
					if(res.data==''){
						return;
					}
					let rea = res.data
					if (rea.status === 1) {
						let list = rea.data
						console.log(list)
						let length = list.length
						let a
						that.listData = []
		
						if (length > 0) {
							let acc = window.localStorage.getItem('account')
							let searchHistory = !!window.localStorage.getItem(acc + 'searchHistory') ? JSON.parse(window.localStorage.getItem(acc + 'searchHistory')) : []
							if (searchHistory.length > 10) {
								searchHistory.shift()
							}
							if (!searchHistory.includes(this.keyword)) {
								searchHistory.push(this.keyword)
							}
							window.localStorage.setItem(acc + 'searchHistory', JSON.stringify(searchHistory))
							that.oldKeywordList = searchHistory
							for(var i in list){
								var row = list[i];
								if(row.symbol.indexOf('SZ')>-1){
									row.symbol = row.symbol.replaceAll('SZ','');
								}
							}
							that.listData = list;
						}else{
							that.listData = []
						}
					} else {
						that.listData = []
		
					}
				})
			},
			sharesDetails(obj) {
				var link = '/market/marketDetail?symbol='+obj.symbol.replace('TSE:','');
				this.clickNext(link)
			}
		},
		
	};
</script>

<style lang="less">
	.searchPage{
		padding-top:.47rem;
		min-height:100vh;
		background: #0F161C;
		.headf {
			//background: linear-gradient(233deg, #f36218, #f59934);
			background: #fff;
			position: fixed;z-index: 888;
			top: 0;
			left: 0;
			width: 100%;
			height:.47rem;
		}
		.search{
			position: relative;
			width:100%;
			overflow: hidden;
			padding-top:.2rem;
			
			.search-box{
				height: .4rem;
				background: #000000;
				border-radius: 0.1rem;
				margin: 0 .11rem;
				padding: 0 .13rem;
				display: flex;
				align-items: center;
				position: relative;z-index: 20;
				img{
					width: .16rem;
					height: .16rem;
					margin-right: .08rem;
				}
				input {
					width: 100%;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.14rem;
					color: #fff;
					background: transparent;
					border: 0;
					height: .37rem;
					line-height: .37rem;margin-left:.05rem;
				}
			}
		}
		
		.nsearch-list {
			margin-top: .15rem;
			.nsearch-list-item {
				display: flex;
				align-items: center;
				justify-content: space-between;
				border-bottom: .01rem solid rgba(255, 255, 255, 0.16);
				padding: 0.1rem 0.12rem;
				.nsearch-item-title{
					height: .65rem;
					width:40%;
					flex:none;
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.14rem;
					color: #FFFFFF;
					display: flex;
					flex-direction: column;
					justify-content: center;
					span{
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 0.13rem;
						color: #718A94;
						margin-top: .02rem;
					}
				}
				.nsearch-arrow{
					width: .07rem;
					height: .14rem;
				}
			}
		}
		.keyword-block {
			.keyword-list-header {
				font-size: .16rem;
				font-family: FZLanTingHeiT-R-GB;
				font-weight: 500;
				color: #fff;
				padding: 0 .14rem;
				height: .53rem;
				display: flex;
				align-items: center;
			}
			.keyword {
				display: flex;
				flex-flow: wrap;
				padding: 0 .09rem;
				div {
					margin: 0 .02rem .05rem .02rem;
					width: calc(100% / 4 - .05rem);
					height: .3rem;
					background: #424E4E;
					border-radius: 0.1rem;
					font-size: .14rem;
					font-family: PingFang SC;
					font-weight: 400;
					color: #fff;
					display: flex;
					align-items: center;
					justify-content: center;
				}
			}
		}
		
	}
</style>