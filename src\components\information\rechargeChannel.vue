<template>
	<div class="page ">
		<top-back title="儲值"></top-back>
		<div class="icon ddjl" @click="$toPage('/information/fundRecord')"></div>
		<div class="cot">
			<!-- <div class="money flex-column-item">
				<div class="t1">{{ $formatMoney(money) }}</div>
				<div class="t">付款金額</div>
			</div> -->
			<div class="nav-box flex flex-b" v-if="logList.length > 1">
				<div class="nav-item" v-for="(item, index) in logList" :key="index" :class="{ active: currmentIndex == index }" @click="changeNav(item, index)">
					通道{{ index + 1 }}
				</div>
			</div>
			 <!-- v-if="logList[currmentIndex]" -->
			<div class="list">
				<div class="item flex flex-b">
					<div class="flex">
						<div class="t">銀行戶名</div>
						<div class="t1">{{ logList[currmentIndex].name || "-" }}</div>
					</div>
					<div class="icon copy animate__animated animate__fadeIn" @click="copy(logList[currmentIndex].name)">
					</div>
				</div>
				<div class="item flex flex-b">
					<div class="flex">
						<div class="t">銀行帳號</div>
						<div class="t1">{{ logList[currmentIndex].bankcard || "-" }}</div>
					</div>
					<div class="icon copy animate__animated animate__fadeIn"
						@click="copy(logList[currmentIndex].bankcard)"></div>
				</div>
				<div class="item flex flex-b">
					<div class="flex">
						<div class="t">銀行名稱</div>
						<div class="t1">{{ logList[currmentIndex].bankname || "-" }}</div>
					</div>
					<div class="icon copy animate__animated animate__fadeIn"
						@click="copy(logList[currmentIndex].bankname)"></div>
				</div>
				<div class="item flex flex-b">
					<div class="flex">
						<div class="t">分行名稱</div>
						<div class="t1">{{ logList[currmentIndex].bankperson || "-" }}</div>
					</div>
					<div class="icon copy animate__animated animate__fadeIn"
						@click="copy(logList[currmentIndex].bankperson)"></div>
				</div>
				<div class="item flex flex-b">
					<div class="flex">
						<div class="t">分行代碼</div>
						<div class="t1">{{ logList[currmentIndex].bankcode || "-" }}</div>
					</div>
					<div class="icon copy animate__animated animate__fadeIn"
					@click="copy(logList[currmentIndex].bankcode)"></div>
				</div>
				<!-- 上传凭证 -->
				<div class="upload animate__animated animate__fadeIn">
					<div class="up-box flex flex-c">
						<div v-if="!showFrontcard">
							<div class="t">+</div>
							<div class="t1">請上傳憑證</div>
						</div>
						<img v-if="showFrontcard" :src="showFrontcard" />
						<input class="inp" accept="image/*" type="file" @change="uploadFile($event)" />
					</div>
				</div>
				<div class="defbtn animate__animated animate__fadeIn" @click="chongzhi">
					確認
				</div>
			</div>
		</div>
		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "rechargeChannel",
		props: {},
		data() {
			return {
				show: false,
				money: "",
				type: 0,
				logList: [
					{
					  name: "name",
					  bankcard: "bankcard",
					  bankname: "bankname",
					},
					{
					  name: "name",
					  bankcard: "bankcard",
					  bankname: "bankname",
					},
				],
				currmentIndex: 0,
				showFrontcard: "",
				form: {
					frontcard: "",
				},
				userInfo: {},
			};
		},
		components: {},
		created() {
			this.money = this.$route.query.money;
			// this.type = this.$route.query.type;
			this.initData();
			this.getUserInfo();
		},
		computed: {},
		methods: {
			initData() {
				this.$server
					.post("/common/recharge_channel", {
						type: "zar"
					})
					.then((res) => {
						let arr = [];
						for (let key in res.data) {
							let obj = res.data[key];

							if (obj.name) {
								if (obj.name.indexOf(":") > -1) {
									let arr = obj.name.split(":");
									obj.title = arr[0];
									obj.name = arr[1];
								}
								arr.push(obj);
							}
						}
						// this.logList = arr;
						// this.logList = this.logList.filter((item, index) => index < 4);
					});
			},
			getUserInfo() {
				this.$server.post("/user/getUserinfo", {
					type: "zar"
				}).then((res) => {
					if (res.status == 1) {
						this.userInfo = res.data;
					}
				});
			},
			changeNav(item, index) {
				this.currmentIndex = index;
			},
			chongzhi() {
				if (!this.showFrontcard) {
					this.$toast("請上傳憑證");
					return;
				}

				let per = {
					money: this.money,
					type: "zar",
					rjpz: this.form.frontcard,
				};

				this.$refs.loading.open(); //开启加载

				this.$server.post("/user/chongzhi", per).then((res) => {
					this.$refs.loading.close();
					if (res.status == 1) {
						this.$toast(this.$t(res.msg));
						this.showFrontcard = "";
					}
				});
			},
			uploadFile(e) {
				var file = e.target.files[0];
				var that = this;
				var formdata = new FormData();
				formdata.append("card", file);
				this.$refs.loading.open(); //开启加载

				this.$server
					.post("/common/upload1", formdata)
					.then((res) => {
						this.$refs.loading.close();
						if (res.status == 1) {
							this.$toast("上傳成功");
							// 正面
							this.showFrontcard = this.$server.url.imgUrl + res.data; //显示用
							this.form.frontcard = res.data; //提交用
						}
					})
					.catch((data) => {});
			},
			copy(text) {
				let textarea = document.createElement("textarea");
				textarea.value = text;
				textarea.readOnly = "readOnly";
				document.body.appendChild(textarea);
				textarea.select(); // 选中文本内容
				textarea.setSelectionRange(0, text.length); // 设置选定区的开始和结束点
				this.$toast(this.$t("複製成功"));
				var result = document.execCommand("copy"); //将当前选中区复制到剪贴板
				textarea.remove();
			},
		},
	};
</script>

<style scoped lang="less">
	.inp {
		position: absolute;
		width: 100%;
		height: 100%;
		left: 0;
		top: 0;
		opacity: 0;
	}

	.page {
		padding: 0.5rem 0 0;
		min-height: 100vh;
	}
	.ddjl{
		position: fixed;
		z-index: 999;
		right: 0.12rem;
		top: 0.12rem;
	}

	.cot {
		margin: 0.12rem;
		.money {
			padding: 0.2rem;
			width: 100%;
			height: 1.01rem;
			background: url('../../assets/v5/moneyBg.png') no-repeat center/100%;
			.t {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.14rem;
				color: #3C2500;

				.icon {
					margin-left: 0.05rem;
				}
			}

			.t1 {
				margin-top: 0.1rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 0.28rem;
				color: #3C2500;
			}
		}
		.nav-box {
			height: 0.4rem;
			background: #FFFFFF;
			border-radius: 0.04rem 0.04rem 0.04rem 0.04rem;
			padding: 0.02rem;

			.nav-item {
				flex: 1;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 0.14rem;
				color: #6F6F6F;
				line-height: 0.36rem;
				text-align: center;
				position: relative;

				&.active {
					height: 0.36rem;
					background: linear-gradient(90deg, #1B167A 0%, #4383C7 100%);
					border-radius: 0.04rem 0.04rem 0.04rem 0.04rem;
					color: #fff;
					position: relative;
					// &::after{
					// 	position: absolute;
					// 	content: '';
					// 	bottom: 0;
					// 	left: 50%;
					// 	transform: translateX(-50%);
					// 	width: 50%;
					// 	height: 0.02rem;
					// 	background-color: #E5C79F;
					// }
				}
			}
		}

		.list {
			background: #FFFFFF;
			border-radius: 0.08rem 0.08rem 0.08rem 0.08rem;
			padding: 0.12rem;
			margin: 0.1rem 0;
			.item {
				padding: 0.16rem 0;
				border-bottom: 0.01rem solid #F3F3F3;
				.t {
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.14rem;
					color: #D2D2D2;
				}
				.t1 {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.14rem;
					color: #000;
					margin-left: 0.1rem;
				}
			}
		}

		.upload {
			padding: 0.2rem 0 0;

			.up-box {
				width: 1.2rem;
				height: 1.2rem;
				position: relative;
				margin: 0 auto;
				border-radius: 0.04rem;
				border: 0.01rem solid #3A4052;
				text-align: center;

				.t {
					font-weight: bold;
					font-size: 0.2rem;
					color: #AEAEAE;
				}

				.t1 {
					font-weight: 500;
					font-size: 0.12rem;
					color: #AEAEAE;
				}

				img {
					width: 1rem;
					height: 1rem;
				}
			}
		}
	}
</style>