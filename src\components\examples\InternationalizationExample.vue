<template>
  <div class="i18n-example">
    <top-back :title="$t('语言设置')"></top-back>
    
    <div class="content">
      <!-- 当前语言显示 -->
      <div class="section">
        <h3>{{ $t('当前语言') }}</h3>
        <p>{{ getCurrentLanguageInfo() }}</p>
      </div>

      <!-- 前端文字翻译示例 -->
      <div class="section">
        <h3>{{ $t('前端文字翻译示例') }}</h3>
        <div class="example-item">
          <span class="label">{{ $t('登入成功') }}:</span>
          <span class="value">{{ $t('登入成功') }}</span>
        </div>
        <div class="example-item">
          <span class="label">{{ $t('操作成功') }}:</span>
          <span class="value">{{ $t('操作成功') }}</span>
        </div>
        <div class="example-item">
          <span class="label">{{ $t('余额不足') }}:</span>
          <span class="value">{{ $t('余额不足') }}</span>
        </div>
      </div>

      <!-- 服务端返回文字翻译示例 -->
      <div class="section">
        <h3>{{ $t('服务端文字翻译示例') }}</h3>
        <div class="example-item">
          <span class="label">{{ $t('原文') }}:</span>
          <span class="value">查询成功</span>
        </div>
        <div class="example-item">
          <span class="label">{{ $t('翻译后') }}:</span>
          <span class="value">{{ $translateServerText('查询成功') }}</span>
        </div>
        <div class="example-item">
          <span class="label">{{ $t('原文') }}:</span>
          <span class="value">余额不足</span>
        </div>
        <div class="example-item">
          <span class="label">{{ $t('翻译后') }}:</span>
          <span class="value">{{ $translateServerText('余额不足') }}</span>
        </div>
      </div>

      <!-- 模拟API响应处理 -->
      <div class="section">
        <h3>{{ $t('模拟API响应') }}</h3>
        <button @click="simulateApiResponse" class="api-button">
          {{ $t('模拟API调用') }}
        </button>
        <div v-if="apiResponse" class="api-response">
          <p><strong>{{ $t('原始响应') }}:</strong> {{ apiResponse.original }}</p>
          <p><strong>{{ $t('翻译后响应') }}:</strong> {{ apiResponse.translated }}</p>
        </div>
      </div>

      <!-- 语言切换 -->
      <div class="section">
        <h3>{{ $t('语言切换') }}</h3>
        <div class="lang-buttons">
          <button 
            v-for="lang in $getLangList()" 
            :key="lang.key"
            @click="switchLanguage(lang.key)"
            :class="{ active: $getCurrentLang() === lang.key }"
            class="lang-button"
          >
            {{ lang.nativeName }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'InternationalizationExample',
  data() {
    return {
      apiResponse: null
    }
  },
  methods: {
    getCurrentLanguageInfo() {
      const currentLang = this.$getCurrentLang();
      const langList = this.$getLangList();
      const langInfo = langList.find(lang => lang.key === currentLang);
      return langInfo ? `${langInfo.name} (${langInfo.nativeName})` : currentLang;
    },
    
    simulateApiResponse() {
      // 模拟不同的服务端响应
      const serverMessages = [
        '查询成功',
        '操作失败',
        '余额不足',
        '划转账户成功',
        '币币交易',
        '申请成功'
      ];
      
      const randomMessage = serverMessages[Math.floor(Math.random() * serverMessages.length)];
      
      this.apiResponse = {
        original: randomMessage,
        translated: this.$translateServerText(randomMessage)
      };
    },
    
    switchLanguage(langKey) {
      if (langKey !== this.$getCurrentLang()) {
        this.$setLang(langKey);
      }
    }
  }
}
</script>

<style scoped lang="less">
.i18n-example {
  padding: 0.6rem 0rem 0.2rem;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.content {
  padding: 0.15rem;
}

.section {
  background-color: #fff;
  margin-bottom: 0.15rem;
  padding: 0.15rem;
  border-radius: 0.08rem;
  
  h3 {
    margin: 0 0 0.1rem 0;
    font-size: 0.18rem;
    color: #333;
    font-weight: 600;
  }
}

.example-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.08rem 0;
  border-bottom: 0.01rem solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
  
  .label {
    font-size: 0.14rem;
    color: #666;
    flex: 1;
  }
  
  .value {
    font-size: 0.14rem;
    color: #333;
    font-weight: 500;
    flex: 1;
    text-align: right;
  }
}

.api-button {
  background-color: #1989fa;
  color: white;
  border: none;
  padding: 0.1rem 0.2rem;
  border-radius: 0.04rem;
  font-size: 0.14rem;
  cursor: pointer;
  
  &:hover {
    background-color: #1976d2;
  }
}

.api-response {
  margin-top: 0.1rem;
  padding: 0.1rem;
  background-color: #f8f9fa;
  border-radius: 0.04rem;
  
  p {
    margin: 0.05rem 0;
    font-size: 0.13rem;
    line-height: 1.4;
  }
}

.lang-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.08rem;
}

.lang-button {
  padding: 0.08rem 0.15rem;
  border: 0.01rem solid #ddd;
  background-color: #fff;
  border-radius: 0.04rem;
  font-size: 0.13rem;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background-color: #f0f0f0;
  }
  
  &.active {
    background-color: #1989fa;
    color: white;
    border-color: #1989fa;
  }
}
</style>
