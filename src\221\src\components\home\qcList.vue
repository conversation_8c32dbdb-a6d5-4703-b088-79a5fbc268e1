<template>
  <div class="page ">
    <!-- 抢筹 -->
    <top-back :title="$t('new').t10"></top-back>

    <van-pull-refresh
      v-model="isLoading"
      @refresh="onRefresh"
     :loosing-text="$t('new').t3"
      :loading-text="$t('new').t4"
      :pulling-text="$t('new').t5"
    >
      <div class="cot">
        <!-- <div class="nav-box flex flex-b">
          <div
            class="nav-item"
            v-for="(item, index) in navList"
            :key="index"
            :class="{ active: currmentIndex == item.type }"
            @click="changeNav(item.type)"
          >
            {{ item.name }}
          </div>
        </div> -->

        <template v-if="currmentIndex == 0">
          <div class="title flex flex-b" v-if="chooseList.length">
            <div class="flex-1">{{ $t("dividend").txt4 }}</div>
            <div class=" flex-1 t-c">{{ $t("dividend").txt2 }}</div>
            <div class="flex-1 t-r"></div>
          </div>
          <div
            class="list-item flex flex-b"
            v-for="(item, index) in chooseList"
            :key="index"
            @click="stockDetails(item)"
          >
            <div class="flex-1">
              <div class="name">{{ item.name }}</div>
              <div class="code">{{ item.symbol }}</div>
            </div>
            <div class="flex-1 price t-c">{{ item.price }}</div>
            <div class="flex-1 flex flex-e">
              <div class="st">{{ $t("new").t2 }}</div>
            </div>
          </div>
          <no-data v-if="!chooseList.length"></no-data>
        </template>

        <!-- 不显示 -->
        <template v-else>
          <div class="title flex flex-b">
            <div class="flex-1 ">{{ $t("dividend").txt4 }}</div>
            <div class="flex-1 t-r">{{ $t("dividend").txt5 }}</div>
            <div class="flex-1 t-r">{{ $t("dividend").txt6 }}</div>
            <div class="flex-1 t-r">{{ $t("dividend").txt7 }}</div>
            <div class="flex-1 t-r">{{ $t("dividend").txt8 }}</div>
          </div>
          <div
            class="list-item flex flex-b"
            v-for="(item, index) in myList"
            :key="index"
          >
            <div class="flex-1">
              <div class="name">{{ item.stock_name }}</div>
              <div class="code">{{ item.stock_code }}</div>
            </div>
            <div class="flex-1 price t-r">{{ item.buy_price }}</div>
            <div class="flex-1 t-r t">{{ item.zhang }}</div>
            <div class="flex-1 t-r t">{{ item.cj_num }}</div>
            <div class="flex-1 t-r">
              <span class="shares-name" v-if="item.status == 0">{{
                $t("dividend").txt14
              }}</span>
              <span class="shares-name" v-if="item.status == 1">{{
                $t("dividend").txt15
              }}</span>
              <span class="shares-name" v-if="item.status == 2">{{
                $t("dividend").txt16
              }}</span>
            </div>
          </div>
          <!-- <no-data v-if="!myList.length"></no-data> -->
        </template>
      </div>
    </van-pull-refresh>

    <van-popup
      v-model="show"
      position="bottom"
      :round="true"
      closeable
      :style="{ height: '50%' }"
    >
      <div class="pop">
        <div class="pop-title">{{ stockObj.name + "/" + stockObj.code }}</div>
        <div class="pop-price">
          <div class="t1">{{ $t("dividend").txt9 }}</div>
          <div class="t">{{ stockObj.price }}</div>
        </div>

        <div class="ipt">
          <div class="">
            <input
              v-model="buyObj.handle"
              type="number"
              :placeholder="$t('dividend').txt12"
              @input="TypeInput($event)"
            />
          </div>

          <div class="txt">
            {{ $t("dividend").txt10 }}
            <span>{{ $formatMoney(countMoney) }}</span>
          </div>
        </div>

<!--        <div class="pop-num">-->
<!--          <input-->
<!--            :placeholder="$t('new').t1"-->
<!--            type="password"-->
<!--            v-model="password"-->
<!--          />-->
<!--        </div>-->
        <div class="flex btips">
          <div class="flex">
            <div class="t1">{{ $t("mine").txt2 }}</div>
            <div class="t2">
              {{ $formatMoney(userInfo.baht) }}
            </div>
          </div>

          <div class="flex">
            <div class="t1">{{ $t("new").t }}</div>
            <div class="t2">{{ stockObj.price }}</div>
          </div>
        </div>

        <div @click="buyFn" class="b-btn">{{ $t("dividend").txt13 }}</div>
      </div>
    </van-popup>
    <loading ref="loading" />
  </div>
</template>

<script>
export default {
  name: "blockTrade",
  data() {
    return {
      isLoading: false,
      navList: [
        { name: this.$t("dividend").menu1, type: 0 },
        { name: this.$t("dividend").menu2, type: 1 },
      ],
      currmentIndex: 0,
      chooseList: [
        // {
        //   name: "名称",
        //   code: "007",
        //   price: "1000",
        // },
      ],
      myList: [
        // {
        //   stock_name: "stock_name",
        //   stock_code: "stock_code",
        //   buy_price: "1000",
        //   zhang: "1000",
        //   cj_num: "1000",
        //   status: 1,
        // },
      ],
      show: false,
      stockObj: {},
      buyObj: {
        handle: null,
      },
      password: "",
      userInfo: {},
    };
  },
  computed: {
    countMoney() {
      return this.stockObj.price * this.buyObj.handle;
    },
  },
  created() {
    this.initData();
    this.getNew();
  },
  mounted() {
    this.$refs.loading.open(); //开启加载
  },
  methods: {
    // 下拉刷新
    onRefresh() {
      this.initData();
      this.getNew();
    },
    initData() {
      this.$server.post("/user/getUserinfo", {}).then((res) => {
        if (res.status == 1) {
          this.userInfo = res.data;
        }
      });
    },
    TypeInput(e) {
      // 只能输入数字的验证;
      const inputType = /[^\d]/g; //想限制什么类型在这里换换正则就可以了
      this.$nextTick(function() {
        this.buyObj.handle = e.target.value.replace(inputType, "");
      });
    },
    getNew() {
      this.$server.post("/transaction/qclist", { type: 0 }).then((res) => {
        this.isLoading = false;
        this.$refs.loading.close(); //关闭加载

        if (res.status == 1) {
          this.chooseList = res.data;
        }
      });
    },
    // 弃用
    getMine() {
      this.$server.post("/transaction/ustockslist", { type: 0 }).then((res) => {
        this.$refs.loading.close(); //关闭加载

        if (res.status == 1) {
          this.myList = res.data;
        }
      });
    },
    stockDetails(stock) {
      this.show = true;
      this.stockObj = stock;

      //   this.$server
      //     .post("/transaction/nbhldetails", { symbol: stock.code })
      //     .then((res) => {
      //       this.stockObj = res.data;
      //     });
    },
    changeNav(index) {
      this.currmentIndex = index;
      this.$refs.loading.open(); //开启加载

      if (index) this.getMine();
      else this.getNew();
    },
    buyFn() {
      if (!this.buyObj.handle) {
        this.$toast(this.$t("dividend").txt12);
        return;
      }
      // if (!this.password) {
      //   this.$toast(this.$t("new").t);
      //   return;
      // }
      this.$refs.loading.open(); //开启加载

      this.$server
        .post("/transaction/buy_stock", {
          symbol: this.stockObj.symbol,
          zhang: this.buyObj.handle,
          //password: this.password,
          buyzd: 1,
          ganggan: 1,
          type: 1,
          is_qc: 1,
        })
        .then((res) => {
          this.$refs.loading.close();

          this.show = false;
          if (res.msg) {
            this.$toast(this.$t(res.msg));
          }
        });
    },
  },
};
</script>

<style scoped lang="less">
.page {
  padding: 0.6rem 0.1rem 0.1rem;
  background: #f8f8f8;
  min-height: 100vh;
}
.pop {
  background-color: #fff;
  padding: 0 0.15rem 0.15rem;
  position: relative;
  .btips {
    padding: 0.1rem 0;
    .t1 {
      font-weight: 500;
      font-size: 0.12rem;
      color: #999999;
    }
    .t2 {
      font-weight: 500;
      font-size: 0.12rem;
      color: #f33030;
      margin-left: 0.05rem;
    }
  }
  .pop-title {
    font-weight: 500;
    font-size: 0.14rem;
    color: #000000;
    text-align: center;
    padding: 0.15rem 0;
    border-bottom: 0.01rem solid #efefef;
  }
  .pop-price {
    padding: 0.15rem 0 0;
    .t {
      font-weight: bold;
      font-size: 0.14rem;
      color: #ea3544;
      margin-top: 0.05rem;
    }
    .t1 {
      font-weight: 500;
      font-size: 0.14rem;
      color: #000000;
    }
  }

  .ipt {
    padding: 0.1rem 0;
    .t1 {
      font-size: 0.12rem;
      color: #9a9fa5;
    }
    .t2 {
      font-size: 0.12rem;
      font-weight: 600;
      color: #3365d6;
      margin-left: 0.05rem;
    }
    .t3 {
      font-size: 0.12rem;
      font-weight: 600;
      color: #50a457;
      margin-left: 0.05rem;
    }
    input {
      width: 100%;
      background: transparent;
      border-radius: 0.05rem;
      border: 0.01rem solid #cdcdcd;
      height: 0.4rem;
      line-height: 0.4rem;
      padding: 0 0.1rem;
      &::placeholder {
        font-weight: 500;
        font-size: 0.14rem;
        color: #999999;
      }
    }
  }

  .pop-num {
    input {
      width: 100%;
      background: transparent;
      border-radius: 0.05rem;
      border: 0.01rem solid #cdcdcd;
      height: 0.4rem;
      line-height: 0.4rem;
      padding: 0 0.1rem;
      &::placeholder {
        font-weight: 500;
        font-size: 0.14rem;
        color: #999999;
      }
    }
  }
  .txt {
    font-weight: 500;
    font-size: 0.12rem;
    color: #999999;
    padding: 0.1rem 0;
    span {
      font-weight: 500;
      font-size: 0.12rem;
      color: #f33030;
      margin-left: 0.05rem;
    }
  }
  .b-btn {
    margin: 0.2rem 0 0;
    border-radius: 0.05rem;
  }
}

.cot {
  background: #ffffff;
  box-shadow: 0rem 0rem 0.14rem 0rem #eeeeee;
  border-radius: 0.1rem;
  .title {
    padding: 0.1rem;
    div {
      font-weight: 500;
      font-size: 0.12rem;
      color: #666666;
    }
  }
  .list-item {
    padding: 0.1rem;
    border-bottom: 0.01rem solid #f8f8f8;
    &:last-child {
      border-bottom: 0;
    }
    .name {
      font-weight: 500;
      font-size: 0.14rem;
      color: #333333;
    }
    .code {
      font-weight: 500;
      font-size: 0.12rem;
      color: #999999;
      margin-top: 0.05rem;
    }
    .price {
      font-weight: 500;
      font-size: 0.14rem;
      color: #ff5b40;
    }
    .st {
      background: #07a66e;
      border-radius: 0.13rem;
      padding: 0.05rem 0.2rem;
      font-weight: 500;
      font-size: 0.12rem;
      color: #ffffff;
    }
    .t {
      font-weight: bold;
    }
    .shares-name {
      color: #07a66e;
    }
  }
}

.nav-box {
  border-bottom: 0.01rem solid #f8f8f8;
  padding: 0.15rem 0.1rem;
  .nav-item {
    width: calc(100% / 2);
    font-weight: 500;
    font-size: 0.14rem;
    color: #666666;
    text-align: center;
    position: relative;
  }
  .active {
    font-weight: bold;
    color: #07a66e;
  }
}
</style>
