<template>
	<!-- 市场更多 -->
	<div class="page ">
		<top-back :title="$t('熱門股票')"></top-back>
		<!-- 切換 -->
		<div class="change">
			<div class="flex flex-b">
				<div class="change-item" v-for="(item, index) in sort" :class="{ active: item.id === sortIndex }"
					:key="index" @click="changeSort(item.id)">
					{{ item.name }}
				</div>
			</div>
			<!-- <div class="titles flex flex-b">
				<div class="flex-2">名稱</div>
				<div class="flex-2 t-c">價格</div>
				<div class="flex-2 t-c flex flex-c">
					成交量
					<div class="icon" :class="show ? 'zq' : 'dq'" @click="changeList(0)"></div>
				</div>
				<div class="flex-1 t-r flex flex-e">
					漲跌
					<div class="icon" :class="show1 ? 'zq' : 'dq'" @click="changeList(1)"></div>
				</div>
			</div> -->
		</div>

		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<van-skeleton title :row="26" :loading="loading">
				<div class="rm">
					<!-- 列表 -->
					<div class="rm-list">
						<div class="titles flex flex-b">
							<div class="flex-2">{{ $t('名稱') }}</div>
							<div class="flex-1 t-c">{{ $t('價格') }}</div>
							<div class="flex-2 t-c flex flex-c">
								{{ $t('成交量') }}
								<div class="icon" :class="show ? 'zq' : 'dq'" @click="changeList(0)"></div>
							</div>
							<div class="flex-2 t-r flex flex-e">
								{{ $t('漲跌') }}
								<div class="icon" :class="show1 ? 'zq' : 'dq'" @click="changeList(1)"></div>
							</div>
						</div>
						<div class="rm-item flex flex-b" v-for="(item, i) in list" :key="i"
							@click=" $toDetail(`/market/stockDetail?symbol=${item.symbol}`, item)">
							<div class="flex-2">
								<div class="name">{{ item.local_name }}</div>
								<div class="code">{{ item.symbol }}</div>
							</div>
							<div class="flex-1 t-c price" :class="item.gain > 0 ? 'red-bg' : 'green-bg'">
								{{ $formatMoney(item.price) }}
							</div>
							<!-- <div class="flex-1 t-c price" :class="item.gain > 0 ? 'red' : 'green'">
								{{ $formatMoney(item.volume / 1000000) }} M
							</div> -->
							<div class="flex flex-c flex-2 per" :class="item.gain > 0 ? 'red' : 'green'">
								<!-- <div class="icon animate__animated animate__fadeIn" :class="item.gain > 0 ? 'up' : 'down'"></div> -->
								{{ $formatMoney(item.gainValue) }}
							</div>
							<div class="per flex flex-e flex-2" :class="item.gain > 0 ? 'red' : 'green'">
								{{ item.gain }}%
							</div>
						</div>
					</div>
				</div>
			</van-skeleton>
		</van-pull-refresh>

		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "moreList",
		props: {},
		data() {
			return {
				show: true,
				show1: true,
				currmentIndex: 1,

				loading: true,
				loading1: true,
				isShow: false,
				isLoading: false,

				sortIndex: 0,
				list: [],
				type: "zhangfb",
			};
		},
		computed: {
			sort() {
				return [{
						name: this.$t("漲幅榜"),
						id: 1
					},
					{
						name: this.$t("跌幅榜"),
						id: 0
					},
					{
						name: this.$t("成交額"),
						id: 2
					},
					// { name: "創高榜", id: 3 },
					// { name: "創低榜", id: 4 },
				];
			},
			filterName() {
				return (value) => {
					value = value.replace("股價", "");
					let indx = value.indexOf("指數");
					return value.slice(indx - 2, indx + 2);
				};
			},
		},
		created() {
			this.getList(0);
		},
		mounted() {},
		methods: {
			changeSort(type) {
				// this.$refs.loading.open();
				this.sortIndex = type;
				// this.list = [];
				// if (type == 0) {
				// 	this.type = "zhangfb";
				// } else if (type == 1) {
				// 	this.type = "diefb";
				// } else {
				// 	this.type = "chengje";
				// }
				this.getList(type);

				// else {
				// 	this.show1 = !this.show1;

				// 	// 漲跌排序
				// 	if (this.show1) {
				// 		this.list = this.list.sort(
				// 			(a, b) => Number(b.gainValue) - Number(a.gainValue)
				// 		);
				// 	} else {
				// 		this.list = this.list.sort(
				// 			(a, b) => Number(a.gainValue) - Number(b.gainValue)
				// 		);
				// 	}
				// }
			},
			// 走的排行榜數據
			getList(type) {
				this.$server
					.post("/parameter/top", {
						type: "zar",
					})
					.then((res) => {
						this.$refs.loading && this.$refs.loading.close();
						this.loading = false;
						this.isLoading = false;
						// console.log("res", res);
						this.list = res.data;
						if (type == 2) {
							this.show = !this.show;
							// 成交額 排序
							if (this.show) {
								this.list = this.list.sort(
									(a, b) => Number(b.volume) - Number(a.volume)
								);
							} else {
								this.list = this.list.sort(
									(a, b) => Number(a.volume) - Number(b.volume)
								);
							}
						} else if (type == 1) {
							this.list = this.list.filter(item => {
								return item.gainValue > 0
							})
						} else {
							this.list = this.list.filter(item => {
								return item.gainValue < 0
							})
						}
					});
			},
			onRefresh() {
				this.isShow = false;
				this.getList();
			},
			changeList(type) {
				if (type == 0) {
					this.show = !this.show;

					// 成交額 排序
					if (this.show) {
						this.list = this.list.sort(
							(a, b) => Number(b.volume) - Number(a.volume)
						);
					} else {
						this.list = this.list.sort(
							(a, b) => Number(a.volume) - Number(b.volume)
						);
					}
				} else {
					this.show1 = !this.show1;

					// 漲跌排序
					if (this.show1) {
						this.list = this.list.sort(
							(a, b) => Number(b.gainValue) - Number(a.gainValue)
						);
					} else {
						this.list = this.list.sort(
							(a, b) => Number(a.gainValue) - Number(b.gainValue)
						);
					}
				}
			},
		},
	};
</script>

<style scoped lang="less">
	::v-deep .van-skeleton__row {
		background-color: transparent !important;
	}

	::v-deep .van-skeleton__title {
		background-color: transparent !important;
	}

	.page {
		padding: 1rem 0 0;
		min-height: 100vh;
	}

	.change {
		padding: 0.02rem;
		width: 95%;
		position: fixed;
		top: 0.5rem;
		left: 50%;
		transform: translateX(-50%);
		z-index: 999;
		height: 0.4rem;
		background: #FFFFFF;
		border-radius: 0.04rem 0.04rem 0.04rem 0.04rem;
		.change-item {
			flex: 1;
			height: 0.36rem;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 0.14rem;
			color: #6F6F6F;
			line-height: 0.36rem;
			text-align: center;

			&.active {
				background: linear-gradient( 90deg, #1B167A 0%, #4383C7 100%);
				border-radius: 0.04rem 0.04rem 0.04rem 0.04rem;
				color: #fff;
			}
		}
	}

	.titles {
		padding: 0 0.12rem;
		background: #FFFFFF;
		border-radius: 0.12rem 0;

		div {
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 0.12rem;
			color: #A0A0A0;
		}

		.icon {
			margin-left: 0.05rem;
		}
	}

	.rm {
		margin: 0 0.12rem;
		background: #FFFFFF;
		border-radius: 0.12rem 0.12rem 0.12rem 0.12rem;

		.rm-list {
			padding: 0.12rem;
			.rm-item {
				padding: 0.12rem 0;
				border-bottom:  0.01rem solid #E0E0E0;
				&:last-child{
					border-bottom: none;
				}
				.name {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 0.14rem;
					color: #000000;
				}

				.code {
					margin-top: 0.05rem;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 0.12rem;
					color: #B9B9B9;
				}

				.price {
					height: 0.24rem;
					line-height: 0.24rem;
					border-radius: 0.02rem 0.02rem 0.02rem 0.02rem;
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 0.12rem;
					color: #FFFFFF;
				}

				.per {
					width: 50%;
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 0.14rem;
					.icon {
						margin-right: 0.05rem;
					}
				}
			}
		}
	}

	::v-deep .search {
		border-radius: 0.04rem;
	}

	.index {
		.t {
			font-weight: 500;
			color: #1e1e1e;
			padding: 0 0.1rem 0.1rem;
		}
	}

	.title {
		padding: 0 0.1rem;

		div {
			font-weight: 600;
			font-size: 0.16rem;
			color: #000000;
		}
	}
</style>