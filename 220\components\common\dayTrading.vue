<template>
	<div class="dayTrading">
		<div class="headf">
			<top :title="$t(text)"></top>
		</div>
		<div class="nav-box">
			<div class="nav-item text-center" v-for="(item, index) in navList" :key="index" :class="{ active: currmentIndex === item.type }" @click="changeNav(item.type)" v-if="item.type!=3">
				{{ $t(item.name) }}
			</div>
		</div>
		<template v-if="currmentIndex == 0">
			<div class="tj">
				<div class="box text-center">
					<img src="../../assets/skin/market/img1.png" />
					<!-- type="digit" -->
					<div class="ipt">
						<input v-model="money" @input="inputEvent" :placeholder="$t('dayTrading').txt4" />
					</div>
					<div class="btn-big" @click="buyFn">{{ $t('dayTrading').btn1 }}</div>
				</div>
				
				<div class="box mt-30">
					<div class="txt">
						<div class="title">{{$t('dayTrading').txt1}}</div>
						<div class="">{{$t('dayTrading').txt2}}</div>
						<div class="">{{$t('dayTrading').txt3}}</div>
					</div>
				</div>
			</div>
		</template>
	
		<template v-if="currmentIndex == 1">
			<div class="jiaoYi" v-if="chooseList.length">
				<div class="th flex flex-b" v-if="chooseList.length">
					<div class="th-td flex-1">
						<div class="">{{ $t('dayTrading').txt5 }}</div>
						<div class="">/{{ $t('dayTrading').txt6 }}</div>
					</div>
					<div class="th-td flex-1 text-center">
						<div class="">{{ $t('dayTrading').txt7 }}</div>
						<div class="">/{{ $t('dayTrading').txt8 }}</div>
					</div>
					<div class="th-td flex-1 text-center">
						<div class="">{{ $t('dayTrading').txt9}}</div>
						<div class="">/{{ $t('dayTrading').txt10 }}</div>
					</div>
					<div class="th-td flex-1 text-right"></div>
				</div>
				
				<div class="pageOne" v-if="chooseList.length">
					<div class="list-box">
						<div class="list-content">
							<div class="list-item flex flex-b" v-for="(item, index) in chooseList" :key="index">
								<div class="shares-name-box flex-1 ">
									<div class="shares-name-box">{{ item.stock_name }}</div>
									<div class="code">{{ item.stock_code }}</div>
								</div>
				
								<div class="shares-name-box flex-1 text-center">
									<div class="shares-name-box">{{ $formatMoney(item.stock_num) }}</div>
									<div class="code">{{ $formatMoney(item.buy_price) }}円</div>
								</div>
				
								<div class="shares-name-box num flex-1 text-right green" :class="{ red: item.gain < 0 }">
									<!-- 盈利红色不带加号，亏损蓝色带减号 -->
									<div class="num-font">{{ $formatMoney(item.yingkui) }}円</div>
									<div class="" v-if="item.gain">{{ item.gain.toFixed(2) }}%</div>
								</div>
				
								<div class="flex-1 text-right" @click="sellstrategy(item)">
									<div class="b-btn flex">{{ $t('dayTrading').btn2 }}</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div v-else class="noData text-center">
				{{$t('transactionRecord').txt4}}
			</div>
		</template>
		<template v-if="currmentIndex == 2">
			<div class="mxList qd30" v-if="chooseList1.length">
				<div class="mx-item " v-for="(item, index) in chooseList1" :key="index">
					<div class="flex mb-10">
						<div class="name">{{ item.stock_name || '-' }}</div>
						<div class="code">({{ item.stock_code || '-' }})</div>
					</div>
					<div class="flex flex-b price text-center">
						<div class="lv flex1">
							<div class="green flex-1 num-font" :class="{ red: item.gain < 0 }">
								{{ $formatMoney(item.yingkui) || '-' }}円
							</div>
							<div class="flex-1 t-r">{{ $t('dayTrading').txt11 }}</div>
						</div>
						<div class="lv flex1">
							<div class="green flex-1" :class="{ red: item.gain < 0 }">
								{{ item.gain ? item.gain.toFixed(2) : '0' }}%
							</div>
							<div class="flex-1 t-r">{{ $t('dayTrading').txt10 }}</div>
						</div>
					</div>
	
					<div class="time">{{ $t('dayTrading').txt12 }} {{ item.sell_time || '-' }}</div>
				</div>
			</div>
	
			<div v-else class="noData text-center">
				{{$t('transactionRecord').txt4}}
			</div>
		</template>
		<!-- 已申请清单 -->
		<template v-if="currmentIndex == 3">
			<div class="qd30" v-if="myList.length">
				<div class="mx-item " v-for="(item, index) in myList" :key="index">
					<div class="flex flex-b">
						<div class="flex-2">
							<div class="name st">
								{{ $formatMoney(item.credit) }}
							</div>
						</div>
						<div class="flex-1">
							<div class="b-btn" :class="{ dis: item.state == '已拒绝' }">{{ $t(item.state) }}</div>
						</div>
					</div>
					<div class="time">{{ $t('other').txt20 }} {{ item.strategy_num || '-' }}</div>
					<div class="time">{{ $t('other').txt21 }} {{ item.create_time || '-' }}</div>
					<div class="time">{{ $t('other').txt22 }} {{ item.end_time || '-' }}</div>
				</div>
			</div>
	
			<div v-else class="noData text-center">
				{{$t('transactionRecord').txt4}}
			</div>
		</template>
	
	</div>
</template>

<script>
	import Vue from "vue";
	import qs from "qs";
	import axios from "axios";
	import {
		Toast
	} from "vant";
	Vue.use(Toast);
	
	import top from "../bar/toper.vue";
	
	export default {
		name: "subscribeRecord",
		data() {
			return {
				text:this.$t('home').top9,
				navList: [
					{name: this.$t('dayTrading').tab1,type: 0}, 
					{name: this.$t('dayTrading').tab2,type: 1}, 
					{name: this.$t('dayTrading').tab3,type: 2}, 
					//{name: this.$t('dayTrading').tab4,type: 3}
				],
				currmentIndex: 0,
				chooseList:[],
				chooseList1:[],
				myList:[],
				money:''
			};
		},
		components: {
			top,
		},
		destroyed() {},
		mounted() {
			this.changeNav(this.currmentIndex);
		},
		methods: {
			inputEvent(e) {
				let value = this.money;
				// 限制输入数字
				this.$nextTick(() => {
					this.money = value.replace(/[^0-9]/g, '');
					this.money = this.money.toLocaleString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
				});
			},
			changeNav(index) {
				this.currmentIndex = index;
				switch (index) {
					case 1:
						this.getNew();
						break;
					case 2:
						this.getNew1();
						break;
					case 3:
						this.getMine();
						break;
					default:
						break;
				}
			},
			//普通股票的持仓列表
			getNew() {
				this.$server.post('/riben/userstocklist', {
					buy_type: 1
				}).then(res => {
					if (res.data.status == '1') {
						delete res.data.data.ccsz;
						delete res.data.data.fdyk;
						this.chooseList = Object.values(res.data.data);
					}
				});
			},
			// 普通股票的平仓列表
			getNew1() {
				this.$server.post('/riben/userstocklists', {
					buy_type: 1
				}).then(res => {
					if (res.data.status == '1') {
						delete res.data.data.ccsz;
						delete res.data.data.lsyk;
						this.chooseList1 = Object.values(res.data.data);
					}
				});
			},
			// 已申请列表
			getMine() {
				this.$server.post('/riben/urnjylist', {
					type: 1
				}).then(res => {
					this.myList = res.data.data;
				});
			},
			buyFn(){
				if (!this.money) {
					Toast({
						message: this.$t('dayTrading').txt4,
						duration: 2000,
					});
					return;
				}
				this.money = this.money.replaceAll(',', '');
				this.$server.post('/riben/rnjy', {
					money: this.money
				}).then(res => {
					if (res.data.msg) {
						Toast({
							message: this.$t(res.data.msg),
							duration: 2000,
						});
						return;
					}
				});
			},
			// 平仓
			sellstrategy(item) {
				this.$server.post('/riben/sell_stock', {
					id: item.id,
					buy_type: 1
				}).then(res => {
					if (res.data.status === 1) {
						Toast({
							message: `${this.$t('position').txt4}`,
							duration: 2000,
						});
						this.getNew();
					}else{
						Toast({
							message: this.$t(res.data.msg),
							duration: 2000,
						});
					}
				});
			},
		},
		
	};
</script>

<style lang="less">
	.dayTrading{
		background: #F5F5F5;
		min-height: 100vh;
		padding-top:.44rem;
		.headf {
			background: #ffffff;
			position: fixed;z-index: 888;
			top: 0;
			left: 0;
			width: 100%;
			height:.44rem;
		}
		.nav-box {
			height: .41rem;
			margin:.11rem;padding:0 .09rem;
			display: flex;
			align-items: center;
			justify-content: center;
		
			.nav-item {
				height: .41rem;
				width: calc(100% / 2);
				display: flex;
				align-items: center;
				justify-content: center;
				flex-direction: column;
				font-size: .14rem;
				color: #666;padding-top:.05rem;
				&:after{
					content:'';display: block;
					width: .16rem;
					height: .04rem;
					border-radius: .02rem;
					margin:.05rem auto 0;
				}
			}
		
			.active {
				font-weight: bold;
				color: #333;
				&:after{
					background: #A8EC7A;
				}
			}
		}
		.tj {
			padding: 0 .15rem .15rem;
			.box{
				background: #FFFFFF;
				box-shadow: 0 .02rem .09rem 0 rgba(0,0,0,0.18);
				border-radius: .12rem;
				padding-bottom: .15rem;
				img{
					width:1.98rem;height:1.41rem;
					margin:.56rem auto .2rem auto;
				}
			}
			.mt40 {
				margin-top: .2rem;
			}
		
			.txt {
				font-size: .13rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #A2A7AA;
				line-height: .3rem;
				padding:.1rem;
				.title {
					font-size: .14rem;
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					color: #333333;
				}
			}
		
			.ipt {
				height: .48rem;
				background: #F5F7FA;
				border-radius: .04rem;
				padding: 0 .15rem;
				margin:.13rem .12rem .23rem .12rem;
		
				input {
					height: 100%;width:100%;
					background: transparent;
					color: #999;
				}
			}
			.btn-big{
				margin:0 .06rem;
			}
		}
		.jiaoYi{
			background: #FFFFFF;
			box-shadow: 0 .02rem .09rem 0 rgba(0,0,0,0.18);
			border-radius: .12rem;
			padding-bottom: .15rem;
			margin:.1rem;
			.th {
				padding: .1rem .2rem;
				font-size:.11rem;color: #666;
			}
			.list-box {
				.list-content {
					.list-item {
						padding: .05rem .1rem;
			
						.b-btn {
							height: .23rem;
							background: #8D49FD;
							border-radius: .12rem;padding:0 .15rem;
							display: inline-flex;
							font-size: .13rem;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							color: #FFFFFF;
						}
			
						.shares-name-box {
							font-size: .14rem;
							font-weight: bold;
							color: #24272C;
			
							.code {
								font-size: .12rem;
								font-weight: 400;
								font-family: Roboto;
								color: #A5A9AF;
							}
			
							&.shares-price-num {
								font-family: Roboto;
								font-weight: bold;
								color: #ff3636;
							}
						}
			
						.new-price {
							display: flex;
							width: 17%;
			
							.shares-price-num {
								font-size: .14rem;
								font-family: PingFangSC-Medium, PingFang SC;
								font-weight: 500;
								color: #333;
							}
						}
			
						.shares-price {
							display: flex;
							width: 17%;
			
							.shares-price-num {
								font-size: .14rem;
								font-family: PingFangSC-Medium, PingFang SC;
								font-weight: 500;
								color: #333;
							}
						}
			
						.number {
							display: flex;
							width: 17%;
			
							.shares-price-num {
								font-size: .14rem;
								font-family: PingFangSC-Medium, PingFang SC;
								font-weight: 500;
								color: #333;
							}
						}
			
						.new-code {
							display: flex;
							width: 17%;
							justify-content: flex-end;
						}
					}
				}
			}
			
		}
		.qd30{
			padding: .1rem .15rem;
			
			.mx-item {
				background: #FFFFFF;
				box-shadow: 0 .02rem .09rem 0 rgba(0,0,0,0.18);
				border-radius: .12rem;
				padding: .15rem .1rem;
				margin-bottom: .1rem;
			
				.b-btn {
					margin: 0;
			
					&.dis {
						background-color: #e71616;
					}
				}
			
				.name {
					font-size: .13rem;
					color: #231E17;font-weight: 500;
			
					&.st {
						color: #8D49FD;
						font-family: Roboto;
						font-weight: bold;
					}
				}
			
				.code {
					font-size: .13rem;font-weight: 500;
					color: #231E17;margin-left:.05rem;
				}
				
				.price{
					background: #F9FCF6;
					border-radius: .04rem;
					padding:.1rem 0;margin:.1rem 0;
				}
				.num {
					font-size: .18rem;
					font-family: Roboto;
					font-weight: bold;
					color: #333333;
				}
			
				.lv {
					font-size: .11rem;
					color: #999;
			
					.green {
						font-size: .18rem;
						font-family: Roboto;
						font-weight: bold;
						color: #5BD4A6 !important;
			
						&.red {
							color: #E97F88 !important;
						}
					}
				}
			
				.time {
					font-size: .11rem;
					color: #999;
					margin-top: .15rem;
				}
			}
		}
		.noData{
			padding:1rem 0;
			font-size:.12rem;color:#999;
		}
		
	}
</style>