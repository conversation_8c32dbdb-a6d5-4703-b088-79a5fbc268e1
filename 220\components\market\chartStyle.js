const textColorDark = "#929AA5";
const gridColorDark = 'transparent'//"#292929";
const axisLineColorDark = 'transparent'//"#333333";
const crossTextBackgroundColorDark = "#373a40";
const textColorLight = "#76808F";
const gridColorLight = "#ededed";
const axisLineColorLight = "#DDDDDD";
const crossTextBackgroundColorLight = "#686d76";

let language;
let language1;
let lang = window.localStorage.getItem("lang") || 'jp';
switch (lang) {
	case 'tw':
		language = ['時間：', '開：', '收：', '高：', '低：', '成交量：'];
		language1 = ["開：", "收：", "漲跌："];
		break;
	case 'en':
		language = ["Time:", 'Open:', 'Close:', 'High:', 'Low:', 'Volume:'];
		language1 = ["Open:", "Close:", "Change:"];
		break;
	case 'jp':
		language = ["時間:","始値:","終値:","高値:","安値:","音量:"];
		language1 = ["開く:", "閉じる:", "変化"];
		break;
}

export function getThemeOptions(theme) {
    const textColor = theme === "dark" ? textColorDark : textColorLight;
    const gridColor = theme === "dark" ? gridColorDark : gridColorLight;
    const axisLineColor =
        theme === "dark" ? axisLineColorDark : axisLineColorLight;
    const crossLineColor =
        theme === "dark" ? axisLineColorDark : axisLineColorLight;
    const crossTextBackgroundColor =
        theme === "dark" ?
            crossTextBackgroundColorDark :
            crossTextBackgroundColorLight;
    return {
        grid: {
            horizontal: {
                color: gridColor,
            },
            vertical: {
                color: gridColor,
            },
        }, 
        candle: {
            bar: {
                upColor: "#53B15F",
                downColor: "#D73E35",
                noChangeColor: "#888888",
            },
            priceMark: {
                high: {
                    color: textColor,
                },
                low: {
                    color: textColor,
                },
            },
            tooltip: {
                text: {
                    color: textColor,
                },
                labels: language,//['時間：', '開：', '收：', '高：', '低：', '成交量：']
            },
        },
        technicalIndicator: {
            tooltip: {
                text: {
                    color: textColor,
                },
            },
            bar: {
                upColor: "#53B15F",
                downColor: "#D73E35",
                noChangeColor: "#888888",
            },
        },
        xAxis: {
            axisLine: {
                color: axisLineColor,
            },
            tickLine: {
                color: axisLineColor,
            },
            tickText: {
                color: textColor,
            },
        },
        yAxis: {
            axisLine: {
                color: axisLineColor,
            },
            tickLine: {
                color: axisLineColor,
            },
            tickText: {
                color: textColor,
            },
        },
        separator: {
            color: axisLineColor,
        },
        crosshair: {
            horizontal: {
                line: {
                    color: crossLineColor,
                },
                text: {
                    backgroundColor: crossTextBackgroundColor,
                },
            },
            vertical: {
                line: {
                    color: crossLineColor,
                },
                text: {
                    backgroundColor: crossTextBackgroundColor,
                },
            },
        },
    };
}

export function getTooltipOptions(
    candleShowType,
    candleShowRule,
    technicalIndicatorShowRule
) {
    return {
        candle: {
            tooltip: {
                showType: candleShowType,
                showRule: candleShowRule,
                labels: language1,//["Open:", "Receive:", "Ups/downs:"],
                values: (kLineData) => {
                    const change =
                        ((kLineData.close - kLineData.open) / kLineData.open) * 100;
                    return [
                        { value: kLineData.open.toFixed(2) },
                        { value: kLineData.close.toFixed(2) },
                        {
                            value: `${change.toFixed(2)}%`,
                            color: change < 0 ? "#D73E35" : "#53B15F",
                        },
                    ];
                },
            },
        },
        technicalIndicator: {
            tooltip: {
                showRule: technicalIndicatorShowRule,
            },
        },
    };
}

let getTime = (str) => {
    let year = str.substring(0, 4);
    let month = str.substring(4, 6)
    let day = str.substring(6, 8);
    let h = str.substring(8, 10)
    let m = str.substring(10, 12);
    let time = new Date();
    time.setFullYear(parseInt(year))
    time.setMonth(parseInt(month) - 1)
    time.setDate(parseInt(day))
    time.setHours(parseInt(h || '0'));
    time.setMinutes(parseInt(m || '0'));
    return time;
}

const apis = {
    // 5m 10m 30m d w m   从左到右为5分 10分 30分 日线 周线 月线
    getMinKEcharts: async(that, opt) => {
        const dataList = [];
        if (!opt.code) {
            return { data: dataList, status: 0, msg: 'ok' }
        }
        let res = await that.$server.post('/trade/kline', { symbol: opt.code, kline_type: opt.type, type: 'jpy' })
        let ta = res.data.data;
        for (let i = 0; i < ta.length; i++) {
            let val = ta[i];
            let time = val.time * 1000;
            let kLineModel = {
                open: Number(val.open),
                low: Number(val.low),
                high:Number( val.high),
                close: Number(val.close),
                volume: Number(val.volume),
                timestamp: time,
            };
            kLineModel.turnover =
                (((kLineModel.open) + (kLineModel.close) +( kLineModel.high) +( kLineModel.low)) /
                    4) *
                kLineModel.volume;
            dataList.push(kLineModel);
        }
        return { data: dataList, status: 0, msg: 'ok' };
    },
    // 分时数据
    getMinuteLine: async(that, opt) => {
        const dataList = [];
        if (!opt.code) {
            return { data: dataList, status: 0, msg: 'ok' }
        }
        let res = await that.$server.post('/trade/kline', { symbol: opt.code, kline_type: opt.type, type: 'jpy' })
        let ta = res.data.data;
        for (let i = 0; i < ta.length; i++) {
            let val = ta[i];
            let time = val.time * 1000;
            let kLineModel = {
                open: Number(val.open),
                low: Number(val.low),
                high:Number( val.high),
                close: Number(val.close),
                volume: Number(val.volume),
                timestamp: time,
            };
            kLineModel.turnover =
                (((kLineModel.open) + (kLineModel.close) +( kLineModel.high) +( kLineModel.low)) /
                    4) *
                kLineModel.volume;
            dataList.push(kLineModel);
        }
        return { data: dataList, status: 0, msg: 'ok' };
    },
    getDayK: (that, opt) => {
        return api.getMinKEcharts(that, {...opt, type: 'd' });
    },
    isOption: function(that, opt) {

    },
    addOption: function(that, opt) {

    },
    delOption: function(that, opt) {

    },

}

export var api = apis;
