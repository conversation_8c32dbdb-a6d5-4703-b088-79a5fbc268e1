<template>
	<div>
		<h2>
			<span @click="fanhui" class="hbnh"><a class="fan"></a></span>
			 {{title}}
			 <a class="xind" v-show="addbark" @click="tianjia">{{$t("新增")}}</a>
			 <a class="shaux" v-show="refresh" @click="dainmk"></a>
			 <a class="xind" v-show="jilu" @click="gorecord">{{$t("交易记录")}}</a>
		</h2>
		<!-- <div class="ghb"></div> -->
	</div>
</template>
<script type="text/javascript">
export default {
	name:"toper",
	props:["title","addbark",'refresh','jilu'],
	data(){
		return {
			
		}
	},
	components:{
		
	},
	methods:{
		fanhui(){
			this.$router.go(-1);
			
		},
		tianjia(){
			this.$router.push({path:'/user/BankCard'})
		},
		dainmk(){
			this.$emit('refule')
		},
		gorecord(){
			
		}
	},
	mounted(){
		
		
	},
}
</script>
<style type="text/css" lang="less" scoped="scoped">


h2{
	// text-align: center;
	padding-left: 0.5rem;
	height:0.47rem;
	width:100%;
	position:relative;
	line-height: 0.47rem;
	font-weight: 500;
	font-size: 0.16rem;
	color: #FFFFFF;
	background: transparent;
	background: #424E4E;
	z-index: 3;
	.hbnh{
		position: absolute;
		left:0.15rem;
		font-size:0.16rem;
		font-weight:500;
		.fan{
			width:0.2rem;
			height:0.15rem;
			background: url(../../assets/v2/back.png) no-repeat center;
			background-size:100%;
			display: inline-block;
			margin-right:0.05rem;
			vertical-align: middle;
			margin-top:-0.02rem;
		}
	}
	.shaux{
		position: absolute;
		width:0.18rem;
		height:0.18rem;
		background: url(../../assets/skin/mine/shuaxin.png)no-repeat center;
		background-size:100%;
		right:0.15rem;
		top:0.15rem;
	}
	.xind{
		position: absolute;
		right:0.15rem;
		color:#fff;
		font-size:0.14rem;
		font-weight: 500;
	}
}
.ghb{
	height:0.47rem;
	width:100%;
}
</style>
