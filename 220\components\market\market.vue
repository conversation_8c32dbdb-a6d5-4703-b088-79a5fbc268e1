<template>
	<div class="market">
		<topCom></topCom>
		<div class="imgGG" @click="showG=false" v-if="showG">
			<div class="icon close02"></div>
		</div>
		<div class="tabBg">
			<div class="tab flex">
				<div class="item" :class="{'sel':tabIdx==0}" @click="tabIdx=0,clickTab()">{{$t('market').tab.txt1}}
				</div>
				<div class="item" :class="{'sel':tabIdx==1}" @click="tabIdx=1,clickTab()">{{$t('market').tab.txt2}}
				</div>
			</div>
		</div>
		<div v-if="tabIdx==0" class="stockList">
			<div class="title">{{$t('other').txt19}}</div>
			<div class="topThree flex flex-b flex-wrap">
				<div class="item" :class="{'red':item.gain>=0,'green':item.gain<0}" v-for="(item,idx) in index" :key="idx" v-if="idx<4">
					<div class="name flex flex-b">
						<div class="txt" v-if="item.name == 'TOPIX 100 Index'">東証株価100指数</div>
            <div class="txt" v-else>{{item.local_name}}</div>
						<span>{{item.symbol}}</span>
					</div>
					<div class="per flex">
						<span class="mr-20">{{$formatMoney(item.gainValue)}}</span>
						<span>{{$formatMoney(item.gain)}}%</span>
					</div>
					<div class="flex flex-b">
						<div class="flex">
							<img src="../../assets/v2/up.png" v-if="item.gain>=0" />
							<img src="../../assets/v2/down.png" v-else />
							<div class="price">{{$formatMoney(item.price)}}</div>
						</div>
					</div>
				</div>
			</div>
			<div class="list">
				<div class="title">{{$t('other').txt18}}</div>
				<div class="itemBox">
					<div class="item flex" v-for="(item,idx) in list" :key="idx"
						@click="clickNext('/market/marketDetail?symbol='+item.symbol.replace('TSE:',''))">
						<div class="name">
							<div class="txt">{{item.local_name}}</div>
							<span>{{item.symbol}}</span>
						</div>
						<div class="price flex-1 flex flex-c" >
							<img src="../../assets/v2/up.png" v-if="item.gain > 0" />
							<img src="../../assets/v2/down.png" v-else />
							{{$formatMoney(item.price)}}円
						</div>
						<div class="per flex-1" :class="{'red':item.gain>=0,'green':item.gain<0}">
							<div class="pert">{{ item.gain < 0 ? '' : '+' }}{{ $formatMoney(item.gainValue) }}円</div>
							<div>{{ item.gain < 0 ? '' : '+' }}{{ $formatMoney(item.gain) }}%</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div v-if="tabIdx==1" class="myList">
			<div class="title">{{$t('other').txt17}}</div>
			<div class="itemBox">
				<div class="item flex flex-b" v-for="(item,idx) in myList" :key="idx"
					@click="clickNext('/market/marketDetail?symbol='+item.symbol)">
					<div class="name">
						<div class="txt">{{item.name}}</div>
						<span>{{item.symbol}}</span>
					</div>
					<div class="price text-center">
						<img src="../../assets/v2/up.png" v-if="item.gain > 0" />
						<img src="../../assets/v2/down.png" v-else />
						{{$formatMoney(item.price)}}円
					</div>
					<div class="per flex-1" :class="{'red':item.gain>=0,'green':item.gain<0}">
						<div class="pert">{{ item.gain < 0 ? '' : '+' }}{{ $formatMoney(item.gainValue) }}円</div>
						<div>{{ item.gain < 0 ? '' : '+' }}{{ $formatMoney(item.gain) }}%</div>
					</div>
				</div>
				<div class="addBtn flex flex-c" @click="clickNext('/home/<USER>')">
					<span>+</span>{{$t('market').stockHot.txt1}}
				</div>
			</div>
		</div>
		<!-- 底部菜单 -->
		<bottomnav :on='1'></bottomnav>
	</div>
</template>

<script type="text/javascript">
	import Vue from 'vue';
	import {
		Toast
	} from 'vant';
	Vue.use(Toast);
	import bottomnav from "../bar/bottomnav.vue";
	import topCom from '../bar/topCom.vue'
	export default {
		name: "market",
		data() {
			return {
				tabIdx: 0,
				list: [],
				myList: [],
				index: [],
				showG:true
			}
		},
		components: {
			bottomnav,
			topCom
		},
		methods: {
			clickTab() {
				if (this.tabIdx == 0) {
          this.getIndex()
					this.getInfo();
				} else if (this.tabIdx == 1) {
					this.getMine();
				}
			},
      getIndex() {
        this.$server.post('/parameter/zhishu', {
          type: 'jpy'
        }).then(res => {
          this.index = res.data.data
        });
      },
			getInfo() {
				this.$server.post('/parameter/top', {
          type: 'jpy'
				}).then(res => {
          this.list = res.data.data
				});
			},
			getMine() {
				this.$server.post("/user/Optionallist", {type: 'jpy'}).then((res) => {
					let arr = [];
					res.data.data.forEach(item => {
						if (item.name) {
							arr.push(item)
						}
					})
					this.myList = arr;
				});
			},
			clickDetail(item) {
				this.clickNext('/market/marketDetail?symbol=' + item.symbol)
			},
		},
		destroyed() {

		},
		mounted() {
			this.clickTab();
		},
	}
</script>

<style lang="less">
	.imgGG{
		position: fixed;
		bottom: 0.6rem;
		width: 100%;height: 0.7rem;
		background: url('../../assets/v2/xwbg.png') no-repeat center/100%;
		.close02{
			margin-left: 3.5rem;
			margin-top: 0.1rem;
		}
		z-index: 999;
	}
	.market {
		background: #0F161C;
		overflow: hidden;
		min-height: 100vh;
		.header {
			width: 100%;
			height: .64rem;
			background: #fff;
			font-weight: 500;
			font-size: .18rem;
			color: #000000;

			.headImg {
				margin-left: .12rem;

				img {
					width: .38rem;
					display: block;
				}
			}

			.ico {
				margin-right: .12rem;

				img {
					width: .32rem;
					display: block;
				}
			}

			.search {
				height: .38rem;
				background: rgba(227, 227, 227, .54);
				border-radius: .19rem;
				font-size: .15rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #C1C4C4;
				margin: 0 .18rem;
			}
		}

		.tabBg {
			background: #0F161C;
			padding: 0.1rem 0.12rem;

			.tab {
				background: #1B232A;
				border-radius: 0.1rem;
				padding: 0.05rem;

				.item {
					height: 0.38rem;
					width: 50%;
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.14rem;
					color: #B3B8B8;
					text-align: center;
					line-height: 0.38rem;
				}

				.sel {
					background: #5ED5A8;
					border-radius: 0.1rem;
					color: #000000;
					font-weight: 600;
				}
			}
		}
		
		.title {
			font-size: .16rem;
			padding: 0.05rem 0.12rem;
			background-color: rgba(94, 213, 168, 0.2);
			border-bottom: .03rem solid #fff;
			font-family: PingFangSC, PingFang SC;
			font-weight: 600;
			font-size: 0.14rem;
			color: #FFFFFF;
		}
		
		.stockList {
			height: calc(100vh - 1.6rem);
			overflow: scroll;
			.topThree {
				margin: .1rem 0;
				padding: 0 .12rem;

				.item {
					width: 48%;
					background: #1B232A;
					border-radius: 0.1rem;
					margin-bottom: 0.1rem;
					padding: 0.1rem;

					// &.red{
					// 	background: #F7E8E8;
					// }
					// &.green{
					// 	background: #3498DA;
					// }
					.name {
						font-family: PingFangSC, PingFang SC;
						font-weight: 500;
						font-size: 0.12rem;
						color: #FFFFFF;
						margin: .05rem .08rem;

						span {
							font-size: .09rem;
							color: #FFFFFF;
						}

						.txt {
							width: 100%;
							overflow: hidden;
							white-space: nowrap;
							text-overflow: ellipsis;
						}
					}

					.price {
						font-weight: 500;
						font-size: 0.18rem;
					}

					img {
						width: .08rem;
						margin-right: .03rem;
					}

					.per {
						font-weight: 400;
						font-size: 0.12rem;
					}

					.line {
						width: 40%;
						height: 0.3rem;
						border: 0.01rem solid #FE0000;
					}
				}
			}
			.list {
				.itemBox {
					margin-top: 0.1rem;
					padding-bottom: 1rem;
					.item {
						padding: 0.05rem 0.12rem;
						border-bottom: 0.01rem solid rgba(255, 255, 255,0.16);
						.name {
							font-family: PingFangSC, PingFang SC;
							font-weight: 500;
							font-size: 0.14rem;
							color: #FFFFFF;
							width: 30%;
							.txt {
								white-space: nowrap;
								overflow: hidden;
								text-overflow: ellipsis;
							}

							span {
								font-size: 0.13rem;
								color: #718A94;
							}
						}

						.price {
							font-family: PingFangSC, PingFang SC;
							font-weight: 500;
							font-size: 0.18rem;
							color: #FFFFFF;
						}

						img {
							width: .08rem;
							margin-right: 0.05rem;
						}
						.per {
							font-size: 0.14rem;
							text-align: center;
							display: flex;
							align-items: center;
							flex-direction: column;
							justify-content: flex-end;
							.pert{
								font-weight: 500;
								font-size: 0.16rem;
								color: #FFFFFF;
							}
						}
						.line {
							width: 18%;
							height: 0.3rem;
							border: 0.01rem solid #FE0000;
						}
					}
				}
			}
			.more {
				font-weight: 500;
				font-size: .12rem;
				color: #999999;
				padding: .1rem 0;
			}
		}
		.myList {
			.itemBox {
				padding-bottom: 1rem;
				.item {
					padding: 0.05rem 0.12rem;
					border-bottom: 0.01rem solid rgba(255, 255, 255,0.16);
					.name {
						font-family: PingFangSC, PingFang SC;
						font-weight: 500;
						font-size: 0.14rem;
						color: #FFFFFF;
						width: 30%;
						.txt {
							white-space: nowrap;
							overflow: hidden;
							text-overflow: ellipsis;
						}
				
						span {
							font-size: 0.13rem;
							color: #718A94;
						}
					}
				
					.price {
						font-family: PingFangSC, PingFang SC;
						font-weight: 500;
						font-size: 0.18rem;
						color: #FFFFFF;
					}
				
					img {
						width: .08rem;
						margin-right: 0.05rem;
					}
					.per {
						font-size: 0.14rem;
						text-align: center;
						display: flex;
						align-items: center;
						flex-direction: column;
						justify-content: flex-end;
						.pert{
							font-weight: 500;
							font-size: 0.16rem;
							color: #FFFFFF;
						}
					}
					.line {
						width: 18%;
						height: 0.3rem;
						border: 0.01rem solid #FE0000;
					}
				}

				.addBtn {
					height: 0.46rem;
					background: #5ED5A8;
					border-radius: 0.1rem;
					margin: .35rem 0.12rem;
					font-family: PingFangSC, PingFang SC;
					font-weight: 600;
					font-size: 0.16rem;
					color: #171D22;
					span {
						padding-right: .05rem;
						
						line-height: 0.46rem;
					}
				}
			}
		}
	}
</style>