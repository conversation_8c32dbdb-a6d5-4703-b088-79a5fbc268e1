<template>
	<!-- 新股申购 -->
	<div class="page ">
		<top-back title="競價拍賣"></top-back>
		<!-- <div class="icon ss" @click="goUrl('/favorite/search')"></div> -->
		<!-- <div class="nav-box">
			<div class="nav-item" v-for="(item, idx) in navList" :key="idx"
				:class="{ active: currmentIndex === item.type }" @click="changeNav(item.type)">
				{{ item.name }}
				<span></span>
			</div>
		</div> -->
		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<van-skeleton title :row="26" :loading="loading">
				<div class="cot" v-if="ksgList.length">
					<div class="item" v-for="(item, i) in ksgList" :key="i" @click="toDetail(item)">
						<div class="item-top flex flex-b">
							<div>
								<div class="flex">
									<div class="name">{{ item.name || "-" }}</div>
									<div class="code">{{ item.symbol || "-" }}</div>
								</div>
								<div class="time">截止日:{{ $formatDate("MM-DD", item.end * 1000) }}</div>
							</div>
							<div class="btn flex flex-c" v-if="item.isEnd">已截止</div>
							<div class="btn flex flex-c" v-if="item.isKsg">競標中</div>
							<div class="btn btn1 flex flex-c" v-if="!item.isEnd&&!item.isKsg">未開始</div>
							<!-- <div class="item-list bt">溢價差 {{ item.mprice - item.sprice > 0 ? "+" : ""}}{{ ((item.mprice - item.sprice) / item.sprice) * 100 == -100 ? "-": ( ((item.mprice - item.sprice) / item.sprice) * 100).toFixed(2)}}%</div> -->
						</div>
						<div class="">
							<!-- <div class="jzrBox">
								<van-circle v-model="currentRate" :rate="30" :speed="100"  size="100px" layer-color="#ececec" stroke-width="80" color="#2a3e97" />
								<div class="jzr flex-column-item">
									<div class="t2 ">截止日</div>
									<div class="t3 flex flex-c">{{ $formatDate("MM-DD", item.end * 1000) }}</div>
								</div>
							</div> -->
							<div class="item-middle flex flex-b flex-wrap">
								<div class="item-list t-c">
									<div class="t3 "> {{ $formatMoney(item.bprice) }}</div>
									<div class="t2 ">承銷價</div>
								</div>
								<div class="item-list t-c">
									<div class="t3">{{ $formatMoney(item.price) }}</div>
									<div class="t2">市價</div>
								</div>
								<div class="item-list t-c">
									<div class="t3">{{ $formatMoney(item.price - item.bprice) }}</div>
									<div class="t2 ">差價</div>
								</div>
								<div class="item-list t-c">
									<div class="t3">{{ $formatMoney(item.num, 0) || "-" }}</div>
									<div class="t2 ">總競拍</div>
								</div>
								<!-- <div class="item-list t-r">
									<div class="t2">撥券日</div>
									<div class="t3">{{ item.amtdate }}</div>
								</div> -->
							</div>
						</div>
					</div>
				</div>
				<no-data v-if="isShow"></no-data>
			</van-skeleton>
		</van-pull-refresh>
		<!--点击按钮加载效果组件 -->
		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "newStockJp",
		data() {
			return {
				loading: true,
				isShow: false,
				isLoading: false,
				currmentIndex: 0,
				navList: [
					// { name: this.$t("新股申购"), type: 0 },
					// { name: this.$t("新股配售"), type: 1 },
				],
				ksgList: [
					// {
					//   name: "名称",
					//   symbol: "0089",
					//   code: "100000",
					//   price: 1000,
					//   bprice: 10,
					//   amount: 100,
					//   markets: "999",
					//   drawdate: "2024-03-04 18:00",
					//   ssdate: "2024-03-04 18:00",
					//   amtdate: "2024-03-04 18:00",
					//   isKsg: true,
					// },
				],
				userInfo: {},
				quantity: "",
			};
		},
		mounted() {
			this.getList();
		},
		methods: {
			goUrl(item) {
				if (item.url == 'kefu') {
					this.getConfig()
				} else if (item.url == 'kefu1') {
					this.getConfig1()
				} else {
					this.$router.push({
						path: item
					})
				}
			},
			// 下拉刷新
			onRefresh() {
				this.isShow = false;
				this.getList();
			},
			changeNav(type) {
				this.currmentIndex = type;
				this.getList();
			},
			// 获取列表
			getList() {
				this.$server
					.post("/trade/placinglist", {
						type: "twd",
						buy_type: 1
					})
					.then((res) => {
						this.isLoading = false; //下拉刷新状态
						this.loading = false;
						if (res.status == 1) {
							let now = new Date().getTime();
							let arr = [];
							res.data.forEach((item) => {
								// 可申购
								if (item.start * 1000 < now && now < item.end * 1000) {
									item.time = Math.floor(
										(item.end * 1000 - now) / 1000 / 60 / 60 / 24
									);
									item.isKsg = true; //是否可申购
									item.isEnd = false;
								} else if (now < item.start * 1000) {
									item.time = Math.floor(
										(item.start * 1000 - now) / 1000 / 60 / 60 / 24
									);
									// 待申购
									item.isKsg = false;
									item.isEnd = false;
								} else {
									// 已截止
									item.isKsg = false;
									item.isEnd = true;
								}

								arr.push(item);
							});

							this.ksgList = [...new Set(arr)];
							this.ksgList =this.ksgList.sort((a,b)=>{
								return b.end-a.end
							})
							if (!this.ksgList.length) {
								this.isShow = true;
							}
						}
					});
			},
			toDetail(item) {
				this.$storage.save("itemTemp", item);
				this.$toPage(`/home/<USER>
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.5rem 0rem 0.1rem;
		min-height: 100vh;

		.ss {
			position: fixed;
			z-index: 999;
			top: 0.14rem;
			z-index: 999;
			right: 0.12rem;
		}
	}

	::v-deep .van-skeleton__row {
		background-color: transparent !important;
	}

	::v-deep .van-skeleton__title {
		background-color: transparent !important;
	}

	.nav-box {
		width: 100%;
		position: fixed;
		top: 0.5rem;
		left: 0;
		z-index: 999;
		background: #002f7c;

		.nav-item {
			margin: 0 0.05rem;
			flex: 1;
			height: 0.41rem;
			background: #D8D8D8;
			border-radius: 0.09rem 0.09rem 0rem 0rem;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 0.16rem;
			color: #333333;
			line-height: 0.41rem;
			text-align: center;
			position: relative;

			&.active {
				background: #fff;
				font-weight: 600;
				color: #002f7c;
			}
		}
	}

	.cot {
		padding: 0.12rem;

		.item {
			background: #232429;
			border-radius: 0.13rem;
			margin-bottom: 0.15rem;
			padding: 0.12rem;
			.item-top {
				padding-bottom: 0.12rem;
			
				.name {
					font-family: PingFang SC;
					font-weight: 500;
					font-size: 0.15rem;
					color: #FFFFFF;
				}
				.code {
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 0.11rem;
					color: #999999;
					margin-left: 0.05rem;
				}
				.time{
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 0.11rem;
					color: #999999;
				}
				.t3 {
					font-size: 0.16rem;
				}
			
				.t4 {
					font-size: 0.12rem;
					color: #fff;
					margin-top: 0.05rem;
				}
			}
			.jzrBox{
				position: relative;
				margin-right: 0.1rem;
				.jzr{
					position: absolute;
					top: 50%;
					left: 50%;
					transform: translate(-50%,-50%);
					.t2{
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 0.12rem;
						color: #393939;
					}
					.t3{
						margin-top: 0.05rem;
						font-family: PingFang SC, PingFang SC;
						font-weight: 600;
						font-size: 0.16rem;
						color: #000000;
					}
				}
			}
			.item-middle {
				padding: 0.1rem;
				background: #434446;
				border-radius: 0.09rem;
				.item-list {
					line-height: 0.2rem;
					.t2 {
						font-family: PingFang SC;
						font-weight: 400;
						font-size: 0.11rem;
						color: #999999;
					}
			
					.t3 {
						font-family: PingFang SC;
						font-weight: 500;
						font-size: 0.14rem;
						color: #FFFFFF;
					}
					.green {
						color: #39B44C;
					}
					.red {
						color: #ba3b3a;
					}
				}
			}
			.btn {
				height: 0.27rem;
				background: #599161;
				border-radius: 0.13rem;
				padding: 0 0.1rem;
				font-family: PingFang SC;
				font-weight: 500;
				font-size: 0.13rem;
				color: #000000;
				line-height: 0.27rem;
				&.btn1{
					background: #8EFE99;
				}
			}
		}
	}

	.list {
		.item {
			padding: 40px 20px;
			border-bottom: 2px solid #cccccc;

			.mb20 {
				margin-bottom: 20px;
			}

			.name {
				font-size: 36px;
				font-weight: bold;
				color: #333333;
			}

			.code {
				padding: 10px 20px;
				background: #f0f3fa;
				border-radius: 10px;
				font-size: 24px;
				font-family: Roboto;
				font-weight: 400;
				color: #333333;
				text-align: center;
				margin-top: 10px;
			}

			.time {
				padding: 20px;
				background: #f7e8e8;
				border-radius: 20px;
				font-size: 32px;
				font-weight: bold;
				color: #ff3636;
			}
		}

		.per {
			width: 180px;
			height: 180px;
			border: 10px solid #d4e0eb;
			border-radius: 50%;
			overflow: hidden;
			padding: 40px 0 0;
			text-align: center;
			position: relative;
			margin-right: 20px;

			.t {
				// font-size: 44px;
				font-size: 36px;
				font-family: Roboto;
				font-weight: bold;
				color: #ff3636;
				line-height: 48px;
			}

			.t1 {
				width: 100%;
				color: #333333;
				background-color: #d4e0eb;
				position: absolute;
				padding: 5px 0;
				bottom: 0px;
				font-size: 24px;
			}
		}

		.b-btn {
			margin: 0;
			width: 45%;
			height: 60px;
			line-height: 60px;

			&.bt {
				background-color: #ccc;
			}
		}
	}
</style>