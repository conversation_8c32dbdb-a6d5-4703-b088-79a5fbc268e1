<template>
	<div class="login">
		<div class="head" @click="clickBack()">
			<img src="../../assets/v2/close.png" />
		</div>
		<!--		<div class="flex-column-item">-->
		<!--			<img class="img" src="../../assets/v2/loginIcon02.png" />-->
		<!--		</div>-->
		<div class="info" :class="inputShow?'margin-left-0':''">
			<div class="info-title">
				<div>{{$t('login').title}}</div>
				<div class="flex" @click="toRegister">
					<div class="t01">{{$t('other').txt3}}</div>
					<div class="t02">{{$t('other').txt30}}</div>
					<img src="../../assets/v2/ljIcon.png" style="width: 0.11rem;height: 0.11rem;" alt="" />
				</div>
			</div>
		</div>
		<div class="InputBox" :class="inputShow?'show':''">
			<div class="input" :class="inputShow?'margin-left-0':''">
				<div class="title mb-10">{{$t('register').txt2}}</div>
				<div class="input-item">
					<!-- <img class="img1" src="../../assets/skin/login/ico2.png" /> -->
					<input v-model="login.account" type="text" :placeholder="$t('login').txt3"
						placeholder-style="color: rgba(153, 153, 153, 1);" />
					<div class="ccheck" @click="setAc">
						<img src="../../assets/check-on.png" v-if="flagAccount" />
						<img src="../../assets/check.png" v-else />
						{{$t('j1')}}
					</div>
				</div>
				<div class="title mb-10">{{$t('login').txt13}}</div>
				<div class="input-item" v-if="showPass">
					<!-- <img class="img1" src="../../assets/skin/login/ico3.png" /> -->
					<input v-model="login.password" type="text" :placeholder="$t('login').txt4"
						placeholder-style="color: rgba(153, 153, 153, 1);" />
					<div class="icon zy" @click="showPass=!showPass" style="margin-left: 0.1rem;"></div>
					<div class="ccheck" @click="setPs">
						<img src="../../assets/check-on.png" v-if="flagPassword" />
						<img src="../../assets/check.png" v-else />
						{{$t('j1')}}
					</div>
				</div>
				<div class="input-item" v-else>
					<!-- <img class="img1" src="../../assets/skin/login/ico3.png" /> -->
					<input v-model="login.password" type="password" :placeholder="$t('login').txt4"
						placeholder-style="color: rgba(153, 153, 153, 1);" />
					<div class="icon by" @click="showPass=!showPass" style="margin-left: 0.1rem;"></div>
					<div class="ccheck" @click="setPs">
						<img src="../../assets/check-on.png" v-if="flagPassword" />
						<img src="../../assets/check.png" v-else />
						{{$t('j1')}}
					</div>
				</div>
			</div>
			<div class="reg flex flex-b">
				<!--				<div class="checkbox flex">-->
				<!--					<div class="rember flex" @click="flagAccount=!flagAccount,checked=!checked">-->
				<!--						<div class="icon wgx" v-if="checked"></div>-->
				<!--						<div class="icon gx" v-else></div>-->
				<!--						<div>{{$t('login').txt5}}</div>-->
				<!--					</div>-->
				<!--				</div>-->
				<span @click="toForget">{{$t('login').txt10}}</span>
				<div class="kefu" @click="clickKf()">{{$t('login').txt1}}</div>
			</div>
		</div>
		<div class="Btn text-center" :class="{'show':btnShow}">
			<div class="link1 flex flex-c" @click="defaultHandlerLogin">{{$t('login').txt2}}</div>
			<!-- <div class="link2 flex flex-c" @click="toRegister">{{$t('register').txt14}}</div> -->
			<div class="link3" @click="clickKf">{{$t('login').txt1}}</div>
		</div>
		<div class="popSuccess" v-if="flagSuccess">
			<div class="logo text-center">
				<!--				<img src="../../assets/v2/logo.png" />-->
				<img :src="newLogo">
			</div>
			<div class="itemBox">
				<!-- <div class="img">
					<img src="../../assets/skin/start/img3.png" />
				</div> -->
				<div class="title text-center">
					<div class="txt">{{$t('login').tip5}}</div>
					<div>{{$t('login').txt17}}</div>
					<div>{{$t('login').txt18}}</div>
					<img class="" src="../../assets/v2/jzz.png"
						style="width: 0.5rem;height: 0.5rem;margin-top: 0.3rem;" />
				</div>
			</div>
		</div>
	</div>
</template>

<script>
	import {
		mapMutations
	} from "vuex";
	import Vue from "vue";
	import {
		Toast
	} from "vant";
	Vue.use(Toast);
	export default {
		data() {
			return {
				flagSuccess: false,
				flagAccount: false,
				flagPassword: false,
				agree: false,
				inputShow: false,
				checkboxShow: false,
				buttonShow: false,
				btnShow: false,
				checked: false,
				login: {
					account: "",
					password: "",
				},
				showPass: false,
				newLogo: ''
			};
		},
		mounted() {
			let _this = this
			_this.peiz()
			setTimeout(function() {
				_this.inputShow = true
				setTimeout(function() {
					_this.checkboxShow = true
					setTimeout(function() {
						_this.buttonShow = true
						setTimeout(function() {
							_this.btnShow = true
						}, 100)
					}, 100)
				}, 100)
			}, 800)
			if (window.localStorage.getItem('account')) {
				this.flagAccount = true
				this.login.account = window.localStorage.getItem('account')
			}
			if (window.localStorage.getItem('password')) {
				this.flagPassword = true
				this.login.password = window.localStorage.getItem('password')
			}
			if(this.$route.query.account){
				this.login.account = this.$route.query.account;
			}
		},
		methods: {
			...mapMutations(["saveToken", "saveAccount"]),
			setAc() {
				this.flagAccount = !this.flagAccount
				if (this.flagAccount) {
					window.localStorage.setItem('account', this.login.account)
				} else {
					window.localStorage.removeItem('account')
				}
			},
			setPs() {
				this.flagPassword = !this.flagPassword
				if (this.flagPassword) {
					window.localStorage.setItem('password', this.login.password)
				} else {
					window.localStorage.removeItem('password')
				}
			},
			peiz() {
				this.$server.post("/common/config", {
					type: 'all'
				}).then((str) => {
					if (parseInt(str.status) === 200) {
						str.data.data.forEach((item) => {
							if (item.name == "logo") {
								this.newLogo = item.value;
							}
						});
					} else {
						Toast({
							message: this.$t(str.data.msg),
							duration: 2000,
						});
					}
				});
			},
			clickSwitch(checked) {
				this.checked = checked;
				this.flagAccount = checked;
				window.localStorage.setItem('flagAccount', this.flagAccount);
			},
			clickKf() {
				this.$server.post("/common/config", {
					type: 'all'
				}).then(res => {
					if (parseInt(res.status) === 200) {
						let list = res.data.data;
						let listLength = list.length;
						let a;
						for (a = 0; a < listLength; a++) {
							if (list[a].name === 'kefu') {
								this.kefu = list[a].value;
								this.openInBrowser(list[a].value)
							}
						}
					}
				});
			},
			toForget() {
				Toast(this.$t('login').tip1);
			},
			toRegister() {
				this.clickNext('/login/register');
			},
			defaultHandlerLogin: function() {
				let that = this;
				if (!that.login.account) {
					Toast(this.$t('login').tip2);
					return;
				}
				if (!that.login.password) {
					Toast(this.$t('login').tip3);
					return;
				}
				if (this.flagAccount) {
					window.localStorage.setItem('account', this.login.account)
				}
				if (this.flagPassword) {
					window.localStorage.setItem('password', this.login.password)
				}
				this.login.loading = true;
				this.$server.post("/user/login", {
					account: that.login.account,
					password: that.login.password,
				}).then((res) => {
					let _this = this
					if (res.data.status == 1) {
						_this.login.loading = false;
						_this.$server.defaults.headers.token = res.data.data.token;
						_this.$server.defaults.headers.account = res.data.data.account;
						_this.saveToken(res.data.data.token);
						_this.saveAccount(res.data.data.account);
						that.flagSuccess = true;
						setTimeout(function() {
							_this.clickNext('/home/<USER>')
						}, 2000)
					} else {
						Toast(this.$t(res.data.msg));
					}
				});
			}
		},
	};
</script>

<style scoped lang="less">
	.login {
		overflow-x: hidden;
		min-height: 100vh;
		color: #333333;
		background: rgba(3, 15, 29, 1);

		.ccheck {
			height: 0.4rem;
			background: #030F1D;
			border-radius: 0.1rem;
			padding: 0 .1rem;
			display: flex;
			align-items: center;
			justify-content: center;
			white-space: nowrap;
			font-weight: 400;
			font-size: 0.12rem;
			color: #FFFFFF;
			margin-left: .1rem;

			img {
				width: 0.16rem;
				height: 0.16rem;
				margin-right: .06rem;
			}
		}

		.head {
			height: .7rem;
			display: flex;
			align-items: center;
			padding-left: .15rem;

			img {
				width: .25rem;
			}
		}

		.img {
			width: 1.86rem;
			margin-bottom: .2rem;
		}

		.info {
			width: 100%;
			margin-left: -3rem;
			margin-bottom: .2rem;
			opacity: 0;
			-webkit-transition-duration: .5s;
			-moz-transition-duration: .5s;
			-o-transition-duration: .5s;

			.info-title {
				font-family: FZZhengHeiS-EB-GB;
				margin-top: .1rem;
				margin-left: .19rem;
				font-weight: 600;
				font-size: 0.17rem;
				color: #FFFFFF;

				.t01 {
					font-weight: 400;
					font-size: 0.11rem;
					color: #FFFFFF;
				}

				.t02 {
					font-weight: 400;
					font-size: 0.11rem;
					color: #5ED5A8;
				}
			}
		}

		.InputBox {
			margin: .12rem 0;
			margin-left: -3rem;
			width: 90%;
			opacity: 0;
			-webkit-transition-duration: 1s;
			-moz-transition-duration: 1s;
			-o-transition-duration: 1s;

			&.show {
				margin-left: .19rem;
				opacity: 1;
			}
		}

		.Btn {
			width: 100%;
			font-weight: 500;
			font-size: 0.14rem;
			color: #171D22;
			margin-left: -3rem;
			opacity: 0;
			-webkit-transition-duration: 1s;
			-moz-transition-duration: 1s;
			-o-transition-duration: 1s;

			&.show {
				margin-left: 0;
				opacity: 1;
			}

			.link1 {
				margin: .18rem .21rem .1rem .21rem;
				height: .4rem;
				background: #5ED5A8;
				border-radius: 0.1rem;
			}

			.link2 {
				margin: .18rem .21rem .1rem .21rem;
				height: .4rem;
				background: #999999;
				border-radius: .05rem;
			}

			.link3 {
				margin: .18rem .21rem .1rem .21rem;
				height: .4rem;
				border-radius: .05rem;
				color: #fff;
				font-size: .14rem;
			}
		}

		.button {
			width: 100%;
			box-sizing: border-box;
			padding: .28rem .07rem 0;
			margin-left: -3rem;
			opacity: 0;
			-webkit-transition-duration: .5s;
			-moz-transition-duration: .5s;
			-o-transition-duration: .5s;
			font-size: .13rem;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			color: #333;

			span {
				color: #212121;
			}

			img {
				width: .12rem;
				height: .12rem;
				margin-left: .05rem;
			}
		}

		.input {
			//margin: 0 48rpx;
			//width: calc(100% - 150rpx);
			//background: #E1E1E1;
			border-radius: .1rem;
			margin-left: -3rem;
			opacity: 0;
			-webkit-transition-duration: .5s;
			-moz-transition-duration: .5s;
			-o-transition-duration: .5s;

			.input-item {
				padding: 0 .1rem;
				height: .48rem;
				margin-bottom: .24rem;
				display: flex;
				justify-content: space-between;
				align-items: center;
				background: rgba(27, 35, 42, 1);
				border-radius: 0.1rem;
				font-size: .14rem;
				color: #fff;

				input {
					background: none;
					width: calc(100% - .16rem);
					font-size: .14rem;
					font-family: PingFang SC;
					font-weight: 500;
					padding: 0 .07rem;
				}

				.img1 {
					width: .14rem;
					margin: 0 .05rem 0 .01rem;
				}

				img {
					width: .16rem;
					height: .16rem;
				}

				&:last-child {
					margin-bottom: .13rem;
				}

				&:nth-child(1) {
					//border-bottom: 1px solid rgba(153, 153, 153, 1);
				}
			}

			.title {
				margin-bottom: .04rem;
				font-weight: 400;
				font-size: 0.12rem;
				color: #FFFFFF;
			}
		}

		.reg {
			margin: .12rem 0;
			color: #fff;
			font-size: .14rem;

			.van-switch {
				transform: scale(.7);
			}

			span {
				color: #28B2AD;
			}
		}

		.popSuccess {
			position: fixed;
			top: 0;
			left: 0;
			width: 100%;
			height: 100vh;
			background: #fff;
			background: url('../../assets/v2/bg.png') no-repeat center/100%;

			.logo {
				width: 100%;
				height: 3.69rem;

				img {
					width: 2rem;
					height: 2rem;
					margin-top: .6rem;
					animation: logo1 2s linear infinite;
				}
			}

			.itemBox {
				border-radius: .2rem;
				padding: .25rem;
				margin: 0.5rem .3rem 0 .3rem;
				line-height: .25rem;

				.img {
					width: 100%;
					margin: 0 auto;

					img {
						width: 100%;
						animation: title 1s linear infinite;
					}
				}

				.title {
					padding-top: .15rem;
					font-weight: 400;
					font-size: 0.13rem;
					color: #999999;

					.txt {
						font-weight: 500;
						font-size: 0.24rem;
						color: #FFFFFF;
						margin-bottom: .3rem;
					}
				}

				.load {
					padding-top: .2rem;

					img {
						width: .63rem;
						height: .63rem;
						display: block;
						margin: 0 auto;
						animation: circle 2s linear infinite;
					}
				}
			}
		}

		.margin-left-0 {
			margin-left: 0;
			opacity: 1;
		}

		.margin-left-48 {
			margin-left: .24rem;
			opacity: 1;
		}
	}



	@keyframes logo1 {
		0% {
			transform: scale(.7);
		}

		50% {
			transform: scale(1);
		}

		100% {
			transform: scale(.7);
		}
	}

	@keyframes circle {
		0% {
			transform: rotate(0deg);
		}

		100% {
			transform: rotate(360deg);
		}
	}

	@keyframes title {
		0% {
			opacity: 1;
		}

		50% {
			opacity: 0;
		}

		100% {
			opacity: 1;
		}
	}

	::v-deep .uni-input-input {
		font-size: .16rem;
		font-family: PingFang SC;
		font-weight: 500;
		color: #030319;
	}
</style>