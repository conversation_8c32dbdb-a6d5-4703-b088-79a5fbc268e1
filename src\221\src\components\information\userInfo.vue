<template>
	<div class="page ">
		<top-back title="通知"></top-back>
		<div class="list-box">
			<div class="list-item flex" v-for="(item, i) in list" :key="i" @click="toMsgInfo(item)">
				<img src="../../assets/v6/ringImg.png" style="width: 0.36rem;height: 0.36rem;margin-right: 0.1rem;" alt="" />
				<div class="flex-1">
					<div class="flex flex-b">
						<div class="t">{{ item.title }}</div>
						<div class="time">{{ $formatDate('YYYY/MM/DD hh:mm:ss', new Date(item.create_time * 1000).getTime()) }}</div>
					</div>
					<div class="cot" v-html="item.content"></div>
				</div>
			</div>
			<no-data v-if="show"></no-data>
		</div>
		<first-loading ref="firstLoading"></first-loading>
	</div>
</template>

<script>
	export default {
		name: "userInfo",
		data() {
			return {
				show: false,
				list: [
					// {
					// 	title:'title',
					// 	create_time:1332,
					// 	content: "contentcontentcontent",
					// },
				],
			};
		},
		computed: {},
		components: {},
		created() {
			this.initData();
		},
		mounted() {
			this.$refs.firstLoading.open();
		},
		methods: {
			toMsgInfo(item) {
				this.$toPage(`/information/msgInfo?id=${item.id}`);
			},
			checkRead(id) {
				if (localStorage.getItem("readMsg")) {
					let read = localStorage.getItem("readMsg")
					let oldRead = JSON.parse(read)
					let hasValue = oldRead.id.includes(id.toString())
					return hasValue
				} else {
					return false
				}
			},
			initData() {
				this.$server.post("/user/notice", {
					type: "twd"
				}).then((res) => {
					this.$refs.firstLoading.close();
					if (res.status == 1) {
						this.list = res.data;
						if (!this.list.length) {
							this.show = true;
						}
						// 获取列表成功，标识已读
						//   let account = uni.getStorageSync("account");
						//   uni.setStorageSync(account, this.list.length);
					}
				});
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.6rem 0.1rem 0.1rem;
		min-height: 100vh;
	}

	.list-box {
		.list-item {
			margin: 0.1rem 0;
			background: #232429;
			border-radius: 0.19rem;
			padding: 0.12rem;
			align-items: flex-start;
			.t {
				font-family: PingFang SC;
				font-weight: 500;
				font-size: 0.14rem;
				color: #999999;
				display: flex;
				align-items: center;
			}

			.time {
				font-size: 0.12rem;
				color: #999;
			}
			.cot{
				margin-top: 0.1rem;
				font-family: PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
				color: #666666;
			}
		}
	}
</style>