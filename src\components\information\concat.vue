<template>
	<div class="page ">
		<top-back :title="$t('線上契約')"></top-back>
		<img :src="showSigImg" class="showImg" v-if="userInfo.signature_status==1 || userInfo.signature_status==2" />
		<div id="send" v-else>
			<img src="../../assets/v4/con-bg1.png" class="top-bg" />
			<div class="con-mid">
				<div class="con-tit"><img src="../../assets/v4/20012.png" />{{ $t('金和順APP電子簽署') }}</div>

				<div class="con-txt">{{ $t('本人（以下稱「乙方」）確認已詳閱以下所列文件之全部內容，並充分理解各文件之條款、聲明及告知事項，特此同意簽署並承諾嚴格遵守下列文件之所有規範：') }}</div>
				<div class="con-txt">{{ $t('金和順APP服務同意書及免責聲明') }}</div>
				<div class="con-txt">{{ $t('客戶重要權益通知書') }}</div>

				<div class="con-txt">{{ $t('隱私權保護聲明書') }}</div>
				<div class="con-txt">{{ $t('個人資料保護法告知事項') }}</div>
				<div class="con-txt">{{ $t('保密措施聲明書') }}</div>
				<div class="con-txt">{{ $t('本人聲明並保證已詳細審閱、充分理解並完全接受上述文件之所有內容，並同意履行相關法律規定及義務。') }}</div>

				<div class="con-txt">提供方資訊（甲方）：</div>
				<div class="con-txt">公司名稱： 金和順投資有限公司</div>
				<div class="con-txt">公司統一編號： 93549904</div>
				<div class="con-txt">公司代表人： 呂水波</div>
				<div class="con-txt">公司資本額： 新台幣701,000,000元整</div>
				<div class="con-txt">公司類型： 有限公司</div>
				<div class="con-txt">登記機關： 經濟部商業發展署</div>
				<div class="con-txt">公司所在地： 高雄市鳳山區文龍路85號9樓</div>
				<!-- <div class="con-txt">代表人：黃茂雄<img class="img2" src="../../assets/hz.jpg" /></div> -->
				<div class="con-txt">簽名或蓋章：<div style="position: absolute;left: .8rem;top: -.3rem;"><img class="img2"
							src="../../assets/v4/hz.jpg" /><img class="img1" src="../../assets/v4/hz2.jpg" /></div>
				</div>
				<div class="con-txt">簽署人資訊（乙方）：</div>
				<!--        <div class="con-txt">簽署人姓名：{{userInfo.realname}}</div>-->
				<!--        <div class="con-txt">身份證字號：{{userInfo.id_card}}</div>-->
				<!--        <div class="con-txt">聯絡電話：{{userInfo.id_card}}</div>-->
				<!--        <div class="con-txt">電子信箱：{{userInfo.realname}}</div>-->
				<!--        <div class="con-txt">聯絡地址： {{dateInfo}}</div>-->

				<div class="con-txt">簽署日期：</div>
				<div class="con-txt">中華民國＿{{year}}＿年＿{{month}}＿月＿{{day}}＿日</div>

				<div class="con-txt">電子簽名欄位：</div>
				<div class="con-txt">簽署人（乙方）請於以下框內進行電子簽名確認：</div>

				<div v-if="sigImg" @click="boxTr">
					<div class="con-txt">
						╔══════════════════
					</div>
					<img class="signatureImg" :src="sigImg" />
					<div class="con-txt" style="margin-top: 0;">

						╚══════════════════
					</div>
				</div>
				<div v-else @click="boxTr">
					<div class="con-txt">
						╔══════════════════
					</div>
					<!-- <div class="con-txt" style="margin-top: 0;">
            ══════╗
          </div> -->
					<div class="con-txt" style="margin-top: 0;">
						║
					</div>
					<div class="con-txt" style="margin-top: 0;">
						║
					</div>
					<div class="con-txt" style="margin-top: 0;">
						║
					</div>
					<div class="con-txt" style="margin-top: 0;">
						║
					</div>
					<div class="con-txt" style="margin-top: 0;">
						║
					</div>
					<div class="con-txt" style="margin-top: 0;">

						╚══════════════════
					</div>
				</div>

				<!-- <div class="con-txt" style="margin-top: 0;">

          ══════╝
        </div> -->

				<div class="con-txt">法律效力聲明：</div>
				<div class="con-txt">本人茲聲明並確認，於本電子文件之簽署，具有與本人親自簽署實體文件完全相同之法律效力，簽署人願意承擔因簽署本文件所生之一切法律責任及相關義務。
				</div>
				<div class="con-txt">保密承諾與違約責任聲明：</div>
				<div class="con-txt">乙方於簽署本文件後，應嚴格履行以下保密義務：</div>
				<div class="con-txt">對本次合作相關之所有內容，包括但不限於合作方案、投資操作策略、操作細節及內部資訊等機密資訊，乙方承諾絕不以任何形式洩漏予第三方或公開傳播。</div>
				<div class="con-txt">若因乙方違反上述保密義務，致使甲方受到任何形式之損害或損失，乙方同意依法承擔所有責任，並無條件支付甲方相當於本次合作操作總資金21%之違約金。</div>
				<div class="con-txt">注意事項：</div>
				<div class="con-txt">乙方務必仔細確認已充分理解本電子簽署內容及相關法律責任，經確認後再進行簽署。本文件經簽署後，即具法律效力，甲乙雙方均須共同遵守本文件及其附屬各項合約之所有條款與規範。
				</div>
				<div class="con-txt">再次提醒：</div>
				<div class="con-txt">乙方務必確保簽署人本人進行電子簽署，且電子簽名與親筆簽名具有同等法律效力。</div>
			</div>
			<img src="../../assets/v4/con-bg3.png" class="top-bg" />
		</div>
		<div class="flex" style="justify-content: center;"
			v-if="userInfo.signature_status==0 || userInfo.signature_status==3">
			<div class="con-foot" @click="boxTr" style="margin-right: .1rem">{{ $t('簽名') }}</div>
			<div class="con-foot" @click="toReal">{{ $t('簽署') }}</div>
		</div>
		<div class="flex" style="justify-content: center;" v-else>
			<div class="con-foot" v-if="userInfo.signature_status==1">{{ $t('審核中') }}</div>
			<div class="con-foot" v-if="userInfo.signature_status==2">{{ $t('審核成功') }}</div>
		</div>
		<div class="signatureBox" v-show="boxShow">
			<canvas ref="canvas" @touchstart="startDrawing" @touchmove="draw" @touchend="stopDrawing"></canvas>
			<div class="btn_group">
				<div class="btn" @click="boxShow=false">{{ $t('取消') }}</div>
				<div class="btn" @click="clearCanvas">{{ $t('清除') }}</div>
				<div class="btn" @click="saveSignature">{{ $t('確認') }}</div>
			</div>
		</div>
		<loading ref="loading" />
	</div>
</template>

<script>
	import {
		compress
	} from "../../assets/js/imgutils";
	import html2canvas from "html2canvas";

	export default {
		name: "aboutUs",
		props: {},
		data() {
			return {
				userInfo: {},
				dateInfo: '',
				year: '',
				day: '',
				month: '',
				boxShow: false,

				ctx: null,
				lastTouch: null,
				paths: [],
				sigImg: '',
				upSigImg: '',
				showSigImg: '',
			};
		},
		mounted() {
			let _this = this
			_this.context = this.$refs.canvas.getContext("2d"); // 获取Canvas上下文
			_this.$refs.canvas.width = window.innerWidth; // 设置Canvas的宽度
			_this.$refs.canvas.height = window.innerHeight - 100; // 设置Canvas的高
		},
		methods: {
			boxTr() {
				let _this = this
				if (this.userInfo.is_true != 1) {
					this.$toast(this.$t('請先完成實名'));
					setTimeout(function() {
						_this.$toPage("/information/authInfo");
					}, 2000)
					return false
				}
				_this.boxShow = true
				setTimeout(function() {
					_this.context = _this.$refs.canvas.getContext("2d"); // 获取Canvas上下文
					_this.$refs.canvas.width = window.innerWidth; // 设置Canvas的宽度
					_this.$refs.canvas.height = window.innerHeight - 100; // 设置Canvas的高
				}, 500)
			},
			// 当鼠标在 Canvas 上触摸时触发
			startDrawing(event) {
				event.preventDefault();
				const touch = event.touches[0];
				this.lastTouch = {
					x: touch.clientX,
					y: touch.clientY
				};
				this.context.beginPath();
				this.context.moveTo(this.lastTouch.x, this.lastTouch.y);
				this.paths.push([]);
			},
			// 当鼠标在 Canvas 上移动时触发
			draw(event) {
				event.preventDefault();
				const touch = event.touches[0];
				const currentTouch = {
					x: touch.clientX,
					y: touch.clientY
				};
				this.context.lineTo(currentTouch.x, currentTouch.y);
				this.context.strokeStyle = '#ff0000'
				this.context.lineWidth = 5;
				this.context.stroke();
				this.lastTouch = currentTouch;
				this.paths[this.paths.length - 1].push({
					x: currentTouch.x,
					y: currentTouch.y
				});
			},
			// 当鼠标松开时触发，用于停止绘制签名
			stopDrawing(event) {
				event.preventDefault();
				this.context.closePath();
			},
			// 清除
			clearCanvas() {
				this.context.clearRect(0, 0, this.$refs.canvas.width, this.$refs.canvas.height);
				this.paths = [];
			},
			base64ToBinary(base64Str) {
				// 移除data URI的前缀
				const dataURI = base64Str.split(',')[1];
				// 将Base64编码的字符串转换为二进制数据
				const binaryString = window.atob(dataURI);
				// 将二进制字符串转换为Uint8Array
				const len = binaryString.length;
				const bytes = new Uint8Array(len);
				for (let i = 0; i < len; i++) {
					bytes[i] = binaryString.charCodeAt(i);
				}
				// 创建Blob对象
				return new Blob([bytes], {
					type: 'image/png'
				});
			},
			// 保存签名
			saveSignature() {
				let _this = this
				// _this.$refs.loading.open(); //开启加载
				this.sigImg = this.$refs.canvas.toDataURL('image/png');
				setTimeout(function() {
					html2canvas(document.getElementById('send')).then(function(canvas) {
						let imgData = canvas.toDataURL("image/png");
						let file = _this.base64ToBinary(imgData);
						compress(file, {
							maxWidth: 840
						}, (file => {
							let formdata = new FormData();
							formdata.append("card", file);
							_this.$server
								.post("/common/upload1", formdata)
								.then((res) => {
									if (res.status == 1) {
										_this.$toast(_this.$t('上傳成功'));
										_this.upSigImg = res.data
										_this.boxShow = false
										_this.$refs.loading.close();
									}
								})
								.catch((data) => {});
						}))
					});
				}, 100)
			},
			// 提交签署文档
			toReal() {
				let _this = this
				if (this.userInfo.is_true != 1) {
					this.$toast(this.$t('請先完成實名'));
					setTimeout(function() {
						_this.$toPage("/information/authInfo");
					}, 2000)
					return false
				}
				if (!this.upSigImg) {
					this.$toast(this.$t('請先完成簽名'));
					return false
				}
				// this.$refs.loading.open(); //开启加载
				this.$server
					.post("/user/userinSign", {
						signature: this.upSigImg,
					})
					.then((res) => {
						this.$refs.loading.close();
						if (res.status == 1) {
							this.$toast(this.$translateServerText(res.msg));
							this.initData()
						}
					});

			},
			initData() {
				let that = this
				this.$server.post("/user/getUserinfo", {
					type: "zar"
				}).then((res) => {
					if (res.status == 1) {
						this.userInfo = res.data;
						if (this.userInfo.signature) {
							if(this.userInfo.signature.indexOf('http') !=-1){
								that.showSigImg = res.data.signature;
							}else{
								that.showSigImg = that.$server.url.imgUrls + res.data.signature;
							}
						}
						this.dateInfo = this.timestampToTime(this.userInfo.create_time)
					}
				});
			}
		},
		created() {
			this.initData()
			let today = new Date();
			// 获取年、月、日
			let year = today.getFullYear();
			let month = today.getMonth() + 1; // 月份是从0开始的，所以需要+1
			let day = today.getDate();
			this.year = year
			this.month = month
			this.day = day
		},
		computed: {
			timestampToTime(timestamp) {
				return (timestamp) => {
					var date = new Date(timestamp * 1000); //时间戳若为10位时需*1000
					var Y = date.getFullYear() - 1911;
					var M = date.getMonth() + 1 >= 10 ? date.getMonth() + 1 : '0' + (date.getMonth() + 1);
					var D = date.getDate() >= 10 ? date.getDate() : '0' + date.getDate();
					var h = date.getHours() >= 10 ? date.getHours() : '0' + date.getHours();
					var m = date.getMinutes() >= 10 ? date.getMinutes() : '0' + date.getMinutes();
					var s = date.getSeconds() >= 10 ? date.getSeconds() : '0' + date.getSeconds();
					return `${Y}年${M}月${D}日`;
				};
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.6rem 0.1rem .2rem;
		min-height: 100vh;
	}

	.signatureBox {
		width: 100vw;
		height: 100vh;
		border-radius: .1rem;
		background: #fff;
		position: fixed;
		left: 0;
		top: 0;
		z-index: 2;
		display: flex;
		flex-direction: column;

		.signature {
			width: 100%;
			height: 300px;
			border: 1px solid #868080;
		}

		.btn_group {
			margin-top: .1rem;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 .1rem;

			.btn {
				width: 32%;
				background: #549d7e;
				border-radius: 0.04rem;
				color: #ffffff;
				padding: 0.1rem;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
	}

	.signatureImg {
		height: 1.4rem;
		margin: 0 auto;
		display: block;
	}

	.showImg {
		width: 100%;
	}

	.top-bg {
		width: 100%;
		display: block;
	}

	.con-mid {
		width: 100%;
		background: url("../../assets/v4/con-bg2.png");
		background-position: top center;
		background-size: 100%;
		background-repeat: repeat-y;
		padding: 0 .3rem;
		box-sizing: border-box;
	}

	.con-tit {
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #333;
		font-size: .18rem;
		font-weight: bold;

		img {
			position: absolute;
			left: .1rem;
			width: .5rem;
			margin-right: .05rem;
		}
	}

	.con-txt {
		color: #333;
		font-size: .13rem;
		margin-top: .08rem;
		display: flex;
		align-items: center;
		position: relative;

		.img1 {
			width: .5rem;
		}

		// .img2{
		//   width: .3rem;
		//   position: absolute;
		//   left: 1rem;
		//   right: 0;
		// }
		.img2 {
			width: .3rem;
			margin-right: .01rem;
		}
	}

	.con-foot {
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: .2rem;
		font-size: .18rem;
		font-weight: bold;
		color: #ff0000;
		border: .01rem solid #ff0000;
		border-radius: .05rem;
		padding: .05rem .3rem;
	}

	.bottomposi {

		.textitem {
			font-size: .1rem;
			color: #000;
			font-weight: bold;
			position: absolute;
			bottom: 1.77rem;
			right: .2rem;
		}

		.textitem1 {
			position: absolute;
			bottom: 1.62rem;
			right: .3rem;
		}

		.textitem2 {
			position: absolute;
			bottom: 1.45rem;
			right: .8rem;
		}

		.textitem3 {
			position: absolute;
			bottom: 1.3rem;
			right: .7rem;
		}
	}
</style>