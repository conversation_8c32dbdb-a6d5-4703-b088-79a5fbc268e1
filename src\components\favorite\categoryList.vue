<template>
	<!-- 市场更多 -->
	<div class="page ">
		<top-back title="產業"></top-back>
		<!-- 切換 -->
		<!-- <div class="change flex flex-b">
			<div class="change-item" v-for="(item, index) in sort" :class="{ active: item.id === sortIndex }"
				:key="index" @click="changeSort(item.id)">
				{{ item.name }}
			</div>
		</div> -->
		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<van-skeleton title :row="26" :loading="loading">
				<div class="tt">產業報告</div>
				<div class="cy-list flex flex-b">
					<div class="cy-item" :class="item.change.sort > 0 ? 'red-border' : 'green-border'"
						v-for="(item, i) in categoryList" :key="i" @click="$toPage(`/favorite/moreListCate?mType=${item.exchange}&sectorId=${item.sectorId}&mName=${item.sectorName}`)">
						<div class="name">{{ item.sectorName || "-" }}</div>
						<!-- <div>{{ item.symbolName }}</div> -->
						<div class="price" :class="item.change.sort > 0 ? 'red' : 'green'">
							{{ $formatMoney(item.price.sort) || "-" }}
						</div>
						<div class="per flex flex-c" :class="item.change.sort > 0 ? 'red' : 'green'">
							<div class="icon" :class="item.change.sort > 0 ? 'up1' : 'down1'"></div>
							<!-- {{ $formatMoney(item.change.sort) }} -->
							{{ item.changePercent || "-" }}
						</div>
					</div>
				</div>
			</van-skeleton>
		</van-pull-refresh>

		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "moreList",
		props: {},
		data() {
			return {
				categoryList: [],
				show: true,
				show1: true,
				currmentIndex: 1,
				loading: true,
				loading1: true,
				isShow: false,
				isLoading: false,
				sort: [{
						name: "漲幅榜",
						id: 0
					},
					{
						name: "跌幅榜",
						id: 1
					},
					{
						name: "成交額",
						id: 2
					},
					// { name: "創高榜", id: 3 },
					// { name: "創低榜", id: 4 },
				],
				sortIndex: 0,
				list: [],
				type: "zhangfb",
			};
		},
		computed: {},
		created() {},
		mounted() {
			this.getCategory();
		},
		methods: {
			changeSort(type) {
				this.$refs.loading.open();
				this.sortIndex = type;
				this.list = [];
				if (type == 0) {
					this.type = "zhangfb";
				} else if (type == 1) {
					this.type = "diefb";
				} else {
					this.type = "chengje";
				}
				this.getList();
			},
			// 走的排行榜數據
			getCategory() {
				this.$server
					.post("/parameter/category", {
						category: "TAI",
						type:'zar'
					})
					.then((res) => {
						if (res.status === 1) {
							res.data.forEach((item, index) => {
								this.$server
									.post("/parameter/stockservices", {
										category: "TAI",
										sectorId: item.sectorId,
										offset: 1,
									})
									.then((ras) => {
										ras.list.forEach((item2, index2) => {
											if (index2 < 1) {
												this.categoryList.push(item2);
											}
											this.loading = false;
											this.isLoading = false
										});
									});
							});
						}
					});
			},
			onRefresh() {
				this.isShow = false;
				this.getCategory();
			},
			changeList(type) {
				if (type == 0) {
					this.show = !this.show;

					// 成交額 排序
					if (this.show) {
						this.list = this.list.sort(
							(a, b) => Number(b.volume) - Number(a.volume)
						);
					} else {
						this.list = this.list.sort(
							(a, b) => Number(a.volume) - Number(b.volume)
						);
					}
				} else {
					this.show1 = !this.show1;

					// 漲跌排序
					if (this.show1) {
						this.list = this.list.sort(
							(a, b) => Number(b.gainValue) - Number(a.gainValue)
						);
					} else {
						this.list = this.list.sort(
							(a, b) => Number(a.gainValue) - Number(b.gainValue)
						);
					}
				}
			},
		},
	};
</script>

<style scoped lang="less">
	.change {
		padding: 0.1rem;
		width: 100%;
		position: fixed;
		top: 0.5rem;
		left: 0;
		z-index: 999;
		background-color: #fff;

		.change-item {
			background: #8e8e8e;
			border-radius: 0.3rem;
			font-size: 0.12rem;
			color: #ffffff;
			padding: 0.05rem 0;
			width: 30%;
			text-align: center;

			&.active {
				background: linear-gradient(124deg, #77ba90 0%, #7dbab4 100%);
			}
		}
	}

	.tt {
		font-weight: 500;
		font-size: 0.14rem;
		color: #fff;
		margin: 0.1rem 0;
	}

	.cy-list {
		padding: 0.1rem 0;
		flex-wrap: wrap;

		.cy-item {
			width: 32%;
			text-align: center;
			background: #FFFFFF;
			border-radius: 0.1rem;
			margin-bottom: 0.1rem;
			padding: 0.1rem 0;

			&.red-border {
				border-color: #cf2829;
			}

			&.green-border {
				border-color: #52985d;
			}

			.name {
				font-size: 0.12rem;
				color: #000;
			}

			.price {
				font-size: 0.16rem;
				padding: 0.1rem 0;
			}

			.icon {
				margin-right: 0.05rem;
			}

			.per {
				font-size: 0.12rem;
			}
		}
	}

	.red {
		color: #ba3b3a;
	}

	.green {
		color: #39B44C;
	}

	.page {
		padding: 0.5rem 0.12rem 0.6rem;
		min-height: 100vh;
	}

	.index {
		.t {
			font-weight: 500;
			color: #1e1e1e;
			padding: 0 0.1rem 0.1rem;
		}
	}

	.title {
		padding: 0 0.1rem;

		div {
			font-weight: 600;
			font-size: 0.16rem;
			color: #000000;
		}
	}
</style>