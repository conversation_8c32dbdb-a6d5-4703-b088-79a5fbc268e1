<template>
	<!-- 市场更多 -->
	<div class="pages ">
		<!-- 切換 -->
		<!-- <div class="tit flex flex-b">
			<div class="tt">熱門名單</div>
			<div class="flex" @click="$toPage('/favorite/moreList')">
				<div class="more">更多</div>
				<div class="icon jt"></div>
			</div>
		</div> -->
		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3" :loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<div class="change flex">
				<div class="change-item" v-for="(item, index) in sort" :class="{ active: item.id === sortIndex }":key="index" @click="changeSort(item.id)">
					{{ item.name }}
				</div>
			</div>
			<van-skeleton title :row="26" :loading="loading">
				<div class="rm">
					<div class="icon jt" @click="$toPage('/favorite/moreList')"></div>
					<div class="titles flex flex-b" v-if="false">
						<div class="flex-1">名稱</div>
						<div class="flex-1 t-c">價格</div>
						<!-- <div class="flex-1 t-c flex flex-c">
							成交量
							<div class="icon" :class="show ? 'zq' : 'dq'" @click="changeList(0)"></div>
						</div> -->
						<div class="flex-1 t-c flex flex-c">
							漲跌
							<div class="icon" :class="show1 ? 'zq' : 'dq'" @click="changeList(1)"></div>
						</div>
						<div class="flex-1 t-r flex flex-e">
							漲幅
							<div class="icon" :class="show1 ? 'zq' : 'dq'" @click="changeList(1)"></div>
						</div>
					</div>
					<!-- 列表 -->
					<div class="rm-list flex">
						<div class="rm-item flex-column-item" v-for="(item, i) in list" :key="i"@click=" $toDetail(`/market/stockDetail?symbol=${item.symbol}`, item)" v-if="i<6">
							<div>
								<div class="name">{{ item.local_name }}</div>
								<!-- <div class="code">{{ item.symbol }}</div> -->
							</div>
							<div class="price" :class="item.gain > 0 ? 'red' : 'green'">{{ $formatMoney(item.price) }}</div>
							<!-- <div class="t" :class="item.gain > 0 ? 'red' : 'green'">總{{ $formatMoney(item.volume / 1000000) }} M</div> -->
							<div class="flex">
								<div class="icon animate__animated animate__fadeIn" style="width: 0.1rem;height: 0.08rem;" :class="item.gain > 0 ? 'up1' : 'down1'"></div>
								<div class="per" :class="item.gain > 0 ? 'red' : 'green'">{{ $formatMoney(item.gainValue) }}({{ item.gain }}%)
								</div>
							</div>
							<!-- <div class="line" :id="'chart'+item.symbol"></div> -->
						</div>
					</div>
				</div>
			</van-skeleton>
		</van-pull-refresh>

		<loading ref="loading" />
	</div>
</template>

<script>
	import {
		init,
		dispose,
		extension
	} from "klinecharts";
	export default {
		name: "moreList",
		props: {},
		data() {
			return {
				show: true,
				show1: true,
				currmentIndex: 1,

				loading: true,
				loading1: true,
				isShow: false,
				isLoading: false,

				sort: [{
						name: "漲幅榜",
						id: 1
					},
					{
						name: "跌幅榜",
						id: 0
					},
					{
						name: "成交額",
						id: 2
					},
					// { name: "創高榜", id: 3 },
					// { name: "創低榜", id: 4 },
				],
				sortIndex: 0,
				list: [],
				type: "zhangfb",
				chartDom: ''
			};
		},
		computed: {
			filterName() {
				return (value) => {
					value = value.replace("股價", "");
					let indx = value.indexOf("指數");
					return value.slice(indx - 2, indx + 2);
				};
			},
		},
		created() {
			this.getList();
		},
		mounted() {},
		methods: {
			getKdata(item) {
				let chartDom = document.getElementById("chart" + item.symbol);
				this.$server.post("/trade/kline", {
					symbol: item.symbol,
					type: 'zar',
					kline_type: 1,
				}).then((res) => {
					let resArr = []
					resArr = res.data.splice(res.data.length - 50, res.data.length - 1)

					let xdata = []
					for (let i = 0; i < resArr.length; i++) {
						if (resArr[i].time) {
							xdata.push(resArr[i].time)
						}
					}

					xdata = xdata.map(item => (isNaN(item) ? null : item));

					let sdata = []
					for (let i = 0; i < resArr.length; i++) {
						if (resArr[i].close) {
							sdata.push(resArr[i].close)
						}
					}

					const max = Math.max(...sdata);

					// 找出最小值
					const min = Math.min(...sdata);

					sdata = sdata.map(item => (isNaN(item) ? null : item));
					var myChart = echarts.init(chartDom);
					let areaStyle = []
					if (Number(item.gain) >= 0) {
						areaStyle = [{
								offset: 0,
								color: 'rgba(235, 51, 59, 0.3)'
							}, // 0% 处的颜色（透明）
							{
								offset: 1,
								color: 'rgba(220, 0, 35, .1)'
							} // 100% 处的颜色（不透明）
						]
					} else {
						areaStyle = [{
								offset: 0,
								color: 'rgba(26, 174, 82, 0.3)'
							}, // 0% 处的颜色（透明）
							{
								offset: 1,
								color: 'rgba(26, 174, 82, 0.1)'
							} // 100% 处的颜色（不透明）
						]
					}
					var option = {
						grid: {
							top: '80%'
						},
						tooltip: {
							show: false // 不显示提示框
						},
						legend: {
							show: false // 不显示图例
						},
						xAxis: {
							show: false,
							type: 'category',
							data: xdata
						},
						yAxis: {
							scale: true,
							show: false,
							type: 'value'
						},
						series: [{
							data: sdata,
							type: 'line',
							symbol: 'none',
							smooth: true,
							itemStyle: {
								normal: {
									lineStyle: {
										color: Number(item.gain) >= 0 ? '#eb333b' : '#1aae52'
									}
								}
							},
							areaStyle: {
								color: new echarts.graphic.LinearGradient(
									0, 0, 0, 1, // 渐变的起点与终点
									areaStyle
								)
							}
						}]
					};

					myChart.setOption(option);
				})
			},
			changeSort(type) {
				let that = this
				const chartContainers = document.querySelectorAll('.line'); // 假设你的图表容器有类名 'echarts'

				chartContainers.forEach(container => {
					const chartInstance = echarts.getInstanceByDom(container); // 获取图表实例
					if (chartInstance) {
						chartInstance.dispose(); // 销毁图表实例
					}
				});
				this.sortIndex = type;
				// if (type == 2) {
				// 	this.show = !this.show;
				// 	// 成交額 排序
				// 	if (this.show) {
				// 		this.list = this.list.sort(
				// 			(a, b) => Number(b.volume) - Number(a.volume)
				// 		);
				// 	} else {
				// 		this.list = this.list.sort(
				// 			(a, b) => Number(a.volume) - Number(b.volume)
				// 		);
				// 	}
				// } else if (type == 1) {
				// 	this.list = this.list.sort(
				// 		(a, b) => Number(b.gainValue) - Number(a.gainValue)
				// 	);
				// } else {
				// 	this.list = this.list.sort(
				// 		(a, b) => Number(a.gainValue) - Number(b.gainValue)
				// 	);
				// }

				// for (let i = 0; i < this.list.length; i++) {
				// 	if (i < 6) {
				// 		that.$nextTick(() => {
				// 			that.getKdata(that.list[i])
				// 		})
				// 	}
				// }
				this.getList()
			},
			// 走的排行榜數據
			getList() {
				let that = this
				let obj = {
					type:'zar'
				}
				if(this.sortIndex==1){
					obj.leixing='gain'
				}
				if(this.sortIndex==0){
					obj.leixing='gain2'
				}
				this.$server
					.post("/parameter/top", obj)
					.then((res) => {
						this.$refs.loading && this.$refs.loading.close();
						this.loading = false;
						this.isLoading = false;
						// console.log("res", res);
						this.list = res.data;
						if(this.sortIndex!=2){
							this.list = this.list.sort((a,b)=>{
								return b.gain-a.gain
							})
						}else{
							this.list = this.list.sort((a,b)=>{
								return b.volume-a.volume
							})
						}
						// for (let i = 0; i < this.list.length; i++) {
						// 	if (i < 6) {
						// 		that.$nextTick(() => {
						// 			that.getKdata(that.list[i])
						// 		})
						// 	}
						// }
					});
			},
			onRefresh() {
				this.isShow = false;
				this.getList();
			},
			changeList(type) {
				if (type == 0) {
					this.show = !this.show;

					// 成交額 排序
					if (this.show) {
						this.list = this.list.sort(
							(a, b) => Number(b.volume) - Number(a.volume)
						);
					} else {
						this.list = this.list.sort(
							(a, b) => Number(a.volume) - Number(b.volume)
						);
					}
				} else {
					this.show1 = !this.show1;

					// 漲跌排序
					if (this.show1) {
						this.list = this.list.sort(
							(a, b) => Number(b.gainValue) - Number(a.gainValue)
						);
					} else {
						this.list = this.list.sort(
							(a, b) => Number(a.gainValue) - Number(b.gainValue)
						);
					}
				}
			},
		},
	};
</script>

<style scoped lang="less">
	::v-deep .van-skeleton__row {
		background-color: transparent !important;
	}

	::v-deep .van-skeleton__title {
		background-color: transparent !important;
	}
	.pages {
		padding: 0 0.12rem;
		.tit{
			padding: 0.12rem 0;
			position: relative;
			z-index: 888;
			.tt{
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 0.16rem;
				color: #152F4C;
			}
			.more{
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.12rem;
				color: #999999;
			}
		}
	}
	.change {
		width: 100%;
		.change-item {
			height: 0.38rem;
			padding: 0 0.15rem;
			background: #151C2C;
			border-radius: 0.09rem 0.09rem 0rem 0rem;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 0.15rem;
			color: #FFFFFF;
			line-height: 0.38rem;
			color: #666666;
			text-align: center;
			position: relative;
			// &::after {
			// 	content: "";
			// 	width: 100%;
			// 	height: 0.01rem;
			// 	position: absolute;
			// 	bottom: 0;
			// 	left: 50%;
			// 	transform: translateX(-50%);
			// 	background: transparent;
			// }
			&.active {
				background: #1F2A40;
				font-weight: 500;
				color: #fff;

				&::after {
					background: #DC0023;
				}
			}
		}
	}
	.titles {
		padding: 0.1rem 0;
		div {
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 0.1rem;
			color: #999999;
		}
		.icon {
			margin-left: 0.05rem;
		}
	}
	.rm {
		background: #1F2A40;
		border-radius: 0rem 0.09rem 0.09rem 0.09rem;
		position: relative;
		.jt{
			position: absolute;
			top: 50%;
			transform: translateY(-50%);
			right: 0.12rem;
		}
		.rm-list {
			overflow-x: scroll;
			scrollbar-width: none;
			-ms-overflow-style: none;
			padding: 0.2rem 0;
			.rm-item {
				padding: 0 0.13rem;
				border-right: 0.01rem solid #30374A;
				.name {
					white-space: nowrap;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.16rem;
					color: #FFFFFF;
				}
				.code {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.17rem;
					color: #666666;
					margin-top: 0.05rem;
				}
				.price {
					padding: 0.1rem 0;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.17rem;
				}

				.per {
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.13rem;
					.icon {
						margin-right: 0.05rem;
					}
				}
			}

			.line {
				width: 1.4rem;
				height: 0.9rem;
				background: url('../../assets/v3/line.png') no-repeat center/100%;
				// background: #181C2B;
			}
		}
	}
</style>