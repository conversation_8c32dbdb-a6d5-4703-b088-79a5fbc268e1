 <template>
	<div>

		<div class="hesadf">
			<top :title='$t(text)'></top>
		</div>

		<div class="head">
			<div class="thj">
				<a class="van-tab__text--ellipsis" :class="{xuan:xnum==1}" @click="choose(1)">{{$t("资金明细")}}</a>
				<a class="van-tab__text--ellipsis" :class="{xuan:xnum==2}" @click="choose(2)">{{$t("充值记录")}}</a>
				<a class="van-tab__text--ellipsis" :class="{xuan:xnum==3}" @click="choose(3)">{{$t("提现记录")}}</a>

				<span class="tiaokl" ref='hnm'></span>
			</div>
		</div>

		<van-list
		  v-model="loading"
		    :finished="finished"
			loading-text='loading...'
		    @load="onLoad">

		<ul class="czjl">
			<li class="czli" :class="{lizj:xnum==1}" v-for="(item,i) in lisdata" :key='i'>
				<div v-show="xnum!=1">
					<h6>
						{{$t("订单号")}}：{{item.order_number}}
					</h6>
					<h5>{{item.money}}</h5>
					<p class="riqi">{{item.create_time}}</p>
					<p class="status" :class="{wanc:item.status!=0}">
						<span class="jishi" :class="{ceng:item.status!=0}"></span>
						{{$t(item.stat)}}
					</p>
				</div>
				<div class="title" v-show="xnum==1">
					<p>
						{{item.detailed}}
					</p>
					<h3>
						<span>{{item.create_time}}</span>
						<a>{{item.money}}</a>
					</h3>
				</div>
			</li>
		</ul>



		</van-list>


	</div>
</template>
<script type="text/javascript">
import Vue from 'vue';
import qs from 'qs';
import axios from 'axios';
import { Toast  } from 'vant';
Vue.use(Toast );
import { List } from 'vant';

Vue.use(List);
import top from '../bar/toper.vue'
export default {
	name:"FundingDetails",
	data(){
		return {
			xnum:1,
			chuan:this.$route.query.num,
			page:0,
			size:10,
			lisdata:[],
			loading: false,
			finished: false,
			text:'资金明细'
		}
	},

	components:{
		top,
	},
	methods:{
		choose(e){
			this.xnum=e;
			this.page=1;
			this.$refs.hnm.style.left=17+(33*(e-1))+"%";
			this.lisdata=[];
			this.getList()
		},
		onLoad() {

				this.page++;

		       // 数据全部加载完成
		      this.getList();
			this.loading =true ;
		   },
		fanhui(){
			this.$router.go(-1)
		},
		getList(){
			var datas = qs.stringify({
				type:this.xnum,
				page:this.page,
				size:this.size,
			});
			this.$server.post('/user/moneyLog',datas)
			.then(str=>{
						this.loading =false ;
						this.finished = true;
						if(str.data.status==1){

							str.data.data.list.forEach((item)=>{
								this.lisdata.push(item)
							})
							this.loading =false ;
							if(this.lisdata.length>=str.data.data.total){
								this.finished = true;
							}else{
								this.finished = false;
							}


						}else{
							Toast({
								message:this.$t(str.data.msg),
								duration:2000
							});
						}

			})
		},

	},
	destroyed() {

	},
	mounted(){
		if(this.chuan){
			this.xnum=this.chuan;
			this.$refs.hnm.style.left=17+(33*(e-1))+"%";
		}


	},
}
</script>
<style type="text/css" lang="less" scoped="scoped">
.czjl{
	width:3.55rem;
	margin:0 auto;
	margin-top:0.06rem;
	.czli{
		width:3.55rem;
		padding-bottom:0.1rem;
		border-bottom:0.01rem solid #ECECEC;
		position: relative;
		margin-bottom:0.1rem;
		&.lizj{
			margin:0 auto;
			margin-bottom:0.2rem;
			width:100%;

		.title{
			width:3.4rem;
			margin:0 auto;
			font-weight: 500;
			p{
				font-size:0.16rem;
				color: #666666;
				line-height: 0.2rem;
				padding-top:0.1rem;
			}
			h3{
				width:100%;
				display: flex;
				justify-content: space-between;
				margin-top:0.08rem;
				font-weight: 500;
				a{
					font-size:0.18rem;
					color:#333;

				}
				span{
					font-size: 0.12rem;
					color: #999;

				}
			}

		}
	}
		h5{
			font-size: 0.15rem;
			font-family: PingFang SC;
			font-weight: 500;
			color: #000;
			margin-top:0.1rem;
			margin-left:0.1rem;
		}
		.riqi{
			margin-top:0.11rem;
			color:#666;
			font-size:0.1rem;
			margin-left:0.1rem;
		}
		.status{
			position: absolute;
			right:0.14rem;
			top:0.3rem;
			font-size:0.14rem;
			color:#D73D3D;
			font-weight: 550;
			&.wanc{
				color: #0072CA;
			}
			// span{
			// 	display: inline-block;
			// 	background: url(../../assets/images/shij.png)no-repeat center;
			// 	background-size:100%;
			// 	width:0.16rem;
			// 	height:0.16rem;
			// 	vertical-align: middle;
			// 	margin-top:-0.02rem;
			// }
			.jishi{
				display: inline-block;
				background: url(../../assets/img/user/shj.png)no-repeat center;
				background-size:100%;
				width:0.18rem;
				height:0.18rem;
				vertical-align: middle;
				margin-top:-0.02rem;
				&.ceng{
					background: url(../../assets/img/user/wcv.png)no-repeat center;
					background-size:100%;
				}
			}
		}
		h6{
			display: flex;
			font-weight: 500;
			height:0.2rem;
			font-size:0.12rem;
			color: #666666;
			line-height: 0.2rem;
			padding-left:0.1rem;
			padding-top:0.05rem;

		}
	}
}
.head{
	height:0.42rem;
	background: #fff;
	border-bottom:0.01rem solid #E0E0E0;
	.thj{
		width:3.5rem;
		height:0.42rem;
		margin:0 auto;
		display: flex;
		justify-content: space-between;
		position: relative;
		a{
			width:33.3%;
			text-align: center;
			color: #666;
			line-height: 0.42rem;
			font-size:0.13rem;
			transition: all 0.3s;
			&.xuan{
				color:#D73D3D;

			}
		}
		.tiaokl{
			width: 0.2rem;
			height: 0.04rem;
			background: #D73D3D;
			border-radius: 0.02rem;
			position: absolute;
			bottom:0.05rem;
			left:17%;
			margin-left:-0.1rem;
			transition: all 0.5s;
		}
	}
}
.hesadf{
	height:0.44rem;
	background: linear-gradient(233deg, #f36218, #f59934);
}
</style>
