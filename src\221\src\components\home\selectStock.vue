<template>
	<div class="page">
		<top-menu></top-menu>
		<div class="cy">
			<div class="title">上市</div>
			<div class="cy-list flex">
				<div class="cy-item" v-for="(item, i) in categoryList" @click="  $toPage( `/favorite/moreListCate?mType=${item.exchange}&sectorId=${item.sectorId}&mName=${item.sectorName}` )" :key="i">
					<div class="name">{{ item.sectorName || "-" }}</div>
					<div class="t">{{item.proList}}</div>
				</div>
			</div>
			<div class="title">上櫃</div>
			<div class="cy-list flex flex-b">
				<div class="cy-item" v-for="(item, i) in categoryList2" @click="  $toPage( `/favorite/moreListCate?mType=${item.exchange}&sectorId=${item.sectorId}&mName=${item.sectorName}` )" :key="i">
					<div class="name">{{ item.sectorName || "-" }}</div>
					<div class="t">{{item.proList}}</div>
				</div>
			</div>
		</div>
		<tab-bar :current="4"></tab-bar>
	</div>
</template>

<script>
	export default {
		data() {
			return {
				categoryList: [],
				categoryList2: [],
				cyTabIndex: 0,
				cyTab: [{
					name: '上市',
					type: 'TAI'
				}, {
					name: '上櫃',
					type: 'TWO'
				}],
			}
		},
		created() {
			this.getCategory();
			this.getCategory2();
		},
		mounted() {},
		methods: {
			clickCyTab(index) {
				this.categoryList = []
				this.cyTabIndex = index
				this.getCategory(this.cyTab[this.cyTabIndex].type)
			},
			getCategory() {
				this.$server
					.post("/parameter/category", {
						category: 'TAI'
					})
					.then((res) => {
						if (res.status === 1) {
							res.data.forEach((item, index) => {
								this.$server
									.post("/parameter/stockservices", {
										category: 'TAI',
										sectorId: item.sectorId,
										offset: 1,
									})
									.then((ras) => {
										ras.list.forEach((item2, index2) => {
											this.$set(item2, 'proList', ras.list.length)
											if (index2 < 1) {
												this.categoryList.push(item2);
											}
										});
									});
							});
						}
					});
			},
			getCategory2() {
				this.$server
					.post("/parameter/category", {
						category: 'TWO'
					})
					.then((res) => {
						if (res.status === 1) {
							res.data.forEach((item, index) => {
								this.$server
									.post("/parameter/stockservices", {
										category: 'TWO',
										sectorId: item.sectorId,
										offset: 1,
									})
									.then((ras) => {
										ras.list.forEach((item2, index2) => {
											this.$set(item2, 'proList', ras.list.length)
											if (index2 < 1) {
												this.categoryList2.push(item2);
											}
										});
									});
							});
						}
					});
			},
		}
	}
</script>

<style lang="less" scoped>
	.page {
		padding: 0.5rem 0.12rem;
	}

	.cy {
		margin: .1rem 0 0;
		position: relative;

		.tt {
			padding: .1rem 0;

			.line {
				margin-right: 0.1rem;
				width: 0.05rem;
				height: 0.22rem;
				background: #DE9F2E;
			}

			.t {
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 0.16rem;
				color: #E1E5ED;
			}

			.t1 {
				font-size: 0.12rem;
				color: #999;
			}
		}

		.cyTab {
			position: absolute;
			left: 50%;
			top: 0.15rem;
			transform: translateX(-50%);
			width: 140px;
			height: 0.34rem;
			background: #71767F;
			border-radius: 0.02rem;
			display: flex;
			align-content: center;
			justify-content: center;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 0.16rem;
			color: #fff;

			.cyTabItemd {
				width: 70px;
				background: #0D6EFE;
				display: flex;
				align-items: center;
				justify-content: center;
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: .15rem;
				color: #fff;
			}

			.cyTabItem {
				width: 70px;
				display: flex;
				align-content: center;
				justify-content: center;
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: .15rem;
				color: #E1E5ED;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}

		.title {
			padding: 0.05rem 0;
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			font-size: 0.14rem;
			color: #FFFFFF;
		}

		.cy-list {
			padding: 0.1rem 0;
			flex-wrap: wrap;

			.cy-item {
				margin-right: 0.035rem;
				width: 24%;
				text-align: center;
				height: 0.44rem;
				background: #181C2B;
				border-radius: 0.04rem;
				border: 0.01rem solid #2D313F;
				margin-bottom: 0.12rem;
				padding: 0.1rem 0;
				position: relative;

				&::before {
					position: absolute;
					border-radius: 0.04rem 0 0 0;
					content: '';
					clip-path: polygon(0% 0%, 100% 0%, 0% 100%, 0% 100%);
					width: 0.1rem;
					height: 0.1rem;
					background: #E1E5ED;
					left: 0;
					top: 0;
				}

				.name {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.14rem;
					color: #CDD2D5;
				}

				.t {
					position: absolute;
					right: 0;
					top: -0.06rem;
					background: #C9132D;
					border-radius: 50%;
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.11rem;
					color: #FFFFFF;
					display: flex;
					align-items: center;
					justify-content: center;
					width: .17rem;
					height: .17rem;
				}

				.price {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 0.16rem;
					padding: 0.05rem 0;
				}

				.icon {
					margin-right: 0.05rem;
				}

				.per {
					font-size: 0.12rem;
				}
			}
		}
	}
</style>