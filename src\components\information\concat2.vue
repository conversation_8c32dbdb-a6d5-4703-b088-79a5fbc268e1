<template>
	<div class="page ">
		<top-back title="證券交易委託書簽署"></top-back>
    <img :src="showSigImg" class="showImg" v-if="userInfo.treaty_status==1 || userInfo.treaty_status==2" />
		<div class="con-mid" id="send" v-else>
      <div class="con-mid-box">
        <div class="con-mid-left">公司簡介</div>
        <div class="con-mid-right">
          金和順投資有限公司
          <br/>
          公司名稱：金和順投資有限公司
          <br/>
          統一編號：93549904
          <br/>
          代表人：呂水波
          <br/>
          公司所在地：高雄市鳳山區文龍路85號9樓
          <br/>
          公司狀況：核准設立
          <br/>
          資本總額：NT$ 701,000,000
          <br/>
          登記機關：經濟部商業發展署
          <br/>
          核准設立日期：112年12月12日
          <br/>
          最後核准變更日期：113年05月21日
          <br/>
          類型：有限公司
          <br/>
          所營事業資料：H201010 一般投資業
        </div>
      </div>
      <div class="con-mid-box">
        <div class="con-mid-left">風險管理</div>
        <div class="con-mid-right">
          一、合作目的｜Purpose of Cooperation
          <br/>
          本協議旨在基於雙方的信任與合作，授權乙方（星聯雲）於合法市場內執行量化投資操作，進行資金運作及資產配置。乙方將依甲方的授權，在預定範圍內進行資金調度與交易操作，並根據風險控制原則執行相關交易策略。
          <br/>
          二、操作授權｜Authorization Scope
          <br/>
          1.證券交易甲方乙方執行：甲方授權乙方執行證券買賣、資金儲值、轉帳等操作，範圍包括但不限於股票、ETF、期貨等交易項目。
          <br/>
          2.資金存取操作甲方乙方乙方甲方：甲方同意乙方執行資金的存入、提取、轉帳等操作，並授權乙方依甲方要求處理相應資金。
          <br/>
          3.證券交易指令甲方同意依其提供的指：甲方同意乙方依其提供的指令執行證券交易，無需每次單獨確認，且甲方已充分理解市場風險。
          <br/>
          三、交易方式與風控｜Execution & Risk Control
          <br/>
          1.交易方式可透過合法管道（包括電子、語音、書面等）進行。
          <br/>
          2.乙方將依據風險控制原則調整交易內容，並依據市場風險管理原則進行調整與操作。
          <br/>
          3.乙方會定期向甲方報告交易成果，並向甲方提供風險控制措施的詳細報告。
          <br/>
          四、資金交割與違約｜Settlement & Default
          <br/>
          1.甲方須依約定完成資金交割；若甲方未依約交割，乙方可依法處理並追索損失。
          <br/>
          2.若乙方未依約履行操作責任，將承擔因此所產生的損失，並負有相應賠償責任。
          <br/>
          五、收益分配與公益提撥｜Profit Allocation & Charity
          <br/>
          1.每筆交易完成後，系統將自動收取操作獲利總金額之5%作為服務費，專用於團隊績效、系統升級及慈善公益，回饋社會。
          <br/>
          2.乙方應提供捐款證明及交易報告，確保捐贈透明且合規。
          <br/>
          六、保密條款｜Confidentiality
          <br/>
          1.甲方不得將乙方提供的交易策略、技術資料等商業機密洩露給第三方，乙方亦不得向任何無關方洩露甲方的財務資訊及交易資料，除非經雙方書面同意或法律要求。
          <br/>
          2.雙方需保護對方的個人資料與業務機密。
          <br/>
          七、資料使用聲明｜Data Use Declaration
          <br/>
          乙方依《個人資料保護法》收集並使用甲方的個人資料，僅限於本協議所述的投資操作及風險管理需求。乙方將依相關法律保護甲方的資料，並保證在必要範圍內使用該資料。
          <br/>
          八、爭議處理與準據法｜Dispute & Governing Law
          <br/>
          1.本協議適用中華民國法律，若發生爭議，雙方應先協商解決，若無法解決，則由台北地方法院管轄。
          <br/>
          2.本協議的解釋與適用應依中華民國相關法律規範。
          <br/>
        </div>
      </div>
      <div class="con-mid-box">
        <div class="con-mid-left">授權內容</div>
        <div class="con-mid-right">
          2.1 授權證券機構執行交易
          <br/>
          客戶授權金和順投資有限公司代為執行所有授權範圍內的證券買賣、資金儲值、轉帳等操作。
          <br/>
          客戶同意證券機構進行各項交易，包括但不限於股票、ETF、期貨等。
          <br/>
          2.2 資金存取操作授權
          <br/>
          客戶同意證券機構進行資金的存入、提取、轉帳等操作，並授權證券機構根據客戶要求處理相應資金。
          <br/>
          2.3 證券交易指令授權
          <br/>
          客戶同意證券機構按照其提供的指令進行證券交易，無需每次單獨確認。
          <br/>
          客戶授權機構根據其指示執行交易，並明確證明對市場風險的理解。
        </div>
      </div>
      <div class="con-mid-box">
        <div class="con-mid-left">立約人同意/<br/>停止共同行<br/>銷資料特別<br/>約定條款</div>
        <div class="con-mid-right">
          1. 定義與範圍
          <br/>
          共同行銷資料：指由金和順投資有限公司（以下簡稱「證券機構」）與合作夥伴、其他金融機構、營銷平台或第三方所共同擁有或提供的，並用於促銷、廣告或市場推廣的任何資料、信息、優惠或服務。
          <br/>
          立約人：指在證券交易中簽署此同意書或協議的客戶。
          <br/>
          2. 立約人同意共同行銷資料使用
          <br/>
          立約人同意並授權證券機構使用其個人資料（包括但不限於姓名、聯絡電話、電子郵件地址及其他基本資料），用於以下目的：
          <br/>
          市場推廣：包括但不限於與證券機構合作的產品或服務的促銷活動、廣告宣傳。
          <br/>
          優惠通知：包括與證券機構、合作夥伴共同推出的優惠、折扣、贈品等推廣活動通知。
          <br/>
          市場分析：根據立約人的交易行為及資料分析，提供更符合需求的產品與服務。
          <br/>
          活動邀請：證券機構可邀請立約人參加市場推廣活動、線上或線下研討會、產品發布會等活動。
          <br/>
          3. 共同行銷資料的分享與轉交
          <br/>
          立約人同意證券機構可以將其個人資料與以下第三方合作夥伴共享或轉交：
          <br/>
          合作機構：證券機構與其他金融機構、營銷平台或商業夥伴共同進行市場推廣或行銷活動時，會將立約人的資料提供給該等合作夥伴，以進行推廣活動。
          <br/>
          第三方服務商：為了進行有效的市場分析與推廣活動，證券機構可以將立約人的資料提供給專業的數據分析服務商。
          <br/>
          4. 立約人停止共同行銷資料使用的權利
          <br/>
          立約人擁有隨時停止共同行銷資料使用的權利。若立約人希望停止共同行銷資料的使用，應按照以下方式提出要求：
          <br/>
          停止同意：立約人可隨時通知證券機構，要求停止使用其個人資料進行市場推廣活動。
          <br/>
          方式：立約人可通過電子郵件、電話、書面或在線平台等方式向證券機構提出停止使用資料的要求。
          <br/>
          5. 停止共同行銷資料使用的後果
          <br/>
          停止後的影響：一旦立約人停止同意使用其資料，證券機構將停止將其資料用於任何後續的市場推廣活動或商業推廣計劃，但已經進行的市場推廣活動不會撤回或修改。
          <br/>
          繼續交易不受影響：停止使用共同行銷資料不會影響立約人進行證券交易的正常權益，立約人仍然可以正常進行所有交易操作。
          <br/>
          6. 資料保護與隱私條款
          <br/>
          資料保護：證券機構承諾會遵守相關法律法規，對立約人的個人資料進行妥善保護，並僅限於法律允許範圍內使用。
          <br/>
          隱私政策：證券機構已提供立約人隱私政策，詳細說明如何收集、使用、存儲和保護個人資料。立約人可隨時查閱該政策。
          <br/>
          7. 爭議處理
          <br/>
          若因共同行銷資料使用引發爭議，雙方同意通過協商解決，若協商無效，將依高雄地方法院管轄。
          <br/>
          8. 同意與簽署
          <br/>
          本人，同意以上立約人同意/停止共同行銷資料使用的條款，並確認已理解如何停止共同行銷資料的使用及其後果。
          <br/>
          其他約定事項
          <br/>
          1.約定有效性
          <br/>
          本約定事項一旦由立約人簽署確認，即成為具有法律效力的合同條款，雙方應嚴格遵守。
          <br/>
          2. 不可抗力
          <br/>
          在不可抗力事件（如天災、戰爭、罷工等）發生的情況下，證券機構對立約人所產生的交易延遲或損失不承擔責任。
          <br/>
          3. 通知方式
          <br/>
          立約人同意，證券機構可通過電子郵件、電話或其他形式向其發送通知，並視為正式通知。
        </div>
      </div>
      <div class="con-mid-middle">
        <div class="con-mid-middle-item">
          <div class="con-mid-middle-title">立約人<br/>簽章</div>
          <div class="con-mid-middle-sig" @click="boxShow=true">
            <img :src="sigImg" v-if="sigImg" />
          </div>
        </div>
        <div class="con-mid-middle-item">
          <div class="con-mid-middle-title">證券機構代表人簽章</div>
          <div class="con-mid-middle-box">
            <img class="img1" src="../../assets/1.png">
            <img class="img2" src="../../assets/2.png">
          </div>
        </div>
        <div class="con-mid-middle-item">
          <div class="con-mid-middle-title">主管/ 處理人員簽章</div>
          <div class="con-mid-middle-box">
            <img class="img1" src="../../assets/3.png">
          </div>
        </div>
      </div>
      <div class="con-mid-tip">中華民國 114年01月13日</div>
      <div class="con-mid-foot">
        <div class="con-mid-foot-top">同意與簽署</div>
        <div class="con-mid-foot-box">本人，已閱讀並同意上述所有約定事項，並確認在證券交易中遵循相關條款與條件，立約人簽名/印章有效。</div>
      </div>
		</div>
    <div class="flex" style="justify-content: center;" v-if="userInfo.treaty_status==0 || userInfo.treaty_status==3">
      <div class="con-foot" @click="boxTr" style="margin-right: .1rem">簽名</div>
      <div class="con-foot" @click="toReal">簽署</div>
    </div>
    <div class="flex" style="justify-content: center;" v-else>
      <div class="con-foot" v-if="userInfo.treaty_status==1">審核中</div>
      <div class="con-foot" v-if="userInfo.treaty_status==2">審核成功</div>
    </div>
    <div class="signatureBox" v-show="boxShow">
      <canvas ref="canvas" @touchstart="startDrawing" @touchmove="draw" @touchend="stopDrawing"></canvas>
      <div class="btn_group">
        <div class="btn" @click="boxShow=false">取消</div>
        <div class="btn" @click="clearCanvas">清除</div>
        <div class="btn" @click="saveSignature">確認</div>
      </div>
    </div>
    <loading ref="loading" />
	</div>
</template>

<script>
  import html2canvas from 'html2canvas';
  import {compress} from "@/assets/js/imgutils";
  export default {
		name: "aboutUs",
		props: {},
		data() {
			return {
				userInfo: {},
				dateInfo: '',
				year:'',
				day:'',
				month:'',
        boxShow: false,

        ctx: null,
        lastTouch: null,
        paths: [],
        sigImg: '',
        upSigImg: '',
        showSigImg: ''
			};
		},
    mounted() {
      let _this = this
      _this.context = this.$refs.canvas.getContext("2d"); // 获取Canvas上下文
      _this.$refs.canvas.width = window.innerWidth; // 设置Canvas的宽度
      _this.$refs.canvas.height = window.innerHeight - 100; // 设置Canvas的高
    },
		methods: {
      boxTr(){
        let _this = this
        if(this.userInfo.is_true!=1){
          this.$toast('請先完成實名');
          setTimeout(function (){
            _this.$toPage("/information/authInfo");
          },2000)
          return false
        }
        _this.boxShow=true
        setTimeout(function (){
          _this.context = this.$refs.canvas.getContext("2d"); // 获取Canvas上下文
          _this.$refs.canvas.width = window.innerWidth; // 设置Canvas的宽度
          _this.$refs.canvas.height = window.innerHeight - 100; // 设置Canvas的高
        },500)
      },
      // 当鼠标在 Canvas 上触摸时触发
      startDrawing(event) {
        event.preventDefault();
        const touch = event.touches[0];
        this.lastTouch = { x: touch.clientX, y: touch.clientY };
        this.context.beginPath();
        this.context.moveTo(this.lastTouch.x, this.lastTouch.y);
        this.paths.push([]);
      },
      // 当鼠标在 Canvas 上移动时触发
      draw(event) {
        event.preventDefault();
        const touch = event.touches[0];
        const currentTouch = { x: touch.clientX, y: touch.clientY };
        this.context.lineTo(currentTouch.x, currentTouch.y);
        this.context.strokeStyle = '#ff0000'
        this.context.lineWidth = 5;
        this.context.stroke();
        this.lastTouch = currentTouch;
        this.paths[this.paths.length - 1].push({ x: currentTouch.x, y: currentTouch.y });
      },
      // 当鼠标松开时触发，用于停止绘制签名
      stopDrawing(event) {
        event.preventDefault();
        this.context.closePath();
      },
      // 清除
      clearCanvas() {
        this.context.clearRect(0, 0, this.$refs.canvas.width, this.$refs.canvas.height);
        this.paths = [];
      },
      base64ToBinary(base64Str) {
        // 移除data URI的前缀
        const dataURI = base64Str.split(',')[1];
        // 将Base64编码的字符串转换为二进制数据
        const binaryString = window.atob(dataURI);
        // 将二进制字符串转换为Uint8Array
        const len = binaryString.length;
        const bytes = new Uint8Array(len);
        for (let i = 0; i < len; i++) {
          bytes[i] = binaryString.charCodeAt(i);
        }
        // 创建Blob对象
        return new Blob([bytes], {type: 'image/png'});
      },
      // 保存
      saveSignature() {
        let _this = this
        _this.$refs.loading.open(); //开启加载
        this.sigImg = this.$refs.canvas.toDataURL('image/png');
        setTimeout(function (){
          html2canvas(document.getElementById('send')).then(function(canvas) {
            let imgData = canvas.toDataURL("image/png");
            let file = _this.base64ToBinary(imgData);
            compress(file,{maxWidth:840},(file=> {
              let formdata = new FormData();
              formdata.append("card", file);
              _this.$server
                  .post("/common/upload1", formdata)
                  .then((res) => {
                    if (res.status == 1) {
                      _this.$toast('上傳成功');
                      _this.upSigImg = res.data
                      _this.boxShow=false
                      _this.$refs.loading.close();
                    }
                  })
                  .catch((data) => {});
            }))
          });
        },100)
      },
      // 提交签署文档
			toReal() {
        let _this = this

        if(this.userInfo.is_true!=1){
          this.$toast('請先完成實名');
          setTimeout(function (){
            _this.$toPage("/information/authInfo");
          },2000)
          return false
        }
				if(!this.upSigImg){
          this.$toast('請先完成簽名');
          return false
        }
        this.$refs.loading.open(); //开启加载
        this.$server
            .post("/user/userinTreaty", {
              treaty: this.upSigImg,
            })
            .then((res) => {
              this.$refs.loading.close();
              if (res.status == 1) {
                this.$toast(this.$t(res.msg));
                this.initData()
              }
            });

			},
			initData() {
				this.$server.post("/user/getUserinfo", {
					type: "twd"
				}).then((res) => {
					if (res.status == 1) {
						this.userInfo = res.data;
            if(this.userInfo.treaty){
              this.showSigImg = this.$server.url.imgUrls + res.data.treaty;
            }
						this.dateInfo = this.timestampToTime(this.userInfo.create_time)
					}
				});
			}
		},
		created() {
			this.initData()
			let today = new Date();
			// 获取年、月、日
			let year = today.getFullYear();
			let month = today.getMonth() + 1; // 月份是从0开始的，所以需要+1
			let day = today.getDate();
			this.year = year
			this.month = month
			this.day = day
		},
		computed: {
			timestampToTime(timestamp) {
				return (timestamp) => {
					var date = new Date(timestamp * 1000); //时间戳若为10位时需*1000
					var Y = date.getFullYear() - 1911;
					var M = date.getMonth() + 1 >= 10 ? date.getMonth() + 1 : '0' + (date.getMonth() + 1);
					var D = date.getDate() >= 10 ? date.getDate() : '0' + date.getDate();
					var h = date.getHours() >= 10 ? date.getHours() : '0' + date.getHours();
					var m = date.getMinutes() >= 10 ? date.getMinutes() : '0' + date.getMinutes();
					var s = date.getSeconds() >= 10 ? date.getSeconds() : '0' + date.getSeconds();
					return `${Y}年${M}月${D}日`;
				};
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.6rem 0.1rem 0;
		min-height: 100vh;
	}

  .showImg{
    width: 100%;
  }
  .signatureBox{
    width: 100vw;
    height: 100vh;
    border-radius: .1rem;
    background: #fff;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 2;
    display: flex;
    flex-direction: column;
    .signature{
      width: 100%;
      height: 300px;
      border: 1px solid #868080;
    }
    .btn_group{
      margin-top: .1rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 .1rem;
      .btn{
        width: 32%;
        background: #549d7e;
        border-radius: 0.04rem;
        color: #ffffff;
        padding: 0.1rem;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
  .signatureImg{
    width: 100%;
  }

	.con-mid {
		width: 100%;
    padding-bottom: .2rem;
    background: #0D111A;
    .con-mid-box{
      border: .01rem solid #999;
      display: flex;
      margin-bottom: .12rem;
    }
    .con-mid-left{
      width: .8rem;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .con-mid-right{
      border-left: .01rem solid #999;
      box-sizing: border-box;
      width: calc(100% - .8rem);
      color: #fff;
      padding: .1rem;
    }
    .con-mid-middle{
      border: .01rem solid #999;
      display: flex;
      margin-bottom: .12rem;
      .con-mid-middle-item{
        width: calc(100% / 3);
        border-right: .01rem solid #999;
      }
      .con-mid-middle-title{
        padding: .1rem;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        border-bottom: .01rem solid #999;
      }
      .con-mid-middle-sig{
        height: 1.4rem;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: .1rem;
        box-sizing: border-box;
        img{
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }
      .con-mid-middle-tip{
        padding: .1rem;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        border-top: .01rem solid #999;
      }
      .con-mid-middle-box{
        height: 1.4rem;
        padding: .1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
        position: relative;
        .img1{
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
        .img2{
          width: .79rem;
          height: .43rem;
          position: absolute;
          bottom: 0;
          right: 0;
        }
      }
    }
    .con-mid-tip{
      padding: .1rem 0;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }
    .con-mid-foot{
      border: .01rem solid #999;
      margin-bottom: .12rem;
      .con-mid-foot-top{
        padding: .1rem;
        border-bottom: .01rem solid #999;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .con-mid-foot-box{
        padding: .1rem;
        color: #fff;
      }
    }
	}
  .con-foot {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: .2rem;
    font-size: .18rem;
    font-weight: bold;
    color: #ff0000;
    border: .01rem solid #ff0000;
    border-radius: .05rem;
    padding: .05rem .3rem;
  }
</style>