<template>
	<!-- @click="$sharesDetails2(currentItem)" -->
	<div>
		<div ref="chartBox" class="chart"></div>
	</div>
</template>

<script>
	import {
		init,
		dispose,
		extension
	} from 'klinecharts';
	export default {
		props: {
			currentId: {
				default: ''
			},
			currentItem: {
				default: ''
			}
		},
		data() {
			return {
				chart: null,
				timer: ''
			};
		},
		watch: {
			currentId: {
				handler(newValue, oldValue) {
					if (newValue && newValue !== oldValue) {
						// if (this.chart !== null) {
						//   dispose(this.chart);
						//   this.chart = null;
						// }

						this.getLine(newValue);
					}
				},
				// deep: true  // 深度监听
				immediate: true // 第一次改变就执行
			}
		},
		methods: {
			open() {
				this.timer = setInterval(() => {
					this.getLine(this.currentId);
				}, 5000);
			},
			clear() {
				clearInterval(this.timer);
			},
			getLine(id) {
				this.$server.post('/riben/gszk_kline', {
					id,
					type: 1
				}).then(res => {
					if (res.data.status == 1) {
						let dataList = [];
						let arr = res.data.data.data;
						for (let i = 0; i < arr.length; i++) {
							let item = arr[i];
							// 数据当中有很部分点信息会返回空值
							if (item[1]) {
								let kLineModel = {
									open: 0,
									close: item[1],
									high: 0,
									low: 0,
									timestamp: item[0]
								};

								dataList.push(kLineModel);
							}
						}

						this.setEchartOption(dataList);
					}
				});
			},
			setEchartOption(dataList) {
				console.log(dataList)
				// 获取的元素必须是标签元素不能是view，text，否者获取不到
				if (this.chart !== null) {
					this.chart.applyNewData(dataList);
					return;
				}
				this.chart = init(this.$refs.chartBox);
				this.chart.setStyleOptions({
					candle: {
						type: 'area',
						// 面积图
						area: {
							lineSize: 2,
							lineColor: '#385AA0',
							value: 'close',
							backgroundColor: [{
									offset: 0,
									color: '#FFFFFF'
								},
								{
									offset: 1,
									color: '#ECEEFB'
								}
							]
						},

						// 提示
						tooltip: {
							// 'always' | 'follow_cross' | 'none'
							showRule: 'none',
							// 'standard' | 'rect'
							showType: 'standard',
							labels: ['时间', '开', '收', '高', '低', '成交量'],
							values: null,
							defaultValue: 'n/a',
							rect: {
								paddingLeft: 0,
								paddingRight: 0,
								paddingTop: 0,
								paddingBottom: 6,
								offsetLeft: 8,
								offsetTop: 8,
								offsetRight: 8,
								borderRadius: 4,
								borderSize: 1,
								borderColor: '#3f4254',
								backgroundColor: 'rgba(17, 17, 17, .3)'
							},
							text: {
								size: 12,
								family: 'Helvetica Neue',
								weight: 'normal',
								color: '#D9D9D9',
								marginLeft: 8,
								marginTop: 6,
								marginRight: 8,
								marginBottom: 0
							}
						}
					},
					grid: {
						show: false,
						// 网格水平线
						horizontal: {
							show: false,
							size: 1,
							color: '#fff',
							// 'solid'|'dash'
							style: 'dash',
							dashValue: [2, 2]
						},
						// 网格垂直线
						vertical: {
							show: false,
							size: 1,
							color: '#fff',
							// 'solid'|'dash'
							style: 'dash',
							dashValue: [2, 2]
						}
					},
					xAxis: {
						tickLine: {
							show: false,
							color: '#fff'
						},
						// x轴分割文字
						tickText: {
							show: true,
							color: '#999999',
							family: 'PingFang SC',
							weight: '500',
							size: 12,
							paddingTop: 12,
							paddingBottom: 6
						},
						axisLine: {
							show: true,
							color: '#CCCCCC'
						}
					},
					yAxis: {
						tickLine: {
							show: false,
							size: 1,
							length: 3,
							color: '#fff'
						},
						tickText: {
							show: true,
							color: '#999999',
							family: 'PingFang SC',
							weight: '500',
							size: 12,
							paddingLeft: 12,
							paddingRight: 6
						},
						axisLine: {
							show: false,
							color: '#fff'
						}
					}
				});

				// 技术指标
				// this.chart.createTechnicalIndicator('VOL', false, {
				//   id: '',
				//   height: 100,
				//   minHeihgt: 50,
				//   dragEnabled: true
				// });

				this.chart.applyNewData(dataList);
			}
		}
	};
</script>

<style scoped lang="less">
	.chart {
		// height: 550rpx;
		height: 2rem;
	}
</style>