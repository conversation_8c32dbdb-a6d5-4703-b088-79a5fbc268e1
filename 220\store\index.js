import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)

export default new Vuex.Store({
	namespaced: true,
	state: {
		tokend: window.localStorage.getItem("tokend"),
		account: window.localStorage.getItem("account")
	},
	mutations: {
		saveToken(state, tokend) {
			window.localStorage.setItem("tokend", tokend);
			state.tokend = tokend;
		},
		saveAccount(state, account) {
			window.localStorage.setItem("account", account);
			state.account = account;
		},
	},
	actions: {
	},
	modules: {
	}
})
