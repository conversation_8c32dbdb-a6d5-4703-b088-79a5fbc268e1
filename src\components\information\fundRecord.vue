<template>
	<div class="page">
		<top-back :title="$t('資金流水')"></top-back>
		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<div class="cot">
				<div class="nav-box flex">
					<div class="nav-item" v-for="(item, index) in navList" :key="index"
						:class="{ active: currmentIndex == item.type }" @click="changeNav(item.type)">
						{{ item.name }}
					</div>
				</div>
				<div class="list">
					<no-data v-if="!logList.length"></no-data>
					<div class="list-item" v-for="(item, index) in logList" :key="index">
						<template v-if="currmentIndex == 1">
							<div class="top flex flex-b">
								<div class="flex flex-b">
									<!-- <div class="t01">訂單來源</div> -->
									<!-- <div class="tt2" > {{formatText1(item.name)}}</div> -->
									<div class="tt2" > {{formatText1(item.name)}}</div>
								</div>
								<!-- <div class="flex flex-b">
									<div class="tt">交易類型</div>
									<div class="tt">{{ formatText1(item.detailed) }}</div>
								</div> -->
								<div class="money" :class="parseFloat(item.money) > 0 ? 'red' : 'green'">{{ parseFloat(item.money) > 0 ? "+" : "" }}{{ $formatMoney(parseFloat(item.money).toFixed(2)) }}</div>
							</div>
							<div class="flex" style="margin: 0.1rem 0;">
								<div class="t01">{{ $t('交易時間') }}</div>
								<div class="t02">{{ $formatDate("YYYY/MM/DD hh:mm:ss", item.create_time * 1000)}}</div>
							</div>
							<div class="flex">
								<div class="t01">{{ $t('交易訂單') }}</div>
								<div class="t02 flex">
									{{ item.order_number }}
									<div class="icon copy" @click="copy(item.order_number)"></div>
								</div>
							</div>
							<!-- <div class="flex">
								<div class="t01">餘額變動</div>
								<div class="t02" :class="parseFloat(item.money) > 0 ? 'red' : 'green'">{{ parseFloat(item.money) > 0 ? "+" : "" }}{{ $formatMoney(parseFloat(item.money).toFixed(2)) }}</div>
							</div> -->
						</template>
						<template v-else>
							<div class="flex flex-b top">
								<div class="tt2">{{ currmentIndex == 2 ? $t("儲值") : $t("提領") }}</div>
								<!-- <div class="tt">{{ $t(item.stat)=='待審核'?'處理中':$t(item.stat) }}</div> -->
								<div class="money" :class="currmentIndex == 2 ? 'red' : 'green'">
									{{ currmentIndex == 2 ? "+" : "-" }}
									{{ $formatMoney(item.money) }}
								</div>
							</div>
							<div class="flex" style="margin: 0.1rem 0;">
								<div class="t01">{{ $t('交易時間') }}</div>
								<div class="t02">{{$formatDate("YYYY/MM/DD hh:mm:ss", item.create_time * 1000) }}
								</div>
							</div>
							<div class="flex">
								<div class="t01">{{ $t('交易訂單') }}</div>
								<div class="t02 flex">
									{{ item.order_number }}
									<div class="icon copy" @click="copy(item.order_number)"></div>
								</div>
							</div>
							<!-- <div class="flex flex-b">
								<div class="t01">{{currmentIndex==2?'儲值金額':'提領金額'}}</div>
								<div class="t02" :class="currmentIndex == 2 ? 'red' : 'green'">
									{{ currmentIndex == 2 ? "+" : "-" }}
									{{ $formatMoney(item.money) }}
								</div>
							</div> -->
						</template>
					</div>
				</div>
			</div>
		</van-pull-refresh>
		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "fundRecord",
		props: {},
		data() {
			return {
				isLoading: false,
				currmentIndex: 1,
				logList: [
					// {
					// 	detailed:'detailed',
					// 	money:100,
					// 	create_time:44444,
					// 	order_number:'1323656',
					// 	stat:'完成'
					// }
				],
			};
		},
		components: {},
		created() {},
		computed: {
			navList() {
				return [{
						name: this.$t("交易"),
						type: 1
					},
					{
						name: this.$t("儲值"),
						type: 2
					},
					{
						name: this.$t("提領"),
						type: 3
					},
				];
			},
			formatText() {
				return (index, value) => {
					let str = "";
					value = value.replace("！", " ");
					value = value.replace(",", "");
					if (index) {
						let start = value.indexOf("號");
						str = value.slice(start + 1);
					} else {
						let pIdx = value.indexOf(" ");
						let name = value.slice(0, pIdx);
						let mIdx = value.indexOf("碼");
						let gIdx = value.indexOf("名");
						let code = value.slice(mIdx + 1, gIdx - 2);
						str = `${this.$t(name)}`;
					}
					return str;
				};
			},
			formatText1() {
				return (value) => {
				console.log(value)
					value = value.replace(",", " ");
					value = value.replace("股票代码", "股票代碼/名稱");
					value = value.replace("股票名称", "");
					value = value.replace("充值", "儲值");
					value = value.replace("提现", "提領");
					value = value.replace("提现 ", "提領");
					value = value.replace("待審核", "處理中");
					value = value.replace("申请日内交易", "申請日內交易");
					value = value.replace("跟单", "跟單");
					value = value.replace("审核日内交易", "稽核日內交易");
					value = value.replace("中签认缴", "中籤認繳");
					value = value.replace("买手续费", "買手續費");
					value = value.replace("购买股票", "購買股票");
					value = value.replace("借款", "借款");
					value = value.replace("信用还款", "信用還款");
					value = value.replace("提領拒绝", "提領拒絕");
					value = value.replace("发单人", "");
					value = value.replace("跟單", "募集繳費");
					value = value.replace("平仓", "平倉");
					value = value.replace("返还资金", "返還資金");
					value = value.replace("卖手续费", "賣手續費");
					value = value.replace("印花税", "印花稅");
					value = value.replace("产品名称", "產品名稱");
					return value;
				};
			},
		},
		mounted() {
			this.changeNav(1);
		},
		methods: {
			onRefresh() {
				this.changeNav(1);
			},
			changeNav(index) {
				this.currmentIndex = index;
				this.logList = [];
				this.getRecord();
			},
			getRecord() {
				this.$refs.loading.open(); //开启加载
				this.logList = [];
				if (this.currmentIndex == 1) {
					this.$server
						.post("/user/capitalloglist", {
							type: "zar",
						})
						.then((res) => {
							this.$refs.loading.close();
							this.isLoading = false; //下拉刷新状态
							if (res.status == 1) {
								this.logList = res.data;
							}
						});
				}

				if (this.currmentIndex == 2) {
					this.$server
						.post("/user/rechargelist", {
							type: "zar",
						})
						.then((res) => {
							this.$refs.loading.close();
							this.isLoading = false; //下拉刷新状态
							if (res.status == 1) {
								this.logList = res.data;
							}
						});
				}

				if (this.currmentIndex == 3) {
					this.$server
						.post("/user/withdrawallist", {
							type: "zar",
						})
						.then((res) => {
							this.$refs.loading.close();
							this.isLoading = false; //下拉刷新状态
							if (res.status == 1) {
								this.logList = res.data;
							}
						});
				}
			},
			copy(text) {
				let textarea = document.createElement("textarea");
				textarea.value = text;
				textarea.readOnly = "readOnly";
				document.body.appendChild(textarea);
				textarea.select(); // 选中文本内容
				textarea.setSelectionRange(0, text.length); // 设置选定区的开始和结束点
				this.$toast(this.$t("複製成功"));
				var result = document.execCommand("copy"); //将当前选中区复制到剪贴板
				textarea.remove();
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 1rem 0 0.1rem;
		min-height: 100vh;
		.cot {
			.nav-box {
				width: 94%;
				position: fixed;
				top: 0.5rem;
				left: 50%;
				transform: translateX(-50%);
				z-index: 999;
				height: 0.38rem;
				background: #232429;
				border-radius: 0.19rem;
				padding: 0.03rem;
				.nav-item {
					flex: 1;
					font-family: PingFang SC;
					font-weight: 500;
					font-size: 0.13rem;
					color: #999999;
					line-height: 0.32rem;
					text-align: center;
					position: relative;

					&.active {
						height: 0.32rem;
						background: #8DFD99;
						border-radius: 0.16rem;
						color: #000000;
						position: relative;
						// &::after{
						// 	position: absolute;
						// 	content: '';
						// 	bottom: 0;
						// 	left: 50%;
						// 	transform: translateX(-50%);
						// 	width: 50%;
						// 	height: 0.02rem;
						// 	background-color: #E5C79F;
						// }
					}
				}
			}

			.list {
				margin: 0 0.12rem;
				.list-item {
					background: #232429;
					border-radius: 0.19rem;
					margin-bottom: 0.15rem;
					padding: 0.12rem;
					.tt2{
						font-family: PingFang SC;
						font-weight: 500;
						font-size: 0.14rem;
						color: #FFFFFF;
					}
					.tt{
						height: 0.24rem;
						padding: 0 0.1rem;
						background: linear-gradient( 90deg, #1B167A 0%, #4383C7 100%);
						border-radius: 0.04rem 0.04rem 0.04rem 0.04rem;
						font-family: PingFang TC, PingFang TC;
						font-weight: 400;
						font-size: 0.12rem;
						color: #FFFFFF;
						line-height: 0.24rem;
					}
					.money{
						font-family: PingFang SC;
						font-weight: 500;
						font-size: 0.13rem;
					}
					.t01{
						font-family: PingFang SC;
						font-weight: 400;
						font-size: 0.11rem;
						color: #666666;
					}
					.t02{
						font-family: PingFang SC;
						font-weight: 400;
						font-size: 0.11rem;
						color: #666666;
					}
					.copy {
						width: 0.1rem;
						height: 0.1rem;
						margin-left: 0.05rem;
					}

					&.green {
						color: #3CAD21;
					}
					&.red {
						color: #FE0200;
					}
				}
			}
		}
	}
</style>
