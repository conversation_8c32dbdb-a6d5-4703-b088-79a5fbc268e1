<template>
	<div class="gdindex">
		<div class="headf">
			<top :title="$t(text)"></top>
			<div class="gdjl" @click="$router.push('/gdindex/gdList')" v-if="false">
				{{ $t("fllow").txt1 }}
			</div>
		</div>
		<div class="ankm" v-if="false">
			<div id="container" class="zankl"></div>
		</div>

		<div class="list" v-if="chooseList.length">
			<div class="item" v-for="(item, index) in chooseList" :key="index" @click="stockDetails(item)">
				<div class="toukm">
					<div class="toulm" v-if="false">{{ $t("fllow").txt2 }}:{{ item.sender }}</div>
					<div>{{ item.name }}</div>
				</div>
				<div class="cont">
					<div class="flex flex-b zongjian flex-wrap">
						<div class="flex flex-b wenk">
							<div class="namk">{{ $t("fllow").txt3 }}</div>
							<div class="hjn">{{ $formatMoney(item.capital) }}円</div>
						</div>
						<div class="flex flex-b wenk">
							<div class="namk">{{ $t("fllow").txt4 }}</div>
							<div class="hjn">{{ item.yprofit }}</div>
						</div>
					</div>
					<img src="../../assets/skin/market/gdImg.png" />
					<div class="link flex flex-c">{{$t('fllow').txt13}}</div>
					<div class="state" v-if="false">
						<div class="timek">{{ $t("fllow").txt5 }}：{{ item.locktime }}</div>
						<div class="anlikm" style="background: #666;" v-if="item.state == -1">{{ $t("fllow").txt6 }}
						</div>
						<div class="anlikm" v-if="item.state == 1">{{ $t("fllow").txt7 }}</div>
						<div class="anlikm" v-if="item.state == 0">{{ $t("fllow").txt8 }}</div>
					</div>
				</div>
			</div>
		</div>
		<div v-else>
			<div class="noData text-center">{{$t('home').txt21}}</div>
		</div>
		<van-popup v-model="show" position="bottom">
			<div class="popBuy" style="padding:0.15rem;width:100%;">
				<div class="uni-title">{{ $t("fllow").txt9 }}</div>
				<div class="uni-content">
					<div class="flex flex-b">
						<div class="f-big">{{ $t("fllow").txt3 }}</div>
						<div class="list_item_right">
							{{ $formatMoney(stockObj.capital) }}円
						</div>
					</div>
					<div class="flex flex-b">
						<div class="f-big">{{ $t("fllow").txt10 }}</div>
						<div class="list_item_right">{{ $formatMoney(stockObj.money) || 0 }}円</div>
					</div>
					<div class="f-big">{{ $t("fllow").txt11 }}</div>
					<div class="list_item_right f-big">
						<input v-model="buyObj.handle" :placeholder="$t('fllow').txt12" placeholder-style="color:#fff" @input="changeInput" />
					</div>
					<div @click="buyFn" class="btn-big">{{ $t("fllow").txt13 }}</div>
				</div>
			</div>
		</van-popup>
		<!-- <div style="height:180px;"></div> -->
	</div>
</template>
<script>
	import bottomnav from "../bar/bottomnav.vue";
	import {
		mapState,
		mapMutations
	} from "vuex";

	import * as echarts from "echarts";
	import axios from "axios";
	import top from "../bar/toper.vue";
	import Vue from "vue";
	import qs from "qs";
	import {
		Toast
	} from "vant";
	Vue.use(Toast);
	import {
		Popup
	} from "vant";
	Vue.use(Popup);

	export default {
		name: "gdindex",
		components: {
			top,
			bottomnav,
		},
		data() {
			return {
				text: this.$t('home').top3,
				stockObj: {},
				chooseList: [
					// {
					// name: '纳斯达克',
					// sender: '2003',
					// capital: 7000,
					// yprofit: 5656,
					// cj_num: 1111,
					// locktime: '2024/02/30',
					// end: '2024/02/30',
					// state: -1,
					// },
				],
				show: false,
				buyObj: {
					handle: "",
				},
			};
		},
		computed: {
			countMoney() {
				return this.stockObj.price * this.buyObj.handle * 1000;
			},
		},
		mounted() {
			// 基于准备好的dom，初始化echarts实例
			/* var myChart = echarts.init(document.getElementById("container"));
			this.$server.post("/taiguo/zdtock", {}).then((res) => {
				let data = Object.values(res.data.data);
				// console.log("data", data);
				// 绘制图表
				myChart.setOption({
					title: {
						text: this.$t("涨跌统计"),
						textStyle: {
							color: "#D7BE82",
						},
					}, // 全局调色盘。
					lineStyle: {
						color: "#999999", //更改坐标轴颜色
					},
					textStyle: {
						color: "#999999", //更改坐标轴文字颜色
					},
					tooltip: {},
					xAxis: {
						data: [
							">7%",
							"7%~5%",
							"5%~3%",
							"3%~0",
							this.$t("平盘"),
							"0~3%",
							"3%~5%",
							"5%~7%",
							">7%",
						],
						axisLabel: {
							interval: 0,
							rotate: 45,
						},
					},
					yAxis: {
						axisLabel: {
							interval: 0,
							rotate: 45,
						},
					},
					series: [{
						type: "bar",
						label: {
							show: true,
							position: "top",
							textStyle: {
								fontWeight: "bolder",
								fontSize: "12",
							},
						},
						data: [{
								value: data[0],
								itemStyle: {
									normal: {
										color: "#d31b26",
									},
								},
							},
							{
								value: data[1],
								itemStyle: {
									normal: {
										color: "#d31b26",
									},
								},
							},
							{
								value: data[2],
								itemStyle: {
									normal: {
										color: "#d31b26",
									},
								},
							},
							{
								value: data[3],
								itemStyle: {
									normal: {
										color: "#d31b26",
									},
								},
							},
							{
								value: data[4],
								itemStyle: {
									normal: {
										color: "#2f4554",
									},
								},
							},
							{
								value: data[5],
								itemStyle: {
									normal: {
										color: "#1BBD5D",
									},
								},
							},
							{
								value: data[6],
								itemStyle: {
									normal: {
										color: "#1BBD5D",
									},
								},
							},
							{
								value: data[7],
								itemStyle: {
									normal: {
										color: "#1BBD5D",
									},
								},
							},
							{
								value: data[8],
								itemStyle: {
									normal: {
										color: "#1BBD5D",
									},
								},
							},
						],
					}, ],
				});
			}); */
		},

		created() {
			this.getNew();
		},
		destroyed() {},
		methods: {
			changeInput(e) {
				if (this.$formatMoney(e.target.value.split(',').join(''))) {
					//console.log(this.$formatMoney(e.target.value.split(',').join('')))
					this.buyObj.handle = this.$formatMoney(e.target.value.split(',').join(''), 0)
				} else {
					this.buyObj.handle = 1
				}
			},
			getNew() {
				this.$server.post("/riben/productlist", {}).then((res) => {
					if (res.data.data.length == 0) return;

					res.data.data.forEach((vo) => {
						let now = Date.now() * 0.001;
						vo.state = now > vo.locktime ? -1 : now > vo.end ? 1 : 0;
						vo.locktime = this.timestampToTime(vo.locktime);
					});

					this.chooseList = res.data.data.reverse();

				});

			},

			timestampToTime(timestamp) {
				var date = new Date(timestamp * 1000); //时间戳若为10位时需*1000
				var Y = date.getFullYear();
				var M =
					date.getMonth() + 1 >= 10 ?
					date.getMonth() + 1 :
					"0" + (date.getMonth() + 1);
				var D = date.getDate() >= 10 ? date.getDate() : "0" + date.getDate();
				var h = date.getHours() >= 10 ? date.getHours() : "0" + date.getHours();
				var m =
					date.getMinutes() >= 10 ? date.getMinutes() : "0" + date.getMinutes();
				var s =
					date.getSeconds() >= 10 ? date.getSeconds() : "0" + date.getSeconds();
				return `${Y}-${M}-${D} ${h}:${m}:${s}`;
			},

			stockDetails(stock) {
				this.stockObj = stock;
				this.show = true;
			},
			buyFn() {
				if (this.buyObj.handle < Number(this.stockObj.money)) {
					Toast({
						message: this.$t("最低投资") + this.stockObj.money,
						duration: 2000,
					});

					return;
				}
				this.$server.post("/riben/buy_product", {
						productid: this.stockObj.id,
						money: this.buyObj.handle.split(',').join(''),
					})
					.then((res) => {
						// console.log("res", res);
						this.show = false;
						Toast({
							message: this.$t(res.data.msg),
							duration: 2000,
						});
					});
			},
		},
	};
</script>
<style scoped lang="less">
	.gdindex {
		background: #0F161C;
		min-height: 100vh;
		padding-top: .47rem;

		.headf {
			height: 0.47rem;
			width: 100vw;
			position: fixed;
			top: 0;
			left: 0;
			z-index: 998;

			.gdjl {
				position: fixed;
				top: 15px;
				right: 20px;
				z-index: 999;
			}
		}

		.list {
			position: relative;
			z-index: 20;

			.item {
				overflow: hidden;

				.toukm {
					font-size: .16rem;
					padding: 0.05rem 0.12rem;
					background-color: rgba(94, 213, 168, 0.2);
					border-bottom: .03rem solid #fff;
					font-family: PingFangSC, PingFang SC;
					font-weight: 600;
					font-size: 0.14rem;
					color: #FFFFFF;
					align-items: flex-end;
				}

				.cont {
					margin: 0 0.12rem;

					.zongjian {
						position: relative;
						margin: 0 0.12rem;

						.wenk {
							width: 48%;
							display: flex;
							align-items: center;
							justify-content: space-between;
							padding: .1rem 0;
							border-bottom: 0.01rem solid rgba(255, 255, 255, 0.2);
							font-family: PingFangSC, PingFang SC;
							font-weight: 600;
							font-size: 0.11rem;

							.hjn {
								color: #FE0000;
							}

							.namk {
								color: #718A94;
							}
						}
					}

					img {
						margin-top: 0.1rem;
						display: block;
						width: 100%;
						border-radius: 0.05rem;
					}

					.link {
						margin: 0.15rem 0;
						height: 0.33rem;
						background: #5ED5A8;
						border-radius: 0.05rem;
						border: 0.01rem solid rgba(255, 255, 255, 0.2);
						font-family: PingFangSC, PingFang SC;
						font-weight: 600;
						font-size: 0.14rem;
						color: #333333;
					}

					.state {
						margin: 0 auto;
						display: flex;
						border-top: .01rem solid #f2f2f2;
						justify-content: space-between;
						align-items: center;
						margin-top: .1rem;
						padding-top: .1rem;

						.timek {
							color: #999;
						}

						.anlikm {
							background: #A8EC7A;
							border-radius: .04rem;
							text-align: center;
							color: #333;
							padding: .1rem .2rem;

							&.jinxin {
								background: #2049ce;
							}

							&.jieshu {
								background: #323640;
								color: #000;
							}
						}
					}
				}
			}
		}

		.van-popup {
			border-radius: .12rem .12rem 0 0;
		}
		.popBuy {
			background: #424E4E;
			border-radius: .12rem .12rem 0 0;
			padding: 0 .2rem;

			.uni-title {
				color: #fff;
				text-align: center;
				font-size: 18px;
				margin-bottom: 0.1rem;
				font-weight: 600;
			}

			.uni-content {
				.f-big {
					font-family: PingFangSC, PingFang SC;
					font-weight: 600;
					font-size: 0.14rem;
					color: #fff;
					line-height: 0.3rem;
				}
			}
			
			.list_item_right {
				font-size: 16px;
				text-align: right;
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 0.14rem;
				color: #fff;

				input {
					margin-top: 0.1rem;
					width: 100%;
					height: 0.46rem;
					background: #000000;
					border-radius: 0.1rem;
					font-family: PingFangSC, PingFang SC;
					font-weight: 600;
					font-size: 0.14rem;
					color: #fff;
					padding-left: 0.1rem;
				}
			}
			.btn-big{
				margin: 0.1rem 0;
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 0.14rem;
				color: #000;
			}
		}

		.ankm {
			width: 100%;
			padding: 10px 10px 20px;
			.zankl {
				width: 100%;
				height: 300px;
				border-radius: 10px;
				margin: 0 auto;
				border-radius: 10px;
				box-shadow: 0 0 2px 4px #f5f5f5;
				padding: 10px;
			}
		}

	}
</style>