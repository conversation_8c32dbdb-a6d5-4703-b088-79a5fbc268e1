<template>
	<div class="page">
		<top-back></top-back>
		<div class="topBg flex">
			<img class="kefIcon" src="../../assets/v5/kefIcon.png" style="width: 0.99rem;height: 0.97rem;" alt="" />
			<div class="t">{{ $t('網路客服  為您服務～') }}</div>
		</div>
		<!-- <img src="../../assets/v3/kfBg.png" style="width: 100%;height: 1.89rem;" alt="" /> -->
		<div class="bgg">
			<div class="flex flex-b bg" @click="goUrl('kefu')">
				<div class="flex">
					<!-- <img src="../../assets/v3/kfIcon.png" style="width: 0.33rem;height: 0.33rem;" alt="" /> -->
					<div class="t">{{ $t('客服1號') }}</div>
				</div>
				<div class="icon arrow"></div>
			</div>
			<div class="flex flex-b bg" @click="goUrl('kefu1')">
				<div class="flex">
					<!-- <img src="../../assets/v3/kfIcon.png" style="width: 0.33rem;height: 0.33rem;" alt="" /> -->
					<div class="t">{{ $t('客服2號') }}</div>
				</div>
				<div class="icon arrow"></div>
			</div>
		</div>
	</div>
</template>

<script>
	export default{
		data() {
			return{

			}
		},
		methods:{
			goUrl(item) {
				if (item == 'kefu') {
					this.getConfig()
				} else if (item == 'kefu1') {
					this.getConfig1()
				} else {
					this.$toPage(item.url)
				}
			},
			async getConfig() {
				const res = await this.$server.post("/common/config", {
					type: "all"
				});
				let val = {};
				res.data.forEach((vo) => {
					val[vo.name] = vo.value;
				});
				this.$openUrl(val.kefu); //重新获取
			},
			async getConfig1() {
				const res = await this.$server.post("/common/config", {
					type: "all"
				});
				let val = {};
				res.data.forEach((vo) => {
					val[vo.name] = vo.value;
				});
				this.$openUrl(val.down); //重新获取
			},
		}
	}
</script>

<style lang="less" scoped>
	.page{
		padding-top: 0.45rem;
		background: #2C2B30;
		.topBg{
			background: linear-gradient( 108deg, #FEE1BF 0%, #FAC591 100%);
			padding: 0.2rem;
			padding-bottom: 0.5rem;
			.t{
				margin-left: 0.1rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 0.2rem;
				color: #3C2500;
			}
		}
		.bgg{
			margin-top: -0.3rem;
			position: relative;
			z-index: 999;
			background: #2C2B30;
			padding: 0.2rem;
			border-radius: 0.2rem 0.2rem 0rem 0rem;
		}
		.bg{
			position: relative;
			z-index: 999;
			margin-bottom: 0.25rem;
			.t{
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.14rem;
				color: #CBBFB0;
			}
		}
	}
	::v-deep .header{
		background: linear-gradient( 108deg, #FEE1BF 0%, #FAC591 100%);
	}
</style>