<template>
  <div class="withdraw">
    <div class="head">
      <top :title="$t(text)"></top>
    </div>
    <div class="flex-column-item">
      <img
        src="../../assets/v2/txBg.png"
        style="width: 1.5rem;height: 1.5rem;margin: 0.1rem 0;"
        alt=""
      />
    </div>
    <div class="box">
      <!-- <div class="money flex flex-b">
				<p>{{ $t("recharge").txt1 }}</p>
				<h6>{{ $formatMoney(userdata.riyuan) }}</h6>
			</div> -->
      <div class="list">
        <div class="heizhi">
          <div class="hekm">
            <div class="flex flex-b">
              <h6>{{ $t("withdraw").txt1 }}</h6>
              <a class="flex" @click="all">{{ $t("withdraw").txt3 }}</a>
            </div>
            <div class="shurdu">
              <input
                :placeholder="$t('withdraw').txt2"
                v-model="number"
                @input="changeInput"
                @blur="changeInput2"
              />
            </div>
            <h6>{{ $t("withdraw").txt4 }}</h6>
            <div class="shurdu">
              <input
                :placeholder="$t('withdraw').txt2"
                v-model="zhip"
                type="password"
              />
            </div>
          </div>
        </div>

        <div class="xuanzh" @click="xunzhe">
          <a>{{ $t(tcardd) }}</a>
          <p class="flex">
            {{ tixianz }}
            <span></span>
          </p>
        </div>
        <div class="btn-big" @click="tixiank">{{ $t("withdraw").txt5 }}</div>
      </div>
    </div>
    <van-popup v-model="show" position="bottom" class="pupbg">
      <div class="kun">
        <div
          class="yinghk"
          v-for="(item, i) in cardlist"
          :key="i"
          @click.stop="xuanak(item)"
        >
          <p>{{ item.bank_address }} ({{ item.bank_num }})</p>
          <span :class="{ xuan: cardid == item.id }"></span>
        </div>

        <div class="quxiao" @click="show = false">
          {{ $t("withdraw").txt6 }}
        </div>
      </div>
    </van-popup>

    <paypass :show="zhifu" @closepop="closepop"></paypass>
  </div>
</template>
<script type="text/javascript">
import Vue from "vue";
import qs from "qs";
import axios from "axios";
import top from "../bar/toper.vue";
import { Popup } from "vant";
Vue.use(Popup);
import paypass from "./paymentPassword.vue";
import { Toast } from "vant";
Vue.use(Toast);
export default {
  name: "withdraw",
  data() {
    return {
      text: this.$t("mine").money.btn2,
      number: "",
      cnum: 1,
      cnumd: 1,
      show: false,
      cardlist: "",
      tcardd: this.$t("withdraw").txt7,
      userdata: {},
      zhifu: false,
      cardid: "",
      zhip: "",
      tixianz: "",
      minwithdrawal: "",
      maxwithdrawal: "",
    };
  },
  components: {
    top,
    paypass,
  },
  methods: {
    getConfig() {
      this.$server.post("/common/config", {type: 'jpy'}).then((str) => {
        if (str.data.status == 1) {
          str.data.data.forEach((item) => {
            // 提现最小值
            if (item.name == "minwithdrawal") {
              this.minwithdrawal = Number(item.value);
            }

            // 提现最大值
            if (item.name == "maxwithdrawal") {
              this.maxwithdrawal = Number(item.value);
            }
          });
        }
      });
    },

    changeInput(e) {
      if (this.$formatMoney(e.target.value.split(",").join(""))) {
        this.number = e.target.value.split(",").join("");
      } else {
        this.number = 1;
      }
    },
    changeInput2() {
      let dot = this.number.split(".");
      if (dot.length > 1) {
        this.number = this.$formatMoney(this.number, dot[1].length);
      } else {
        this.number = this.$formatMoney(this.number, 0);
      }
    },
    choose(e, k) {
      this.cnum = k;
      this.number = e;
    },
    all() {
      this.number = this.userdata.jpy;
    },
    closepop(value) {
      this.zhifu = false;
      if (value) {
        this.zhip = value;
        this.enterwith();
      }
    },
    choosed(e) {
      this.cnumd = e;
    },
    xuanak(e) {
      console.log(e);
      this.show = false;
      this.tcardd = this.$t("withdraw").txt8;
      this.tixianz = e.bank_name;
      this.cardid = e.id;
    },
    enterwith() {
      if (this.zhip == "") {
        Toast({
          message: this.$t("withdraw").txt9,
          duration: 2000,
        });
        return false;
      }
      var datas = qs.stringify({
        money: this.number.split(",").join(""),
        bankid: this.cardid,
        passwords: this.zhip,
        type: "jpy",
      });
      this.$server.post("/user/withdrawal", datas).then((str) => {
        if (str.data.status == 1) {
          Toast({
            message: this.$t(str.data.msg),
            duration: 3000,
          });
          this.number = "";
          this.zhip = "";
          this.$server.post("/user/getUserinfo").then((str) => {
            if (str.data.status == 1) {
              this.userdata = str.data.data;
              window.localStorage.setItem(
                "userdata",
                JSON.stringify(str.data.data)
              );
            } else {
              Toast({
                message: this.$t(str.data.msg),
                duration: 2000,
              });
            }
          });
        } else {
          if (str.data.msg.indexOf("请于出金时间") != -1) {
            Toast({
              message: this.$t("请于出金时间：") + str.data.msg.substr(7, 16),
              duration: 2000,
            });
          } else if (str.data.msg.indexOf("出金最小值") != -1) {
            Toast({
              message:
                this.$t("出金最小值") +
                this.$formatMoney(parseFloat(this.ruj.value).toFixed(0)),
              duration: 2000,
            });
          } else if (str.data.msg.indexOf("出金最大值") != -1) {
            Toast({
              message:
                this.$t("出金最大值") +
                this.$formatMoney(parseFloat(this.ruj2.value).toFixed(0)),
              duration: 2000,
            });
          } else {
            Toast({
              message: this.$t(str.data.msg),
              duration: 2000,
            });
          }
        }
      });
    },
    tixiank() {
      let _this = this;
      Toast.loading({
        message: "Loading...",
        forbidClick: true,
      });

      let num = this.number.replace(/\,/g, "");

      //   console.log(num, this.minwithdrawal, this.maxwithdrawal);

      if (Number(num) < this.minwithdrawal) {
        Toast({
          message:
            this.$t("withdraw").txt10 +
            this.$formatMoney(this.minwithdrawal, 0),
          duration: 2000,
        });
        return false;
      }

      if (Number(num) > this.maxwithdrawal) {
        Toast({
          message:
            this.$t("出金最大值") + this.$formatMoney(this.maxwithdrawal, 0),
          duration: 2000,
        });
        return false;
      }

      if (this.cardid == "") {
        Toast({
          message: this.$t("withdraw").txt11,
          duration: 2000,
        });
        return false;
      }

      if (!this.userdata.passwords) {
        Toast({
          message: this.$t("withdraw").txt12,
          duration: 2000,
        });
        setTimeout(function() {
          _this.$router.push({
            path: "/user/setPassword",
          });
        }, 2000);
        return false;
      }

      this.enterwith();

      //   this.$server.post("/riben/config").then((str) => {
      //     if (str.data.status == 1) {
      //       str.data.data.forEach((item) => {
      //         if (item.name == "minwithdrawal") {
      //           this.minwithdrawal = Number(item.value);
      //           if (this.number < this.minwithdrawal) {
      //             Toast({
      //               message: this.$t("withdraw").txt10 + this.minwithdrawal,
      //               duration: 2000,
      //             });
      //             return false;
      //           }

      //           Toast({
      //             message:
      //               this.$t("出金最大值") +
      //               this.$formatMoney(parseFloat(this.ruj2.value).toFixed(0)),
      //             duration: 2000,
      //           });

      //           if (this.cardid == "") {
      //             Toast({
      //               message: this.$t("withdraw").txt11,
      //               duration: 2000,
      //             });
      //             return false;
      //           }

      //           if (!this.userdata.passwords) {
      //             Toast({
      //               message: this.$t("withdraw").txt12,
      //               duration: 2000,
      //             });
      //             setTimeout(function() {
      //               _this.$router.push({
      //                 path: "/user/setPassword",
      //               });
      //             }, 2000);
      //             return false;
      //           }

      //           this.enterwith();
      //         }
      //       });
      //     } else {
      //       if (str.data.msg.indexOf("请于出金时间") != -1) {
      //         Toast({
      //           message: this.$t("请于出金时间：") + str.data.msg.substr(7, 16),
      //           duration: 2000,
      //         });
      //       } else if (str.data.msg.indexOf("出金最小值") != -1) {
      //         Toast({
      //           message:
      //             this.$t("出金最小值") +
      //             this.$formatMoney(parseFloat(this.ruj.value).toFixed(0)),
      //           duration: 2000,
      //         });
      //       } else if (str.data.msg.indexOf("出金最大值") != -1) {
      //         Toast({
      //           message:
      //             this.$t("出金最大值") +
      //             this.$formatMoney(parseFloat(this.ruj2.value).toFixed(0)),
      //           duration: 2000,
      //         });
      //       } else {
      //         Toast({
      //           message: this.$t(str.data.msg),
      //           duration: 2000,
      //         });
      //       }
      //     }
      //   });
    },
    xunzhe() {
      if (this.cardlist) {
        this.show = true;
      } else {
        this.getcard();
      }
    },
    getcard() {
      this.$server.post("/user/cardList", {type: 'jpy'}).then((str) => {
        if (str.data.status == 1) {
          this.cardlist = str.data.data;
          if (this.cardlist.length >= 1) {
            this.show = true;
          } else {
            this.$router.push({
              path: "/user/BankCard",
            });
          }
        } else {
          Toast({
            message: this.$t(str.data.msg),
            duration: 2000,
          });
        }
      });
    },
  },
  destroyed() {},
  mounted() {
    this.getConfig();
    // if (window.localStorage.getItem("userdata")) {
    //   this.udata = JSON.parse(window.localStorage.getItem("userdata"));
    // }
    this.$server.post("/user/getUserinfo").then((str) => {
      if (str.data.status == 1) {
        this.userdata = str.data.data;
        window.localStorage.setItem("userdata", JSON.stringify(str.data.data));
      } else {
        Toast({
          message: this.$t(str.data.msg),
          duration: 2000,
        });
      }
    });
  },
};
</script>
<style type="text/css" lang="less" scoped="scoped">
input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  color: #999;
  font-size: 0.16rem;
  font-weight: 500;
}

.withdraw {
  background: #0f161c;
  min-height: 100vh;
  .box {
    margin: 0.12rem;
    padding: 0.12rem;
  }
  .money {
    background: url("../../assets/skin/mine/img1.png") center no-repeat;
    background-size: 100%;
    border-radius: 0.1rem;
    height: 1rem;
    padding: 0 0.12rem;

    h6 {
      font-weight: 500;
      font-size: 0.24rem;
      color: #fff;
    }

    p {
      color: #fff;
      font-size: 0.15rem;
    }
  }

  .list {
    min-height: 0.8rem;
    padding-bottom: 0.02rem;
    .btn-big {
      margin: 0.4rem 0;
    }
  }
  .kun {
    background: #fff;
    margin: 0 auto;
    padding: 0.1rem;

    .yinghk {
      margin: 0 auto;
      display: flex;
      height: 0.44rem;
      border-radius: 0.1rem;
      margin-bottom: 0.1rem;
      background: #f2f2f2;
      justify-content: space-between;

      p {
        margin-left: 0.15rem;
        color: #333;
        font-size: 0.16rem;
        line-height: 0.44rem;
      }

      span {
        width: 0.15rem;
        height: 0.15rem;
        margin-right: 0.15rem;
        margin-top: 0.15rem;

        &.xuan {
          background: url(../../assets/skin/mine/xuanz.png) no-repeat center;
          background-size: 100%;
        }
      }
    }

    .quxiao {
      height: 0.44rem;
      margin: 0 auto;
      text-align: center;
      line-height: 0.44rem;
      background: #ffffff;
      color: #4a7dff;
      font-size: 0.16rem;
      border-radius: 0.1rem;
    }
  }

  .rechg {
    height: 0.44;
    //background: linear-gradient(90deg, #fce051, #ff8335);
    background: linear-gradient(90deg, #77e1e3, #224aae);
    border-radius: 0.05rem;
    margin: 0 auto;
    text-align: center;
    line-height: 0.44rem;
    color: #fff;
    font-size: 0.16rem;
    margin-top: 0.3rem;
  }

  .xuanzh {
    margin: 0.15rem auto;
    height: 0.44rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #000000;
    border-radius: 0.1rem;
    padding: 0 0.1rem;
    a {
      color: #fff;
      font-weight: 400;
      font-size: 0.14rem;
    }

    p {
      font-size: 0.16rem;
      color: #fff;

      span {
        background: url(../../assets/skin/mine/arrow1.png) no-repeat center;
        background-size: 100%;
        width: 0.07rem;
        height: 0.13rem;
        display: inline-block;
        vertical-align: middle;
        margin-left: 0.05rem;
      }
    }
  }

  .heizhi {
    min-height: 0.5rem;

    .hekm {
      h6 {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 0.12rem;
        color: #ffffff;
      }
      a {
        color: #fff;
        font-size: 0.15rem;
        font-weight: 400;
        font-size: 0.12rem;
        color: #fe0000;
        // &:before{
        // 	content:'';display: block;
        // 	width:.01rem;height:.16rem;
        // 	background:#C7C0C0;margin-right:.12rem;
        // }
      }
      .shurdu {
        margin: 0.16rem 0;
        width: 100%;
        input {
          width: 100%;
          height: 0.46rem;
          background: #000000;
          border-radius: 0.1rem;
          padding-left: 0.1rem;
          font-weight: 400;
          font-size: 0.14rem;
          color: #999999;
          color: #fff;
        }
      }
    }
  }

  .txtd {
    margin-left: 0.13rem;
    margin-top: 0.36rem;

    p {
      color: #666666;
      font-size: 0.13rem;
      line-height: 0.21rem;

      span {
        color: #cb1a1e;
      }
    }
  }
}
</style>
