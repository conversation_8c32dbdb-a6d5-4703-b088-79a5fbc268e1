<template>
	<div class="dividend2">
		<div class="headf">
			<top :title="$t(text)"></top>
		</div>
		<div class="nav-box">
			<div class="itemBox flex1 flex flex-c">
				<div class="nav-item" v-for="(item,index) in navList" :key='index' :class="{active:currmentIndex === item.type}" @click="changeNav(item.type)">
					{{item.name}}
				</div>
			</div>
		</div>
		<div class="inBox">
			<template v-if="currmentIndex == 0">
				<div class="list-item" v-for="(item, index) in chooseList" :key="index" @click="stockDetails(item)">
					<div class="flex title" style="align-items: flex-end;">
						<div class="shares-name">{{ item.name }}</div>
						<span class="shares-code">{{ item.symbol }}</span>
					</div>
					<div class="list flex flex-b flex-wrap">
						<div class="item flex flex-b">
							<div class="th-td">{{$t('dividend').txt2}}</div>
							<span class="shares-price-num">{{ $formatMoney(item.price) }}円</span>
						</div>
					</div>
				</div>
			</template>
			<template v-else>
				<div class="list-item" v-for="(item, index) in myList" :key="index">
					<div class="flex flex-b title">
						<div class="flex" style="align-items: flex-end;">
							<div class="shares-name">{{ item.stock_name }}</div>
							<span class="shares-code">{{ item.stock_code }}</span>
						</div>
						<div class="shares-name-box">
							<span class="shares-name" v-if="parseInt(item.status)===0">{{$t('dividend').txt14}}</span>
							<span class="shares-name" v-if="parseInt(item.status)===1">{{$t('dividend').txt15}}</span>
							<span class="shares-name" v-if="parseInt(item.status)===2">{{$t('dividend').txt16}}</span>
						</div>
					</div>
					<div class="flex flex-b flex-wrap list">
						<div class="flex flex-b item">
							<div class="th-td">{{$t('dividend').txt5}}</div>
							<span class="shares-price-num">{{ $formatMoney(item.buy_price) }}円</span>
						</div>
						<div class="flex flex-b item">
							<div class="th-td">{{$t('dividend').txt6}}</div>
							<span class="shares-name">{{ $formatMoney(item.zhang, 0)  }}</span>
						</div>
						<div class="flex flex-b item">
							<div class="th-td">{{$t('dividend').txt7}}</div>
							<span class="shares-name">{{ $formatMoney(item.cj_num, 0)  }}</span>
						</div>
					</div>
				</div>
			</template>
		</div>
		<van-popup v-model="flagBuyPop" position="bottom">
			<div class="pop">
				<div class="pop-title">{{ stockObj.name + "/" + stockObj.symbol }}</div>
				<div class="pop-price text-center">
					<div class="txt">{{$formatMoney(stockObj.price)}}円</div>
					{{$t('dividend').txt9}}
				</div>
				<div class="pop-money">{{$t('dividend').txt10}}: {{$formatMoney(countMoney)}}円</div>
				<div class="pop-num">
					<input v-model="buyObj.handle" type="number" :placeholder="$t('dividend').txt12" @input="TypeInput($event)" />
				</div>
				<span @click="buyFn" class="btn-big">{{$t('dividend').txt13}}</span>
			</div>
		</van-popup>
		<div class="pop popLoad" v-if="!flagBuy"></div>
	</div>
</template>

<script>
	import Vue from "vue";
	import qs from "qs";
	import axios from "axios";
	import {
		Toast
	} from "vant";
	Vue.use(Toast);
	
	import top from "../bar/toper.vue";
	
	export default {
		name: "dividend",
		data() {
			return {
				flagBuy: true,
				flagBuyPop:false,
				text:this.$t('home').top10,
				type:0,//1狙擊當沖,0红利
				navList: [
					{ name: this.$t('dividend').menu1, type: 0 },
					{ name: this.$t('dividend').menu2, type: 1 }
				],
				currmentIndex:0,
				chooseList:[],
				myList:[
					// {
					// 	stock_name:'纳斯达克',
					// 	stock_code:'2003',
					// 	buy_price:7000,
					// 	zhang:5656,
					// 	cj_num:1111,
					// 	start:'2024/02/30',
					// 	end:'2024/02/30',
					// 	status:0,
					// }
				],
				stockObj: {},
				buyObj: {
					handle: null,
				},
				password: ''
			};
		},
		components: {
			top,
		},
		computed: {
			countMoney() {
				return this.stockObj.price * this.buyObj.handle
			}
		},
		destroyed() {},
		mounted() {
			this.type = this.$route.query.type;
			if(this.type==4){
				this.text = this.$t('home').top11;
			}else if(this.type==3){
				this.text = this.$t('home').top10;
			}
			this.getNew();
		},
		methods: {
			TypeInput(e) {
				// 只能输入数字的验证;
				const inputType = /[^\d]/g   //想限制什么类型在这里换换正则就可以了
				this.$nextTick(function() {
					this.buyObj.handle = e.target.value.replace(inputType, '');
				})
			},
			getNew() {
				this.$server.post('/trade/nbhllist', {
          dz_type:this.type,
          type: 'jpy'
				}).then(res => {
					if (res.data.status === 1) {
						this.chooseList = res.data.data;
						
					}
				})
			},
			getMine() {
				this.$server.post("/trade/ustockslist", {
          dz_type:this.type,
          type: 'jpy'
				}).then((res) => {
					this.myList = res.data.data;
				});
			},
			stockDetails(stock) {
				this.flagBuyPop = true;
				this.stockObj = stock;
			},
			changeNav(index) {
				this.currmentIndex = index;
				if (index) this.getMine();
				else this.getNew();
			},
			buyFn() {
				let _this = this;
				if(this.buyObj.handle<this.stockObj.stock_num){
					Toast({
						message: this.$t('dividend').txt17,
						duration: 2000,
					});
					return;
				}
				this.flagBuy = false;
				this.$forceUpdate();
				this.$server.post("/trade/buy_stock", {
          id: this.stockObj.id,
          symbol: this.stockObj.symbol,
          zhang: this.buyObj.handle,
          password:this.password,
          dz_type:this.type,
          is_qc:2,
          type: 'jpy',
          buyzd: 1,
          ganggan: 1,
				}).then((res) => {
					this.flagBuyPop = false;
					this.flagBuy = true;
					if (res.data.msg) {
						Toast({
							message: this.$t(res.data.msg),
							duration: 2000,
						});
					}
					if (res.data.status === 1) {
						setTimeout(function(){
							_this.clickNext('/position/position')
						},1000)
					}
				});
			}
		},
		
	};
</script>

<style lang="less">
	.dividend2{
		min-height:100vh;width:100%;
		background: #0F161C;
		padding-top: 0.47rem;
		
		.headf {
			background: #fff;
			position: fixed;z-index: 888;
			top: 0;
			left: 0;
			width: 100%;
			height:.44rem;
		}
		.nav-box {
			width: 100%;
			box-sizing: border-box;
			position: fixed;
			left: 0;
			top: .47rem;
			z-index: 10;
			padding: 0.1rem 0.12rem;
			.itemBox{
				width: 100%;
				height: 0.46rem;
				background: #424E4E;
				border-radius: 0.1rem;
				padding: 0.05rem;
			}
			.nav-item {
				width: 50%;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 0.14rem;
				color: #B3B8B8;
				text-align: center;
				line-height: 0.4rem;
			}
			.active {
				background: #5ED5A8;
				border-radius: 0.1rem;
				color: #000000;
				font-weight: 600;
			}
		}
		.inBox{
			margin:0.7rem 0rem 0;
			position: relative;
			z-index:200;
			.th{
				height: .44rem;
				display: flex;
				align-items: center;
				padding: 0 .17rem;
				background: #EFEFEF;
				border-radius: .05rem;
				.th-td{
					font-size: .13rem;
					font-family: FZLanTingHeiT-R-GB;
					font-weight: 400;
					color: #504B58;
					display: flex;
					align-items: center;
					justify-content: center;
					word-break: break-all;
				}
				.flex-start{
				  justify-content: flex-start;
				}
				.flex-end{
				  justify-content: flex-end;
				}
			}
			.list-item {
				.title{
					padding: 0.05rem 0.12rem;
					background-color: rgba(94, 213, 168, 0.2);
					border-bottom: .03rem solid #fff;
					font-family: PingFangSC, PingFang SC;
					font-weight: 600;
					font-size: 0.14rem;
					color: #FFFFFF;
					align-items: flex-end;
					.shares-code{
						margin-left: 0.05rem;
						font-size: 0.12rem;
					}
				}
				.list{
					margin: 0 0.12rem;
					.item{
						width: 48%;
						display: flex;
						align-items: center;
						justify-content: space-between;
						font-weight: 600;
						font-size: 0.11rem;
						color: #718A94;
						padding: .1rem 0;
						border-bottom: 0.01rem solid rgba(255, 255, 255, 0.2);
						.th-td{
							font-family: PingFangSC, PingFang SC;
							font-weight: 600;
							font-size: 0.11rem;
							color: #718A94;
						}
						.shares-price-num{
							font-family: PingFangSC, PingFang SC;
							font-weight: 600;
							font-size: 0.11rem;
							color: #FE0000;
						}
						.shares-name{
							font-family: PingFangSC, PingFang SC;
							font-weight: 600;
							font-size: 0.11rem;
							color: #FFFFFF;
						}
					}
				}
			}
		}
		.van-popup{
			border-radius: .12rem .12rem 0 0;
		}
		.pop{
			background: #424E4E;
			border-radius: .12rem .12rem 0 0;
			padding:.12rem;
			
			.pop-title{
				height: .6rem;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: .15rem;
				font-family: PingFangSC-Semibold, PingFang SC;
				font-weight: 600;
				color: #fff;
			}
			.pop-price{
				font-weight: 400;
				font-size: .14rem;
				color: #fff;
				.txt{
					margin-bottom: .1rem;
					font-weight: bold;
					font-size: .28rem;
					color: #FE0000;
				}
			}
			.pop-money{
				height: .3rem;
				display: flex;
				align-items: center;
				font-size: .15rem;
				color: #fff;
				margin-top: .1rem;
			}
			.pop-num{
				display: flex;
				align-items: center;
				font-size: .15rem;
				color: #727272;
				margin-top: .1rem;margin-bottom:.1rem;
				input{
					background: #000000;
					border-radius: 0.1rem;
					height: .52rem;width:100%;
					padding: 0 .1rem;
					color: #fff;margin-top:.1rem
				}
			}
			.btn-big{
				margin: .25rem auto;
			}
		}
	}
</style>