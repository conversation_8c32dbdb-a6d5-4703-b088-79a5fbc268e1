<template>
	<div class="page">
		<top-back :title="$t('實名認證')"></top-back>

		<template v-if="isEdit">
			<div class="editing">
				<!-- <div class="top">
					<div class="icon rzwc animate__animated animate__fadeIn"></div>
					<div class="t">
						{{ $t("new").a8 }}
					</div>
				</div> -->
				<div class="bg">
					<!-- <div class="txt">請依下列資料完成實名認證</div> -->
					<div class="ipt flex">
						<div class="flex">
							<img src="../../assets/v6/sco1.png" style="width: 0.22rem;height: 0.22rem;margin-right: 0.1rem;"/>
							<div class="t1">{{ $t('姓名') }}</div>
						</div>
						<input v-model="form.true_name" placeholder-style="color: #999" :placeholder="$t('請輸入姓名')" />
					</div>
					<div class="ipt flex">
						<div class="flex">
							<img src="../../assets/v6/sco2.png" style="width: 0.22rem;height: 0.22rem;margin-right: 0.1rem;"/>
							<div class="t1">{{ $t('身分證號') }}</div>
						</div>
						<input v-model="form.card_id" placeholder-style="color: #999" :placeholder="$t('請輸入身分證號')" />
					</div>
				</div>
				<div class="upload">
					<!-- <div class="t">請上傳您的身分證正反面照片</div> -->
					<div class="flex flex-b">
						<div class="item flex flex-c">
							<div v-if="!showFrontcard">
								<div class="icon sf1 animate__animated animate__fadeIn"></div>
								<div class="t1">{{ $t('正面') }}</div>
							</div>
							<img class="animate__animated animate__fadeIn" v-if="showFrontcard" :src="showFrontcard" />
							<input class="inp" accept="image/*" type="file" @change="uploadFile($event, 1)" />
						</div>
						<div class="item flex flex-c">
							<div v-if="!showBackcard">
								<div class="icon sf1 animate__animated animate__fadeIn"></div>
								<div class="t1">{{ $t('反面') }}</div>
							</div>
							<img class="animate__animated animate__fadeIn" v-if="showBackcard" :src="showBackcard" />
							<input class="inp" accept="image/*" type="file" @change="uploadFile($event, 2)" />
						</div>
					</div>
					<div class="defbtn animate__animated animate__fadeIn" @click="submit">{{ $t('提交') }}</div>
				</div>
			</div>
		</template>

		<template v-if="!isEdit">
			<div class="edited">
				<div class="bot">
					<div class="flex">
						<div class="flex">
							<img src="../../assets/v6/sco1.png" style="width: 0.22rem;height: 0.22rem;margin-right: 0.1rem;"/>
							<div class="t1">{{ $t('姓名') }}</div>
						</div>
						<div class="ipt flex-1">{{ form.true_name || "-" }}</div>
					</div>
					<div class="flex">
						<div class="flex">
							<img src="../../assets/v6/sco2.png" style="width: 0.22rem;height: 0.22rem;margin-right: 0.1rem;"/>
							<div class="t1">{{ $t('身分證號') }}</div>
						</div>
						<div class="ipt flex-1">{{ form.card_id || "-" }}</div>
					</div>
				</div>
				<div class="top">
					<!-- <div class="animate__animated animate__fadeIn flex flex-c">
						<img v-if="userInfo.is_true == 1" src="../../assets/v5/smrzIcon.png" style="width: 1.29rem;height: 1.23rem;"/>
						<van-icon v-if="userInfo.is_true == 1" name="checked" size="50" color="#7f7f7f" />
						<van-icon v-else name="clear" size="50" color="#7f7f7f" />
					</div> -->
					<div class="t">{{ userInfo.is_true == 1 ? $t("已實名") : $t("審核中") }}</div>
				</div>
			</div>
		</template>
		<!-- <div class="tips">
			您提交的資訊將確保其真實有效，我們不會與其他組織或個人分享您的訊息，以免給我們帶來系統性風險。
		</div> -->
		<loading ref="loading" />
	</div>
</template>

<script>
import {compress} from "../../assets/js/imgutils"
	export default {
		name: "authInfo",
		props: {},
		data() {
			return {
				isEdit: false,
				showFrontcard: "",
				showBackcard: "",
				form: {
					frontcard: "",
					backcard: "",
					true_name: "",
					card_id: "",
				},
				userInfo: {},
			};
		},
		components: {},
		created() {
			this.initData();
		},
		computed: {},
		methods: {
			initData() {
				this.$server.post("/user/getUserinfo", {
					type: "twd"
				}).then((res) => {
					if (res.status == 1) {
						this.userInfo = res.data;
						if (res.data.frontcard) {
							// this.showFrontcard = this.$server.url.imgUrls + res.data.frontcard;
							// this.showBackcard = this.$server.url.imgUrls + res.data.backcard;
						}
						this.form.true_name = res.data.realname;
						this.form.card_id = res.data.id_card;

						//0未认证 1已实名 2审核失败 3审核中
						if (res.data.is_true == 1 || res.data.is_true == 3) {
							this.isEdit = false;
						}
						if (res.data.is_true == 0 || res.data.is_true == 2) {
							this.isEdit = true;
						}
					}
				});
			},
			submit() {
				if (!this.form.true_name) {
					this.$toast(this.$t("請輸入姓名"));
					return;
				}
				if (!this.form.card_id) {
					this.$toast(this.$t("請輸入身分證號"));
					return;
				}

				if (!this.form.frontcard || !this.form.backcard) {
					this.$toast(this.$t("請上傳您的身分證照片"));
					return;
				}
				this.$refs.loading.open(); //开启加载

				this.$server
					.post("/user/shiming", {
						id_card: this.form.card_id,
						realname: this.form.true_name,
						frontcard: this.form.frontcard,
						backcard: this.form.backcard,
					})
					.then((res) => {
						this.$refs.loading.close();

						if (res.status == 1) {
							this.$toast(this.$translateServerText(res.msg));
							// this.initData();
              this.$router.go(-1)
						}
					});
			},
			uploadFile(e, type) {
				var file = e.target.files[0];
				var that = this;
        compress(file,{maxWidth:640},(file=> {
          var formdata = new FormData();
          formdata.append("card", file);

          this.$server
              .post("/common/upload1", formdata)
              .then((res) => {
                if (res.status == 1) {
                  this.$toast(this.$t('上傳成功'));
                  if (type == 1) {
                    // 正面
                    this.showFrontcard = this.$server.url.imgUrls + res.data; //显示用
                    this.form.frontcard = res.data; //提交用
                  } else {
                    // 反面
                    this.showBackcard = this.$server.url.imgUrls + res.data;
                    this.form.backcard = res.data;
                  }
                }
              })
              .catch((data) => {});
        }))
			},
		},
	};
</script>

<style scoped lang="less">
	.inp {
		position: absolute;
		width: 100%;
		height: 100%;
		left: 0;
		top: 0;
		opacity: 0;
	}

	.page {
		padding: 0.7rem 0.12rem 0.2rem;
		min-height: 100vh;

		.edited {
			background: linear-gradient(45deg, #B2E56E, #EFFDB0);
			border-radius: 0.13rem;
			padding: 0.12rem;
			.top {
				padding: 0.1rem 0;
				.icon {
					margin: 0 auto 0.15rem;
				}
				.t {
					height: 0.5rem;
					line-height: 0.5rem;
					text-align: center;
					background: rgba(255, 255, 255, 0.3);
					border-radius: 0.09rem;
					text-align: center;
					font-weight: 500;
					font-size: 0.16rem;
					color: #444444;
					margin-top: 0.1rem;
				}
			}
			.bot{
			}
			.t1 {
				font-family: PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
				color: #000000;
			}

			.ipt {
				text-align: right;
				height: 0.42rem;
				font-family: PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
				color: #000000;
				line-height: 0.42rem;
			}
		}

		.editing {
			background: linear-gradient(45deg, #B2E56E, #EFFDB0);
			border-radius: 0.13rem;
			padding: 0.12rem;
			.txt {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 0.14rem;
				color: #FFFFFF;
				margin-bottom: 0.15rem;
			}
			.top {
				padding: 0.3rem 0;
				.icon {
					margin: 0 auto 0.15rem;
				}
				.t {
					height: 0.5rem;
					background: rgba(255, 255, 255, 0.3);
					border-radius: 0.09rem;
					text-align: center;
					font-weight: 500;
					font-size: 0.16rem;
					color: #444444;
				}
			}
			.bg{
				background: rgba(255, 255, 255, 0.3);
				border-radius: 0.09rem;
				padding: 0.12rem;
				.txt{
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 0.16rem;
					color: #000000;
				}
			}
			.ipt {
				.t1 {
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 0.13rem;
					color: #000000;
				}
				input {
					flex: 1;
					text-align: right;
					height: 0.42rem;
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 0.13rem;
					color: #000000;
					line-height:  0.42rem;
					&::placeholder {
						font-family: PingFang TC, PingFang TC;
						font-weight: 500;
						font-size: 0.12rem;
						color: #B6B6B6;
					}
				}
			}

			.upload {
				margin-top:0.1rem;
				.t {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 0.16rem;
					color: #000000;
				}
				.item {
					width: 48%;
					margin: 0.1rem 0;
					padding: 0.3rem 0;
					background: #FFFFFF;
					border-radius: 0.16rem;
					position: relative;
					text-align: center;
					img {
						width: 100%;
						height: 100%;
						border-radius: 0.12rem;
						object-fit: contain;
					}
					.icon {
						margin: 0 auto 0.1rem;
					}
					.t1 {
						font-family: PingFang TC, PingFang TC;
						font-weight: 500;
						font-size: 0.12rem;
						color: #363636;
						text-align: center;
					}
				}
			}

			.b-btn {
				margin: 0.1rem 0;
			}
		}

		.tips {
			font-size: 0.12rem;
			line-height: 0.2rem;
			margin-top: 0.2rem;
			color: #7f7f7f;
		}
	}
</style>