<template>
  <div class="page ">
    <top-back :title="$t('new').b6"></top-back>

    <van-pull-refresh
      v-model="isLoading"
      @refresh="onRefresh"
      :loosing-text="$t('new').t3"
      :loading-text="$t('new').t4"
      :pulling-text="$t('new').t5"
    >
      <van-skeleton title :row="26" :loading="loading">
        <div class="cot">
          <div class="nav-box flex flex-b">
            <div
              class="nav-item"
              v-for="(item, index) in navList"
              :key="index"
              :class="{ active: currmentIndex == item.type }"
              @click="changeNav(item.type)"
            >
              {{ item.name }}
            </div>
          </div>

          <div class="list" v-if="currmentIndex == 0">
            <div
              class="list-item"
              v-for="(item, index) in chooseList"
              :key="index"
              @click="stockDetails(item)"
            >
              <div class="flex flex-b">
                <div class="">
                  <div class="name">{{ item.name }}</div>
                  <div class="code">{{ item.symbol }}</div>
                </div>

                <div class="st">{{ $t("买入") }}</div>
              </div>

              <div class="flex flex-b mtb10">
                <div class="tt">{{ $t("dividend").txt2 }}</div>

                <div class="price red">{{ $formatMoney(item.price) }}</div>
              </div>

              <div class="flex flex-b">
                <div class="tt">{{ $t("最小买入股数") }}</div>

                <div class="price">
                  {{ $formatMoney(item.stock_num) || "-" }}
                </div>
              </div>
            </div>
            <no-data v-if="!chooseList.length"></no-data>
          </div>

          <div class="list" v-else>
            <div
              class="list-items"
              v-for="(item, index) in myList"
              :key="index"
            >
              <div class="flex flex-b">
                <div>
                  <div class="name">
                    {{ item.stock_name }}
                  </div>
                  <div class="code">
                    {{ item.stock_code }}
                  </div>
                </div>

                <div class="st" :class="item.state != '审核中' ? 'sc' : ''">
                  {{ $t(item.state) }}
                </div>
              </div>

              <div class="inner flex flex-b">
                <div class="inner-item flex flex-b">
                  <div class="t">
                    {{ $t("dividend").txt5 }}
                  </div>
                  <div class="t1 red">
                    {{ $formatMoney(item.buy_price) }}
                  </div>
                </div>

                <div class="inner-item flex flex-b">
                  <div class="t">
                    {{ $t("dividend").txt6 }}
                  </div>
                  <div class="t1">
                    {{ $formatMoney(item.zhang) }}
                  </div>
                </div>

                <div class="inner-item flex flex-b">
                  <div class="t">
                    {{ $t("dividend").txt7 }}
                  </div>
                  <div class="t1">
                    {{ $formatMoney(item.cj_num) }}
                  </div>
                </div>
              </div>
            </div>

            <no-data v-if="!myList.length"></no-data>
          </div>
        </div>
      </van-skeleton>
    </van-pull-refresh>

    <van-popup v-model="show" position="center" :style="{ width: '90%' }">
      <div class="pop">
        <div class="pop-title ">{{ stockObj.name + "/" + stockObj.code }}</div>
        <div class="pop-price t-c">
          <div class="t">{{ $formatMoney(stockObj.price) || 0 }}</div>
          <div class="t1">{{ $t("newt").t12 }}</div>
        </div>

        <div class=" btips ">
          <div class="flex flex-b">
            <div class="t1 ">{{ $t("newt").t14 }}</div>
            <div class="t2 flex-1 t-r">
              {{ $formatMoney(countMoney) || 0 }}
            </div>
          </div>

          <div class="flex flex-b">
            <div class="t1">{{ $t("newt").t13 }}</div>
            <div class="t2 flex-1 t-r">
              {{ $formatMoney(userInfo.krw) || 0 }}
            </div>
          </div>
        </div>

        <div class="ipt ">
          <div>{{ $t("new").b46 }}</div>
          <input
            class="flex-1"
            v-model="buyObj.handle"
            type="number"
            :placeholder="$t('dividend').txt12"
            @input="TypeInput($event)"
          />
        </div>

        <!--        <div class="pop-num">-->
        <!--          <div>{{ $t("new").b47 }}</div>-->
        <!--          <input-->
        <!--            :placeholder="$t('new').t1"-->
        <!--            type="password"-->
        <!--            v-model="password"-->
        <!--          />-->
        <!--        </div>-->

        <div class="flex flex-b">
          <div class="b-btn bt" @click="show = false">
            {{ $t("取消") }}
          </div>

          <div @click="buyFn" class="b-btn">{{ $t("dividend").btn }}</div>
        </div>
      </div>
      <!-- <div class="icon close" @click="show = false"></div> -->
    </van-popup>
    <loading ref="loading" />
  </div>
</template>

<script>
export default {
  name: "blockTrade",
  data() {
    return {
      loading: true,
      isLoading: false,
      navList: [
        {
          name: this.$t("dividend").tab1,
          type: 0,
        },
        {
          name: this.$t("dividend").tab2,
          type: 1,
        },
      ],
      currmentIndex: 0,
      chooseList: [
        // {
        //   name: "名称",
        //   code: "007",
        //   price: "1000",
        //   stock_num: 1000,
        // },
      ],
      myList: [
        // {
        //   stock_name: "stock_name",
        //   stock_code: "stock_code",
        //   buy_price: "1000",
        //   zhang: "1000",
        //   cj_num: "1000",
        //   status: 1,
        //   state: "审核中",
        // },
      ],
      show: false,
      stockObj: {},
      buyObj: {
        handle: null,
      },
      password: "",
      userInfo: {},
      currentItem: {},
    };
  },
  computed: {
    countMoney() {
      return this.stockObj.price * this.buyObj.handle;
    },
  },
  created() {
    this.initData();
    this.getNew();
  },
  mounted() {},
  methods: {
    // 下拉刷新
    onRefresh() {
      this.initData();
      this.getNew();
    },
    initData() {
      this.$server.post("/user/getUserinfo", {}).then((res) => {
        if (res.status == 1) {
          this.userInfo = res.data;
        }
      });
    },
    TypeInput(e) {
      // 只能输入数字的验证;
      const inputType = /[^\d]/g; //想限制什么类型在这里换换正则就可以了
      this.$nextTick(function() {
        this.buyObj.handle = e.target.value.replace(inputType, "");
      });
    },
    getNew() {
      this.$server
        .post("/transaction/nbhllist", {
          type: 1,
        })
        .then((res) => {
          this.isLoading = false;
          this.loading = false;
          this.$refs.loading.close(); //关闭加载

          if (res.status == 1) {
            this.chooseList = res.data;
          }
        });
    },
    getMine() {
      this.$server
        .post("/transaction/ustockslist", {
          type: 1,
        })
        .then((res) => {
          this.$refs.loading.close(); //关闭加载

          if (res.status == 1) {
            this.myList = res.data;
          }
        });
    },
    stockDetails(stock) {
      this.show = true;
      this.currentItem = stock;
      this.$server
        .post("/transaction/nbhldetails", {
          symbol: stock.code,
        })
        .then((res) => {
          this.stockObj = res.data;
        });
    },
    changeNav(index) {
      this.currmentIndex = index;
      this.$refs.loading.open(); //开启加载

      if (index) this.getMine();
      else this.getNew();
    },
    buyFn() {
      if (!this.buyObj.handle) {
        this.$toast(this.$t("dividend").txt12);
        return;
      }
      // if (!this.password) {
      //   this.$toast(this.$t("new").t);
      //   return;
      // }
      this.$refs.loading.open(); //开启加载

      this.$server
        .post("/transaction/buy_stock", {
          symbol: this.stockObj.symbol,
          zhang: this.buyObj.handle,
          //password: this.password,
          buyzd: 1,
          ganggan: 1,
          type: 1,
          is_qc: 2,
          id: this.currentItem.id, //大宗增加传递参数，列表id
        })
        .then((res) => {
          this.$refs.loading.close();

          this.show = false;
          if (res.msg) {
            this.$toast(this.$t(res.msg));
          }
        });
    },
  },
};
</script>

<style scoped lang="less">
.page {
  padding: 0.5rem 0 0.1rem;
  min-height: 100vh;
}
::v-deep .van-skeleton__row{
  background-color:transparent !important;
}
::v-deep .van-skeleton__title{
  background-color:transparent !important;
}
.titles {
  padding: 0.1rem 0.1rem 0;
  border-bottom: 0.01rem solid #f5f5f5;

  div {
    font-size: 0.12rem;
    color: #464646;
  }
}

.van-popup {
  background-color: transparent;
}

.close {
  margin: 0.1rem auto 0;
}

.pop {
  background-color: #fff;
  padding: 0.15rem;
  border-radius: 0.08rem;
  position: relative;

  .btips {
    padding: 0.15rem 0;
    line-height: 0.24rem;

    .t2 {
      color: #a91111;
    }
  }

  .pop-title {
    font-size: 0.16rem;
    text-align: center;
  }

  .pop-price {
    padding: 0.2rem 0;

    .t {
      font-weight: 600;
      font-size: 0.32rem;
      color: #b63130;
    }

    .t1 {
      font-size: 0.12rem;
    }
  }

  .ipt {
    input {
      background: #f8f8f8;
      border-radius: 0.06rem;
      border: 0.01rem solid #b8b8b8;
      height: 0.4rem;
      line-height: 0.4rem;
      padding: 0 0.1rem;
      // margin-left: 0.1rem;
      margin-top: 0.1rem;
      width: 100%;

      &::placeholder {
        font-size: 0.12rem;
        color: #9a9fa5;
      }
    }
  }

  .pop-num {
    margin-top: 0.15rem;

    input {
      margin: 0.05rem 0;
      width: 100%;
      background: #f8f8f8;
      border-radius: 0.06rem;
      border: 0.01rem solid #b8b8b8;
      height: 0.4rem;
      line-height: 0.4rem;
      padding: 0 0.1rem;

      &::placeholder {
        font-size: 0.12rem;
        color: #606060;
      }
    }
  }

  .txt {
    font-size: 0.12rem;
    color: #9a9fa5;

    span {
      font-size: 0.12rem;
      color: #c5585e;
    }
  }

  .b-btn {
    margin: 0.2rem 0 0;
    width: 48%;
    padding: 0.1rem 0;
    border-radius: 0.04rem;
    &.bt {
      background-color: #fff;
      border: 0.01rem solid #000000;
      color: #000;
    }
  }
}

.cot {
  .title {
    padding: 0.1rem;

    div {
      font-weight: 500;
      font-size: 0.12rem;
      color: #666666;
    }
  }

  .list {
    padding: 0.15rem 0.1rem;
  }

  .list-item {
    padding: 0.1rem;
    background: #ffffff;
    box-shadow: 0rem 0.02rem 0.04rem 0rem rgba(179, 179, 179, 0.25),
      0rem -0.02rem 0.04rem 0rem rgba(218, 218, 218, 0.25);
    border-radius: 0.06rem 0.06rem 0.06rem 0.06rem;
    margin-bottom: 0.1rem;
    .name {
      font-weight: bold;
      font-size: 0.12rem;
    }

    .code {
      font-size: 0.1rem;
      color: #c4c4c4;
    }

    .mtb10 {
      margin: 0.1rem 0;
    }

    .tt {
      color: #8e8e8e;
      font-size: 0.12rem;
    }

    .price {
      font-size: 0.12rem;
    }

    .red {
      color: #a91111;
    }

    .st {
      background: #000000;
      border-radius: 0.04rem;
      padding: 0.05rem 0.15rem;
      font-weight: 500;
      font-size: 0.12rem;
      color: #ffffff;
    }

    .t {
      font-weight: bold;
    }
  }

  .list-items {
    padding: 0.1rem;
    background: #ffffff;
    box-shadow: 0rem 0.02rem 0.04rem 0rem rgba(179, 179, 179, 0.25),
      0rem -0.02rem 0.04rem 0rem rgba(218, 218, 218, 0.25);
    border-radius: 0.06rem 0.06rem 0.06rem 0.06rem;
    margin-bottom: 0.1rem;

    .name {
      font-weight: bold;
      font-size: 0.12rem;
    }

    .code {
      font-size: 0.1rem;
      color: #c4c4c4;
    }

    .st {
      background: #db8a2b;
      border-radius: 0.04rem;
      padding: 0.05rem 0.15rem;
      font-weight: 500;
      font-size: 0.12rem;
      color: #ffffff;
      &.sc {
        background: #6bb831;
      }
    }

    .inner {
      flex-wrap: wrap;
      padding: 0.1rem 0 0;

      .inner-item {
        width: 100%;
        line-height: 0.24rem;
        .t {
          font-size: 0.12rem;
          color: #8e8e8e;
        }

        .t1 {
          font-size: 0.12rem;
        }

        .red {
          color: #a91111;
        }
      }
    }
  }
}

.nav-box {
  // padding: 0 0.1rem;
  background: #ffffff;
  // box-shadow: -0.01rem 0.02rem 0.02rem 0rem rgba(175, 175, 175, 0.25);

  .nav-item {
    padding: 0.1rem 0;
    flex: 1;
    font-size: 0.12rem;
    color: #a1a1a1;
    text-align: center;
    position: relative;

    &::after {
      content: "";
      width: 100%;
      height: 0.01rem;
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      background: transparent;
    }

    &.active {
      color: #000000;
      &::after {
        background: #000000;
      }
    }
  }
}
</style>
