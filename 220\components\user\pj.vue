<template>
  <div style="min-height: 100vh;background-color: #F2F2F2;">
    <div class="bijnm1">
      <div class="pj">
        <div style="width: 33.3%;text-align: left;">
          <img @click="fanhui" src="../../assets/skin/login/back.png" />
        </div>
        <div style="width: 33.3%;text-align: center;">
          {{ $t("信用评级") }}
        </div>
        <div @click="jkjl" style="width: 33.3%;text-align: right;">
          {{ $t("借款记录") }}
        </div>
      </div>
      <div style="margin: auto;width: 1.82rem;margin-top: .3rem;">
        <van-circle
          :clockwise="true"
          v-model="currentRate"
          size="1.82rem"
          :stroke-width="120"
          :color="gradientColor"
        />
      </div>
      <div style="text-align: center;color: #fff;margin-top: -1.2rem;">
        <div style="font-size: .4rem;font-weight: bold;height: .44rem;">
          {{ userdata.xyf }}
        </div>
        <p style="margin-top: .1rem;color: #f5f5f5;font-size: .15rem;">
          {{ $t("信用分") }}
        </p>
      </div>
      <img
        src="../../assets/dikuang.png"
        style="width: 3.2rem;height: 2rem;display: block;margin: auto;margin-top: .64rem;"
      />
      <div style="margin-top: -1.7rem;">
        <div style="margin-bottom: .3rem;">
          <div
            style="text-align: center;font-size: .3rem;color: #333;margin-bottom: .15rem;"
            v-show="userdata.riyuankjed == null"
          >
            0.00
          </div>
          <div
            style="text-align: center;font-size: .3rem;color: #333;margin-bottom: .15rem;"
            v-show="userdata.riyuankjed != null"
          >
            <!-- {{(userdata.kjed)}} -->
            {{ Number(userdata.riyuankjed).toFixed(2) }}
          </div>
          <div style="text-align: center;font-size: .14rem;color: #999;">
            {{ $t("信用额度") }}
          </div>
        </div>
        <div
          style="display: flex;align-items: center;justify-content: space-between;"
        >
          <div style="width: 50%;text-align: center;">
            <div style="font-size: .18rem;color: #333;margin-bottom: .15rem;">
              <!-- {{(userdata.kjed - userdata.yjed).toFixed(2)}} -->
              {{ (userdata.riyuankjed - userdata.riyuanyjed).toFixed(2) }}
            </div>
            <div style="font-size: .14rem;color: #999;">
              {{ $t("可借额度") }}
            </div>
          </div>
          <div style="width: 50%;text-align: center;">
            <div
              style="font-size: .18rem;color: #333;margin-bottom: .15rem;"
              v-show="userdata.riyuanyjed == null"
            >
              0.00
            </div>
            <div
              style="font-size: .18rem;color: #333;margin-bottom: .15rem;"
              v-show="userdata.riyuanyjed != null"
            >
              {{ Number(userdata.riyuanyjed).toFixed(2) }}
            </div>
            <div style="font-size: .14rem;color: #FF5A0D;">
              {{ $t("已借额度") }}
            </div>
          </div>
        </div>
      </div>
      <div class="btns" v-if="false">
        <div class="btn btn1" @click="btn1">{{ $t("借款") }}</div>
        <div class="btn btn2" @click="btn2">{{ $t("还款") }}</div>
      </div>
    </div>
    <van-dialog
      v-model="show1"
      :title="$t('借款')"
      :beforeClose="beforeClose1"
      :confirmButtonText="$t('确认')"
      :cancelButtonText="$t('取消')"
      show-cancel-button
    >
      <van-field
        type="number"
        v-model="jkVal"
        :placeholder="$t('请输入借款金额')"
      />
    </van-dialog>
    <van-dialog
      v-model="show2"
      :title="$t('还款')"
      :beforeClose="beforeClose2"
      show-cancel-button
    >
      <van-field
        type="number"
        v-model="hkVal"
        :placeholder="$t('请输入还款金额')"
      />
    </van-dialog>
  </div>
</template>
<script type="text/javascript">
import Vue from "vue";
import qs from "qs";
import axios from "axios";
import top from "../bar/toper.vue";
import { Toast } from "vant";
Vue.use(Toast);
import { Circle } from "vant";
Vue.use(Circle);
import { Dialog } from "vant";
Vue.use(Dialog);
export default {
  name: "BankCard",
  data() {
    return {
      text: "信用评级",
      currentRate: 0,
      gradientColor: {
        "0%": "#FF560C",
        "100%": "#FFC71D",
      },
      userdata: {},
      show1: false,
      jkVal: "",
      show2: false,
      hkVal: "",
    };
  },
  components: {
    top,
  },
  methods: {
    jkjl() {
      this.$router.push({ path: "/user/jkjl" });
    },
    fanhui() {
      this.$router.go(-1);
    },
    getUserinfo() {
      this.$server.post("/user/getUserinfo").then((str) => {
        if (str.data.status == 1) {
          this.userdata = str.data.data;
          this.currentRate = str.data.data.xyf;
        }
      });
    },
    btn1() {
      this.jkVal = "";
      this.show1 = true;
    },
    btn2() {
      this.hkVal = "";
      Dialog.alert({
        message: this.$t("请联系客服经理确认还款事宜！"),
        confirmButtonText: this.$t("确认"),
        cancelButtonText: this.$t("取消"),
      }).then(() => {
        // on close
      });
      //this.show2 = true;
    },
    beforeClose1(action, done) {
      if (action === "confirm") {
        setTimeout(done, 1000000);
        //done();
        this.$server
          .post("/user/to_jq", {
            money: this.jkVal,
            type: "riyuan",
          })
          .then((str) => {
            if (str.data.status == 1) {
              done();
              Toast({
                message: this.$t(str.data.msg),
                duration: 3000,
              });
              this.getUserinfo();
            } else {
              done();
              Toast({
                message: this.$t(str.data.msg),
                duration: 3000,
              });
            }
          });
      } else {
        done();
      }
    },
    beforeClose2(action, done) {
      Dialog.alert({
        title: "标题",
        message: "弹窗内容",
      }).then(() => {
        // on close
      });
      return;
    },
  },
  destroyed() {},
  mounted() {
    this.getUserinfo();
  },
};
</script>
<style type="text/css" lang="less">
.btns {
  display: flex;
  width: 3.2rem;
  margin: auto;
  margin-top: 0.6rem;
  justify-content: space-between;
  align-items: center;
  .btn {
    width: 1.5rem;
    height: 0.44rem;
    border-radius: 5px;
    font-size: 0.16rem;
    color: #fff;
    text-align: center;
    line-height: 0.44rem;
  }
  .btn1 {
    background: linear-gradient(233deg, #f36218, #f59934);
  }
  .btn2 {
    background: linear-gradient(233deg, #f36218, #f59934);
  }
}
.bijnm1 {
  height: 3.75rem;
  //background: linear-gradient(233deg, #f36218, #f59934);
  background: linear-gradient(90deg, #4b9dd6, #2f38d5);
  border-radius: 0px 0px 10px 10px;
}
.pj {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.1rem;
  color: #fff;
  font-size: 0.16rem;
  img {
    width: 0.19rem;
    height: 0.19rem;
  }
}
.xxxxxxx p {
  line-height: 0.25rem;
  text-align: left !important;
  font-size: 0.16rem !important;
}
input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  color: #999;
  font-size: 0.15rem;
}
.bijnm {
  background: #fff;
  min-height: 100vh;
}

.headf {
  width: 100%;
  height: 0.44rem;
  background: linear-gradient(233deg, #f36218, #f59934);
}
.hezi {
  width: 3.45rem;
  border-bottom: 0.01rem solid #e0e0e0;
  margin: 0 auto;
  margin-top: 0.44rem;
  text-align: center;
  padding-bottom: 0.2rem;
  img {
    width: 0.6rem;
    height: 0.6rem;
    border-radius: 50%;
  }
  h6 {
    color: #333333;
  }
  p {
    color: #999999;
  }
}
</style>
