<template>
	<div class="subscribeRecord">
		<div class="headf">
			<top :title="$t(text)"></top>
		</div>
		<div class="list" v-if="xinguList.length">
			<div class="item" v-for="(item,idx) in xinguList" :key="idx"
				v-if="(type==0&&item.xgstate!='已中签')||(type==1&&item.xgstate=='已中签')">
				<div class="title flex flex-b">
					<div class="name">{{item.stock_name}}</div>
					<div class="time">{{ $formatDate('DD/MM/YY hh:mm',item.buy_time) }}</div>
				</div>
				<div class="cont flex flex-b flex-wrap">
					<div class="col flex flex-b">
						{{$t('position').p3}}<span>{{ $formatMoney(item.apply_total, 0) }}</span>
					</div>
					<div class="col flex flex-b">
						{{$t('position').p2}}<span>{{ $formatMoney(item.lucky_total, 0) }}</span>
					</div>
					<div class="col flex flex-b">
						{{$t('position').p4}}<span>{{ $formatMoney(item.rjmoney) }}円</span>
					</div>
					<div class="col flex flex-b">
						{{$t('position').a6}}<span>{{ $formatMoney(item.market_value) }}円</span>
					</div>
					<div class="col flex flex-b">
						{{$t('position').p1}}<span>{{$formatMoney(item.apply_price)}}円</span>
					</div>
				</div>
				<div class="flex status" v-if="type==0">{{item.xgstate}}</div>
				<div class="flex status status02" v-if="type==1">{{item.xgstate}}</div>
			</div>
		</div>
		<div class="noData text-center" v-else>
			{{$t('transactionRecord').txt4}}
		</div>
	</div>
</template>

<script>
	import Vue from "vue";
	import qs from "qs";
	import axios from "axios";
	import {
		Toast
	} from "vant";
	Vue.use(Toast);

	import top from "../bar/toper.vue";

	export default {
		name: "subscribeRecord",
		data() {
			return {
				text: this.$t('subscribe').title,
				type: null,
				xinguList: []
			};
		},
		components: {
			top,
		},
		destroyed() {},
		mounted() {
			if (this.$route.query.type) {
				this.type = JSON.parse(this.$route.query.type);
				if (this.type == 0) {
					this.text = this.$t('subscribe').txt12;
				} else if (this.type == 1) {
					this.text = this.$t('subscribe').txt13;
				}
				this.getList();
			}
		},
		methods: {
			getList() {
				this.$server.post("/trade/usernewstocklist", {
          type: 'jpy', buy_type: 0
				}).then((res) => {
					if (res.data.status === 1) {
						var arr = [];
						for (var i in res.data.data) {
							var row = res.data.data[i];
							if (this.type == 0 && row.xgstate != '已中签') {
								arr.push(row);
							} else if (this.type == 1 && row.xgstate == '已中签') {
								arr.push(row);
							}
						}
						this.xinguList = arr;

					}
				});
			}
		},

	};
</script>

<style lang="less">
	.subscribeRecord {
		padding-top: .44rem;
		min-height: 100vh;
		width: 100%;
		background: #0F161C;

		.headf {
			position: fixed;
			z-index: 888;
			top: 0;
			left: 0;
			width: 100%;
			height: .44rem;
			border-bottom: .01rem solid #DCDCE0;
		}

		.list {
			margin-top: 0.03rem;
			position: relative;
			z-index: 20;

			.item {
				.title {
					box-sizing: border-box;
					padding: 0.05rem 0.12rem;
					background-color: rgba(94, 213, 168, 0.2);
					border-bottom: .03rem solid #fff;

					.name {
						font-family: PingFangSC, PingFang SC;
						font-weight: 600;
						font-size: 0.14rem;
						color: #FFFFFF;
					}

					.time {
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 0.13rem;
						color: #FFFFFF;
					}
				}

				.cont {
					padding: .1rem 0.12rem;

					.col {
						width: 48%;
						display: flex;
						align-items: center;
						justify-content: space-between;
						font-weight: 600;
						font-size: 0.11rem;
						color: #718A94;
						padding: .1rem 0;
						border-bottom: 0.01rem solid rgba(255, 255, 255, 0.2);

						span {
							font-weight: 600;
							font-size: 0.11rem;
							color: #FFFFFF;
						}
					}
				}

				.status {
					margin: 0 0.12rem 0.1rem;
					height: .32rem;
					background: #5ED5A8;
					border-radius: 0.05rem;
					display: flex;
					align-items: center;
					justify-content: center;
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.12rem;
					color: #000;
				}

				.status02 {
					background: rgba(94, 213, 168, 0.2);
					color: #FFFFFF;
				}
			}
		}

		.noData {
			margin-top: 1rem;
			font-size: .12rem;
			color: #999;
		}
	}
</style>