<template>
  <div class="page ">
    <top-back :title="$t('exchange').title"></top-back>

    <div class="head-box">
      <template v-if="currmentIndex === 'USD'">
        <div class="head-title">
          <div class="name flex">
            <div class="icon thb animate__animated animate__fadeIn"></div>
            THB
          </div>
          <div class="num">
            {{ $t("exchange").txt1 }}{{ parseFloat(userInfo.baht).toFixed(2) }}
          </div>
        </div>
        <div class="head-num">
          <input
            type="number"
            v-model="moneyTWD"
            @input="setUSD"
            :placeholder="$t('exchange').txt2"
          />
        </div>
      </template>
      <template v-if="currmentIndex === 'THB'">
        <div class="head-title">
          <div class="name flex">
            <div class="icon usd animate__animated animate__fadeIn"></div>
            USD
          </div>
          <div class="num">
            {{ $t("exchange").txt1
            }}{{ parseFloat(userInfo.dollar).toFixed(2) }}
          </div>
        </div>
        <div class="head-num">
          <input
            type="number"
            v-model="moneyUSD"
            @input="setTWD"
            :placeholder="$t('exchange').txt2"
          />
        </div>
      </template>

      <div
        class="icon qh animate__animated animate__fadeIn"
        @click="changeMoney"
      ></div>

      <template v-if="currmentIndex === 'USD'">
        <div class="head-title">
          <div class="name flex">
            <div class="icon usd"></div>
            USD
          </div>
          <div class="num">
            {{ $t("exchange").txt1
            }}{{ parseFloat(userInfo.dollar).toFixed(2) }}
          </div>
        </div>
        <div class="head-num">
          <input
            type="number"
            v-model="moneyUSD"
            @input="setTWD"
            :placeholder="$t('exchange').txt2"
          />
        </div>
      </template>
      <template v-if="currmentIndex === 'THB'">
        <div class="head-title">
          <div class="name flex">
            <div class="icon thb"></div>
            THB
          </div>
          <div class="num">
            {{ $t("exchange").txt1 }}{{ parseFloat(userInfo.taibi).toFixed(2) }}
          </div>
        </div>
        <div class="head-num">
          <input
            type="number"
            v-model="moneyTWD"
            @input="setUSD"
            :placeholder="$t('exchange').txt2"
          />
        </div>
      </template>

      <div class="cot">
        <div class="head-tip" v-if="currmentIndex === 'USD'">
          1(THB)={{ changeUSD }}(USD)
        </div>
        <div class="head-tip" v-if="currmentIndex === 'THB'">
          1(USD)={{ changeTWD }}(THB)
        </div>
      </div>
    </div>
    <div class="b-btn animate__animated animate__fadeIn" @click="chongzhi">
      {{ $t("exchange").txt3 }}
    </div>
    <loading ref="loading" />
  </div>
</template>
<script>
export default {
  name: "exChange",
  data() {
    return {
      currmentIndex: "USD",
      moneyUSD: "",
      moneyTWD: "",
      userInfo: {},
      changeUSD: 0,
      changeTWD: 0,
    };
  },
  computed: {},
  mounted() {
    this.getConfig();
    this.getUserInfo();
  },
  methods: {
    getUserInfo() {
      this.$server.post("/user/getUserinfo", {}).then((res) => {
        if (res.status == 1) {
          this.userInfo = res.data;
        }
      });
    },
    getConfig() {
      this.$server.post("/common/config", {}).then((res) => {
        if (res.status == 1) {
          let list = res.data;
          let listLength = list.length;
          let a;
          for (a = 0; a < listLength; a++) {
            if (list[a].name === "bahttodollar") {
              this.changeUSD = list[a].value;
            }
            if (list[a].name === "dollartobaht") {
              this.changeTWD = list[a].value;
            }
          }
        }
      });
    },
    setUSD() {
      this.moneyUSD = parseFloat(this.moneyTWD) * parseFloat(this.changeUSD);
    },
    setTWD() {
      this.moneyTWD = parseFloat(this.moneyUSD) * parseFloat(this.changeTWD);
    },
    changeMoney() {
      if (this.currmentIndex === "USD") {
        this.currmentIndex = "THB";
      } else {
        this.currmentIndex = "USD";
      }
    },
    chongzhi() {
      if (this.userInfo.is_true != 1) {
        this.$toast(this.$t("exchange").tip1);
        return;
      }
      if (this.moneyUSD || this.moneyTWD) {
        let pre = {};
        if (this.currmentIndex === "USD") {
          pre = {
            money: this.moneyTWD,
            from: "baht",
            to: "dollar",
          };
        } else {
          pre = {
            money: this.moneyUSD,
            from: "dollar",
            to: "baht",
          };
        }
        this.$refs.loading.open(); //开启加载

        this.$server.post("/user/rotation", pre).then((res) => {
          this.$refs.loading.close();
          if (res.status == 1) {
            this.$toast(this.$translateServerText(res.msg));

            this.getUserInfo();
            this.money = "";
          }
        });
      } else {
        this.$toast(this.$t("exchange").tip3);
      }
    },
  },
};
</script>

<style scoped lang="less">
.page {
  background: #f8f8f8;
  padding: 0.6rem 0.1rem 0;
  min-height: 100vh;
}

.head-box {
  background: #ffffff;
  box-shadow: 0rem 0rem 0.14rem 0rem #eeeeee;
  border-radius: 0.1rem;
  padding: 0.2rem 0.1rem;
}
.head-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .name {
    font-size: 0.17rem;
    color: #333333;
    .icon {
      margin-right: 0.1rem;
    }
  }
  .num {
    font-size: 0.14rem;
    color: #666666;
  }
}
.head-num {
  margin-top: 0.15rem;
  input {
    width: 100%;
    height: 0.5rem;
    background: #f5f5f5;
    border-radius: 0.1rem;
    padding: 0 0.1rem;
    border: 0;
    &::placeholder {
      color: #999;
    }
  }
}
.qh {
  margin: 0.2rem auto;
}

.head-tip {
  margin-top: 0.2rem;
  font-size: 0.14rem;
  color: #f53333;
}
.b-btn {
  margin: 0.2rem 0;
}
</style>
