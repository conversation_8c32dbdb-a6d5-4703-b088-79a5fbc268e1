 <template>
	<div>
	<van-popup v-model="Marshow" position="top" class="pupbg" :close-on-click-overlay='false' @click-overlay='change'>
	
		<div class="kun">
			<h6 class="htit">
				<span class="fank" @click="change"></span>
				保证金明细
			</h6>
			<ul class="shuju">
				<li>
					<h6>持仓总冻结（元）</h6>
					<p>0.00</p>
				</li>
				<li>
					<h6>大宗交易冻结（元）</h6>
					<p>0.00</p>
				</li>
				<li>
					<h6>新股配售冻结（元）</h6>
					<p>0.00</p>
				</li>
				<li>
					<h6>新股申购冻结（元）</h6>
					<p>0.00</p>
				</li>
				<li>
					<h6>提现申请冻结（元）</h6>
					<p>0.00</p>
				</li>
				<li>
					<h6>抢筹冻结（元）</h6>
					<p>0.00</p>
				</li>
			</ul>
			
			<div class="button">已显示全部</div>
		</div>
	
		
	</van-popup>
	</div>
</template>
<script type="text/javascript">
import Vue from 'vue';
import qs from 'qs';
import axios from 'axios';
import { Popup  } from 'vant';
Vue.use(Popup );

export default {
	name:"Margin",
	data(){
		return {
			
			
		}
	},
	props:['Marshow'],
	components:{
		
	},
	methods:{
		change(){
			this.$emit('closeMar')
		}
		
	},
	destroyed() {
		
	},
	mounted(){
		
	},
}
</script>
<style type="text/css" lang="less" scoped="scoped">

input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder{
  color: #CCC;
  font-size: 0.12rem;
}
.kun{
	width:3.47rem;
	margin:0 auto;
	margin-top:1rem;
	background: #11151d;
	padding-bottom:0.26rem;
	border-radius: 0.06rem;
	margin-bottom:0.8rem;
	overflow: hidden;
	.htit{
		height:0.58rem;
		background: #1c222e;
		font-weight: 500;
		text-align: center;
		line-height: 0.58rem;
		color:#fff;
		font-size:0.15rem;
		position: relative;
		.fank{
			position: absolute;
			background: url(../../assets/images/xfh.png)no-repeat center;
			background-size:100%;
			width:0.11rem;
			height:0.15rem;
			left:0.1rem;
			top:0.21rem;
		}
	}
	.shuju{
		width:2.77rem;
		margin:0 auto;
		margin-top:0.4rem;
		li{
			width:100%;
			border-bottom:0.01rem solid #ccc;
			margin-bottom:0.15rem;
			color:#ccc;
			font-size:0.14rem;
			font-weight:600;
			p{
				margin-top:0.1rem;
				padding-bottom:0.03rem;
			}
		}
	}
	.button{
		width:1.86rem;
		height:0.35rem;
		border-radius: 0.18rem;
		background: #e93f3e;
		color:#fff;
		margin:0 auto;
		margin-top:0.25rem;
		font-size:0.15rem;
		text-align: center;
		line-height: 0.35rem;
	}
}
</style>
