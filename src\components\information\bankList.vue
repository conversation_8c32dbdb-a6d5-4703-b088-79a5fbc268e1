<template>
	<div class="page ">
		<top-back title="銀行帳戶" :isList="true"></top-back>
		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<div class="cot">
				<!-- <div class="flex flex-b top">
					<div class="t">我的卡 (共{{ bankList.length }}張)</div>
				</div> -->
				<div class="list">
					<!-- <no-data v-if="!bankList.length"></no-data> -->
					<div class="item" v-for="(item, index) in bankList" :key="index">
						<div class="flex">
							<img src="../../assets/v6/yl.png" style="width: 0.25rem;height: 0.15rem;" alt="" />
							<div class="name">{{ item.bank_name }}</div>
						</div>
						<div class="num">{{item.bank_num}}</div>
						<div class="jc flex flex-e">
							<div class="icon del animate__animated animate__fadeIn" @click="delbakcard(item.id)" style="margin-right: 0.05rem;"></div>
							解除
						</div>
						<!-- <div class="name">{{ item.bank_code }} ({{item.bank_address}})</div> -->
						<!-- <div class="flex flex-b">
							<div class="flex">
								<div class="t1">戶名：</div>
								<div class="t">{{ item.realname }}</div>
							</div>
							<div class="flex">
								<div class="t1">帳號：</div>
								<div class="t">{{ item.bank_num }}</div>
							</div>
						</div> -->
					</div>
				</div>
				<!-- <div class="nodata" v-if="!bankList.length">
					<div class="flex flex-c">
						<img src="../../assets/v5/bankImg.png" style="width: 1.05rem;height: 1.05rem;margin-bottom: 0.3rem;" alt="" />
					</div>
					<div class="defbtn" @click="$toPage('/information/addBankCard')">添加銀行卡帳號
					</div>
				</div> -->
				<div class="defbtn" @click="$toPage('/information/addBankCard')">
					添加銀行卡帳號
				</div>
			</div>
		</van-pull-refresh>
		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "bankList",
		props: {},
		data() {
			return {
				isLoading: false,
				bankList: [
					// {
					// 	bank_address:'bank_address',
					// 	bank_code:'bank_code',
					// 	bank_name:'bank_name',
					// 	bank_num:123333,
					// 	realname:'realname',
					// },
					// {
					// 	bank_address:'bank_address',
					// 	bank_code:'bank_code',
					// 	bank_name:'bank_name',
					// 	bank_num:123333,
					// 	realname:'realname',
					// },
				],
				flag: false,
			};
		},
		components: {},
		created() {
			this.initData();
		},
		computed: {
			formatNum() {
				return (value) => {
					let str = value.slice(0, 4);
					return `${str} **** **** ****`;
				};
			},
		},
		mounted() {
			// this.$refs.firstLoading.open();
		},
		methods: {
			onRefresh() {
				this.initData();
			},
			delbakcard(bankid) {
				if (this.flag) return;
				this.$refs.loading.open(); //开启加载
				this.flag = true;
				this.$server
					.post("/user/delbakcard", {
						bankid,
						type: "twd"
					})
					.then((res) => {
						this.$refs.loading.close();

						if (res.status == 1) {
							this.$toast(res.msg);
							setTimeout(() => {
								this.initData();
							}, 1500);
						}
					});
			},
			initData() {
				this.$server
					.post(
						"/user/cardList", {
							size: 100,
							page: 1,
							type: "twd"
						},
						(failres) => {
							that.bankList = [];
						}
					)
					.then((res) => {
						// this.$refs.firstLoading.close();
						this.isLoading = false;
						this.flag = false;
						var arr = [];
						for (var i in res.data) {
							var row = res.data[i];
							if (row.bank_name != "TRC" && row.bank_name != "ERC") {
								arr.push(row);
							}
						}
						this.bankList = arr;
					});
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.5rem 0 0.2rem;
		min-height: 100vh;

		.padd {
			padding: 0.1rem 0;

			.b-btn {
				margin: 0;
				font-size: 0.12rem;
				color: #888888;
				background-color: transparent;

				.icon {
					margin-right: 0.1rem;
				}
			}
		}

		.cot {
			.top {
				padding: 0.2rem 0.12rem;
				.t {
					font-weight: 600;
					font-size: 0.16rem;
					color: #ffffff;
				}
			}

			.defbtn {
				margin: 0.12rem;
			}
			.nodata{
				margin: 0.12rem;
				background: #FFFFFF;
				box-shadow: 0rem 0.04rem 0.04rem 0rem rgba(0,0,0,0.12), 0rem -0.02rem 0.04rem 0rem rgba(123,187,161,0.25);
				border-radius: 0.06rem 0.06rem 0.06rem 0.06rem;
				padding: 0.5rem 0.12rem;
			}
			.list {
				margin: 0.12rem;
				.item {
					position: relative;
					width: 100%;
					margin-bottom: 0.15rem;
					background: #232429;
					border-radius: 0.09rem;
					padding: 0.12rem;
					.name{
						margin-left: 0.1rem;
						font-family: PingFang SC;
						font-weight: 400;
						font-size: 0.14rem;
						color: #FFFFFF;
					}
					.jc{
						font-family: PingFang SC;
						font-weight: 400;
						font-size: 0.13rem;
						color: #666666;
					}
					.num {
						padding-left: 0.35rem;
						margin-top: 0.1rem;
						font-family: Arial Black;
						font-weight: 400;
						font-size: 0.16rem;
						color: #FFFFFF;
					}
					.t {
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 0.12rem;
						color: #FFFFFF;
					}
				}
			}
		}
	}
</style>