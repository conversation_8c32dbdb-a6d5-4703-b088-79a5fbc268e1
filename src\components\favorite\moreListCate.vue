<template>
	<!-- 分类单独跳转 更多 -->
	<div class="page ">
		<top-back :title="mName"></top-back>

		<!-- 切換 -->
		<!-- <div class="change flex flex-b">
			<div class="change-item" v-for="(item, index) in sort" :class="{ active: item.id === sortIndex }"
				:key="index" @click="changeSort(item.id)">
				{{ item.name }}
			</div>
		</div> -->

		<div class="top-fixed">
			<div class="top" @click="$toPage('/favorite/search')">
				<div class="flex flex-b  search">
					<div class="icon sou animate__animated animate__fadeIn"></div>
					<div class="ipt flex-1">股票名稱/股票代碼</div>
				</div>
			</div>

			<!-- 市場切換 -->
			<div class="m-tab flex flex-b" v-if="false">
				<div class="m-item flex-1 t-c" v-for="(item, index) in tab" :class="{ active: item.type == mType }"
					:key="index" @click="changeMarket(item.type)">
					{{ item.name }}
				</div>
			</div>

			<div class="bg">
				<!-- 分類切換 -->
				<div class="change flex flex-b">
					<div class="change-item" v-for="(item, index) in mList"
						:class="{ active: item.sectorId == sortIndex }" :key="index" @click="changeSort(item.sectorId)">
						{{ item.name }}
					</div>
				</div>
			</div>
		</div>

		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<van-skeleton title :row="26" :loading="loading">
				<div class="rm">
					<!-- 列表 -->
					<div class="rm-list">
						<div class="titles flex flex-b">
							<div class="flex-1">名稱</div>
							<div class="flex-1 t-c">價格</div>
							<div class="flex-1 t-c flex flex-c">
								成交量
								<div class="icon" :class="show ? 'zq' : 'dq'" @click="changeList(0)"></div>
							</div>
							<div class="flex-1 t-r flex flex-e">
								漲跌
								<div class="icon" :class="show1 ? 'zq' : 'dq'" @click="changeList(1)"></div>
							</div>
						</div>

						<no-data v-if="!list.length"></no-data>

						<div class="rm-item flex flex-b" v-for="(item, i) in list" :key="i" @click="
                $toDetail(`/market/stockDetail?symbol=${item.systexId}`, item)
              ">
							<div class="flex-1">
								<div class="name">{{ item.symbolName }}</div>

								<div class="code">{{ item.systexId }}</div>
							</div>
							<div class="flex-1 t-c price" :class="item.change.sort > 0 ? 'red' : 'green'">
								{{ $formatMoney(item.price.sort) || "-" }}
							</div>
							<div class="flex-1 t-c" :class="Number(item.change.sort) > 0 ? 'red' : 'green'">
								<!-- {{ $formatMoney(item.volume / 1000000) }} M -->
								{{ $formatMoney(Number(item.volumeK)) || "-" }} K
							</div>
							<div class="flex-1 t-r">
								<div class="flex flex-e per" :class="Number(item.change.sort) > 0 ? 'red' : 'green'">
									<div class="icon animate__animated animate__fadeIn"
										:class="Number(item.change.sort) > 0 ? 'up' : 'down'"></div>
									{{ $formatMoney(item.change.sort) || "-" }}
								</div>
								<div class="per" :class="item.change.sort > 0 ? 'red' : 'green'">
									{{ item.changePercent || "-" }}
								</div>
							</div>
						</div>
					</div>
				</div>
			</van-skeleton>
		</van-pull-refresh>

		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "moreList",
		props: {},
		data() {
			return {
				mType: "TAI",
				tab: [{
						name: "上市",
						type: "TAI",
					},
					{
						name: "上櫃",
						type: "TWO",
					},
				],
				mList: [],
				show: true,
				show1: true,
				currmentIndex: 1,

				loading: true,
				loading1: true,
				isShow: false,
				isLoading: false,

				sort: [{
						name: "漲幅榜",
						id: 0
					},
					{
						name: "跌幅榜",
						id: 1
					},
					{
						name: "成交額",
						id: 2
					},
					// { name: "創高榜", id: 3 },
					// { name: "創低榜", id: 4 },
				],
				sortIndex: 0,
				list: [],
				type: "zhangfb",
				mName: "",
			};
		},
		computed: {
			filterName() {
				return (value) => {
					value = value.replace("股價", "");
					let indx = value.indexOf("指數");
					return value.slice(indx - 2, indx + 2);
				};
			},
		},
		created() {
			// this.getList();
			let type = this.$route.query;
			if (type.mType) {
				this.mType = type.mType;
				this.sortIndex = type.sectorId;
				this.mName = type.mName;

				this.getMarkListStock();
			}
		},
		mounted() {},
		methods: {
			changeMarket(type) {
				this.$refs.loading.open();
				this.mType = type;
				this.getMarkList();
			},
			changeSort(type) {
				this.$refs.loading.open();
				this.sortIndex = type;
				this.getMarkListStock();
			},
			// 获取市场下的分类
			getMarkList() {
				this.$server
					.post("/parameter/category", {
						category: this.mType
					})
					.then((res) => {
						if (res.status == 1) {
							this.mList = res.data;
							// this.sortIndex = this.mList[0].sectorId;
							this.getMarkListStock();
						}
					});
			},

			// 获取分类下的股票
			getMarkListStock() {
				this.$server
					.post("/parameter/stockservices", {
						category: this.mType,
						sectorId: this.sortIndex,
						offset: 1,
					})
					.then((res) => {
						this.$refs.loading && this.$refs.loading.close();
						this.loading = false;
						this.loading2 = false;
						this.isLoading = false;
						this.list = res.list || [];
					});
			},

			// 走的排行榜數據
			getList() {
				this.$server
					.post("/parameter/top", {
						type: "twd",
					})
					.then((res) => {
						this.$refs.loading && this.$refs.loading.close();
						this.loading = false;
						this.isLoading = false;
						// console.log("res", res);
						this.list = res.data;
					});
			},
			onRefresh() {
				this.isShow = false;
				this.getMarkListStock();
			},
			changeList(type) {
				if (type == 0) {
					this.show = !this.show;

					// 成交額 排序
					if (this.show) {
						this.list = this.list.sort(
							(a, b) => Number(b.volume) - Number(a.volume)
						);
					} else {
						this.list = this.list.sort(
							(a, b) => Number(a.volume) - Number(b.volume)
						);
					}
				} else {
					this.show1 = !this.show1;

					// 漲跌排序
					if (this.show1) {
						this.list = this.list.sort(
							(a, b) => Number(b.gainValue) - Number(a.gainValue)
						);
					} else {
						this.list = this.list.sort(
							(a, b) => Number(a.gainValue) - Number(b.gainValue)
						);
					}
				}
			},
		},
	};
</script>

<style scoped lang="less">
	::-webkit-scrollbar {
		display: none;
	}

	.top {
		.search {
			border-radius: 0.3rem;
			background: #fff;
			height: 0.3rem;
			padding: 0 0.1rem;

			.ipt {
				font-size: 0.14rem;
				color: #000;
				margin-left: 0.1rem;
				font-size: 0.12rem;
				color: #8f8f8f;
			}
		}
	}

	.m-tab {
		border-bottom: 0.01rem solid #dedede;

		.m-item {
			font-size: 0.14rem;
			color: #979797;
			position: relative;
			padding: 0.1rem 0;

			&::after {
				content: "";
				width: 0.2rem;
				height: 0.02rem;
				position: absolute;
				left: 50%;
				bottom: 0;
				transform: translateX(-50%);
				background-color: transparent;
			}

			&.active {
				color: #549d7e;

				&::after {
					background-color: #549d7e;
				}
			}
		}
	}

	.bg {
		overflow-x: scroll;

		.change {
			padding: 0.15rem 0;
			width: 800%;

			.change-item {
				background: #8e8e8e;
				border-radius: 0.3rem;
				font-size: 0.12rem;
				color: #ffffff;
				padding: 0.05rem 0;
				width: 30%;
				text-align: center;
				margin-right: 0.1rem;

				&.active {
					background: linear-gradient(124deg, #77ba90 0%, #7dbab4 100%);
				}
			}
		}
	}

	.rm {
		.tt {
			.t {
				font-size: 0.14rem;
				color: #000000;
				// font-weight: bold;
			}

			.t1 {
				font-size: 0.12rem;
				color: #549d7e;
			}
		}

		.rm-list {
			.titles {
				padding: 0.05rem 0;

				div {
					font-size: 0.12rem;
					color: #787878;
				}

				.icon {
					margin-left: 0.05rem;
				}
			}

			.rm-item {
				padding: 0.1rem 0;
				border-bottom: 0.01rem solid #dedede;

				div {
					font-size: 0.12rem;
				}

				.name {
					color: #fff;
				}

				.code {
					font-size: 0.11rem;
					color: #909090;
					margin-top: 0.05rem;
				}

				.per {
					.icon {
						margin-right: 0.05rem;
					}
				}
			}
		}
	}

	.red {
		color: #ba3b3a;
	}

	.green {
		color: #39B44C;
	}

	::v-deep .search {
		border-radius: 0.04rem;
	}

	.page {
		padding: 1rem 0.15rem 0.6rem;
		min-height: 100vh;
		// background: #f7f7f7;
	}

	.top-fixed {
		position: fixed;
		width: 100%;
		top: 0.5rem;
		left: 0;
		z-index: 999;
		padding: 0.1rem 0.15rem;
	}

	.index {
		.t {
			font-weight: 500;
			color: #1e1e1e;
			padding: 0 0.1rem 0.1rem;
		}
	}

	.title {
		padding: 0 0.1rem;

		div {
			font-weight: 600;
			font-size: 0.16rem;
			color: #000000;
		}
	}
</style>