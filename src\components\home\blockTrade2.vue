<template>
	<div class="page ">
		<top-back :title="$t('日内交易')"></top-back>

		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<van-skeleton title :row="26" :loading="loading">
				<div class="cot">
					<!-- <div class="nav-box flex flex-b">
						<div class="nav-item" v-for="(item, index) in navList" :key="index"
							:class="{ active: currmentIndex == item.type }" @click="changeNav(item.type)">
							{{ item.name }}
						</div>
					</div> -->
					<template v-if="currmentIndex == 0">
						<div class="titles flex flex-b">
							<div class="flex-1">{{ $t("dividend").txt4 }}</div>
							<div class=" flex-1 t-c">{{ $t("dividend").txt2 }}</div>
							<div class="flex-1 t-r"></div>
						</div>
						<div class="list-item flex flex-b" v-for="(item, index) in chooseList" :key="index"
							@click="stockDetails(item)">
							<div class="flex-1">
								<div class="name">{{ item.name }}</div>
								<div class="code">{{ item.symbol }}</div>
							</div>
							<div class="flex-1 price t-c">{{ $formatMoney(item.price) }}</div>
							<div class="flex-1 flex flex-e">
								<div class="st">{{ $t("new").t2 }}</div>
							</div>
						</div>
						<no-data v-if="!chooseList.length"></no-data>
					</template>
					<template v-else>
						<div class="list-items" v-for="(item, index) in myList" :key="index">
							<div>
								<div class="name">
									{{ item.stock_name }}
								</div>
								<div class="code">
									{{ item.stock_code }}
								</div>
							</div>

							<div class="inner flex flex-b">
								<div class="inner-item flex flex-b">
									<div class="t">
										{{ $t("dividend").txt5 }}
									</div>
									<div class="t1">
										{{ $formatMoney(item.buy_price) }}
									</div>
								</div>

								<div class="inner-item flex flex-b">
									<div class="t">
										{{ $t("dividend").txt6 }}
									</div>
									<div class="t1">
										{{ $formatMoney(item.zhang) }}
									</div>
								</div>

								<div class="inner-item flex flex-b">
									<div class="t">
										{{ $t("dividend").txt7 }}
									</div>
									<div class="t1">
										{{ $formatMoney(item.cj_num) }}
									</div>
								</div>

								<div class="inner-item flex flex-b">
									<div class="t">
										{{ $t("dividend").txt8 }}
									</div>
									<div class="t1 shares-name">
										{{ $t(item.state) }}
									</div>
								</div>
							</div>
						</div>

						<no-data v-if="!myList.length"></no-data>
					</template>
				</div>
			</van-skeleton>
		</van-pull-refresh>

		<van-popup v-model="show" position="center" :style="{ width: '90%' }">
			<div class="pop">
				<div class="pop-title ">{{ stockObj.name + "/" + stockObj.code }}</div>
				<div class="pop-price t-c">
					<div class="t">{{ $formatMoney(stockObj.price) || 0 }}</div>
					<div class="t1">{{ $t("newt").t12 }}</div>
				</div>

				<div class="ipt">
					<div>{{ $t("new").b46 }}</div>
					<div class="">
						<input v-model="buyObj.handle" type="number" :placeholder="$t('dividend').txt12"
							@input="TypeInput($event)" />
					</div>

					<div class="txt">
						{{ $t("newt").t14 }}
						<span>{{ $formatMoney(countMoney) || 0 }}</span>
					</div>
				</div>

				<!--        <div class="pop-num">-->
				<!--          <div>{{ $t("new").b47 }}</div>-->
				<!--          <input-->
				<!--              :placeholder="$t('new').t1"-->
				<!--              type="password"-->
				<!--              v-model="password"-->
				<!--          />-->
				<!--        </div>-->
				<div class="flex flex-b btips ">
					<div class="flex">
						<div class="t1">{{ $t("newt").t13 }}</div>
						<div class="t2">
							{{ $formatMoney(userInfo.krw) || 0 }}
						</div>
					</div>

					<div class="flex">
						<div class="t1">{{ $t("newt").t12 }}</div>
						<div class="t2">{{ $formatMoney(stockObj.price) || 0 }}</div>
					</div>
				</div>

				<div @click="buyFn" class="b-btn">{{ $t("dividend").btn }}</div>
			</div>
			<div class="icon close" @click="show = false"></div>
		</van-popup>
		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "blockTrade",
		data() {
			return {
				loading: true,
				isLoading: false,
				navList: [{
						name: this.$t("dividend").tab1,
						type: 0
					},
					{
						name: this.$t("dividend").tab2,
						type: 1
					},
				],
				currmentIndex: 0,
				chooseList: [
					// {
					//   name: "名称",
					//   code: "007",
					//   price: "1000",
					// },
				],
				myList: [
					// {
					//   stock_name: "stock_name",
					//   stock_code: "stock_code",
					//   buy_price: "1000",
					//   zhang: "1000",
					//   cj_num: "1000",
					//   status: 1,
					//   state: "审核中",
					// },
				],
				show: false,
				stockObj: {},
				buyObj: {
					handle: null,
				},
				password: "",
				userInfo: {},
				currentItem: {},
			};
		},
		computed: {
			countMoney() {
				return this.stockObj.price * this.buyObj.handle;
			},
		},
		created() {
			this.initData();
			this.getNew();
		},
		mounted() {

		},
		methods: {
			// 下拉刷新
			onRefresh() {
				this.initData();
				this.getNew();
			},
			initData() {
				this.$server.post("/user/getUserinfo", {}).then((res) => {
					if (res.status == 1) {
						this.userInfo = res.data;
					}
				});
			},
			TypeInput(e) {
				// 只能输入数字的验证;
				const inputType = /[^\d]/g; //想限制什么类型在这里换换正则就可以了
				this.$nextTick(function() {
					this.buyObj.handle = e.target.value.replace(inputType, "");
				});
			},
			getNew() {
				this.$server.post("/transaction/nbhllist", {
					type: 2
				}).then((res) => {
					this.isLoading = false;
					this.loading = false;
					this.$refs.loading.close(); //关闭加载

					if (res.status == 1) {
						this.chooseList = res.data;
					}
				});
			},
			getMine() {
				this.$server.post("/transaction/ustockslist", {
					type: 2
				}).then((res) => {
					this.$refs.loading.close(); //关闭加载

					if (res.status == 1) {
						this.myList = res.data;
					}
				});
			},
			stockDetails(stock) {
				this.show = true;
				this.currentItem = stock;
				this.$server
					.post("/transaction/nbhldetails", {
						symbol: stock.code
					})
					.then((res) => {
						this.stockObj = res.data;
					});
			},
			changeNav(index) {
				this.currmentIndex = index;
				this.$refs.loading.open(); //开启加载

				if (index) this.getMine();
				else this.getNew();
			},
			buyFn() {
				if (!this.buyObj.handle) {
					this.$toast(this.$t("dividend").txt12);
					return;
				}
				// if (!this.password) {
				//   this.$toast(this.$t("new").t);
				//   return;
				// }
				this.$refs.loading.open(); //开启加载

				this.$server
					.post("/transaction/buy_stock", {
						symbol: this.stockObj.symbol,
						zhang: this.buyObj.handle,
						//password: this.password,
						buyzd: 1,
						ganggan: 1,
						type: 1,
						is_qc: 2,
						id: this.currentItem.id, //大宗增加传递参数，列表id
					})
					.then((res) => {
						this.$refs.loading.close();

						this.show = false;
						if (res.msg) {
							this.$toast(this.$t(res.msg));
						}
					});
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.5rem 0 0.1rem;
		min-height: 100vh;
	}
  ::v-deep .van-skeleton__row{
    background-color:transparent !important;
  }
  ::v-deep .van-skeleton__title{
    background-color:transparent !important;
  }
	.titles {
		padding: 0.1rem 0.1rem 0;
		border-bottom: 0.01rem solid #f5f5f5;

		div {
			font-size: 0.12rem;
			color: #464646;
		}
	}

	.van-popup {
		background-color: transparent;
	}

	.close {
		margin: 0.1rem auto 0;
	}

	.pop {
		background-color: #fff;
		padding: 0.15rem;
		border-radius: 0.08rem;
		position: relative;

		.btips {
			padding: 0.1rem 0;

			.t1 {
				font-size: 0.12rem;
				color: #9a9fa5;
			}

			.t2 {
				font-weight: 500;
				font-size: 0.12rem;
				color: #c5585e;
				margin-left: 0.05rem;
			}
		}

		.pop-title {
			font-size: 0.16rem;
			text-align: center;
		}

		.pop-price {
			padding: 0.2rem 0;

			.t {
				font-weight: 500;
				font-size: 0.26rem;
				color: #c5585e;
			}

			.t1 {
				font-size: 0.12rem;
			}
		}

		.ipt {
			input {
				margin: 0.05rem 0;
				width: 100%;
				background: transparent;
				border-radius: 0.04rem;
				border: 0.01rem solid #c0c0c0;
				height: 0.4rem;
				line-height: 0.4rem;
				padding: 0 0.1rem;

				&::placeholder {
					font-size: 0.12rem;
					color: #9a9fa5;
				}
			}
		}

		.pop-num {
			margin-top: 0.15rem;

			input {
				margin: 0.05rem 0;
				width: 100%;
				background: transparent;
				border-radius: 0.04rem;
				border: 0.01rem solid #c0c0c0;
				height: 0.4rem;
				line-height: 0.4rem;
				padding: 0 0.1rem;

				&::placeholder {
					font-size: 0.12rem;
					color: #9a9fa5;
				}
			}
		}

		.txt {
			font-size: 0.12rem;
			color: #9a9fa5;

			span {
				font-size: 0.12rem;
				color: #c5585e;
			}
		}

		.b-btn {
			margin: 0.2rem 0 0;
		}
	}

	.cot {
		.title {
			padding: 0.1rem;

			div {
				font-weight: 500;
				font-size: 0.12rem;
				color: #666666;
			}
		}

		.list-item {
			padding: 0.1rem;
			border-bottom: 0.01rem solid #f5f5f5;

			&:last-child {
				border-bottom: 0;
			}

			.name {
				font-weight: 600;
			}

			.code {
				font-weight: 500;
				font-size: 0.12rem;
				color: #464646;
			}

			.price {
				font-weight: 600;
				// font-size: 0.12rem;
				color: #c5585e;
			}

			.st {
				background: #6970af;
				border-radius: 0.04rem;
				padding: 0.05rem 0.1rem;
				font-weight: 500;
				font-size: 0.12rem;
				color: #ffffff;
			}

			.t {
				font-weight: bold;
			}
		}

		.list-items {
			padding: 0.1rem;
			border-bottom: 0.01rem solid #f5f5f5;

			.name {
				font-weight: 600;
			}

			.code {
				font-weight: 500;
				font-size: 0.12rem;
				color: #464646;
			}

			.inner {
				flex-wrap: wrap;
				padding: 0.1rem 0 0;

				.inner-item {
					width: 48%;
					line-height: 0.2rem;

					.t {
						font-size: 0.12rem;
						color: #464646;
					}

					.t1 {
						font-weight: 600;
						font-size: 0.12rem;
					}

					.shares-name {
						color: #6970af;
					}
				}
			}
		}
	}

	.nav-box {
		padding: 0 0.1rem;
		background: #ffffff;
		box-shadow: -0.01rem 0.02rem 0.02rem 0rem rgba(175, 175, 175, 0.25);

		.nav-item {
			padding: 0.1rem 0;
			flex: 1;
			font-size: 0.12rem;
			color: #a1a1a1;
			text-align: center;
			position: relative;

			&::after {
				content: "";
				width: 50%;
				height: 0.02rem;
				position: absolute;
				bottom: 0;
				left: 50%;
				transform: translateX(-50%);
				background: transparent;
			}

			&.active {
				color: #6970af;

				&::after {
					background: #6970af;
				}
			}
		}
	}
</style>
