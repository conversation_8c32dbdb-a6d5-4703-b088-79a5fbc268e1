<template>
	<div class="top-fixed">
		<div class="header flex flex-b">
			<div class="searchBox flex flex-1" @click="$toPage('/favorite/search')" v-if="home">
				<div class="icon sou" style="margin-right: 0.1rem;"></div>
				<div class="t">搜尋</div>
			</div>
			<div class="title">{{title}}</div>
			<div class="flex">
				<!-- <div class="icon kf1" @click="$toPage('kefu',$cfg.kefu)" style="margin-right: 0.1rem;"></div> -->
				<div class="icon ring animate__animated animate__fadeIn" @click="$toPage('/information/userInfo')">
					<span v-if="readNum>0" style="width: .08rem;height: .08rem;border-radius: 50%;background: #ff0000;margin-right: .05rem;display: block;"></span>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
	export default {
		name: "topMenu",
		props: {
			title: {
				type: String,
				default: "",
			},
			home: {
				type: Boolean,
				default: false,
			},
		},
		data() {
			return {
				readNum:0
			};
		},
		components: {},
		methods: {},
		created() {},
		computed: {},
	};
</script>

<style scoped lang="less">
	.top-fixed {
		width: 100%;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 999;
	
		.header {
			padding: 0.15rem;
			width: 100%;
			background: #18191B;
			position: relative;
			.searchBox{
				height: 0.31rem;
				background: #434446;
				border-radius: 0.16rem;
				padding: 0 0.1rem;
				margin-right: 0.15rem;
				.t{
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 0.13rem;
					color: #888888;
				}
			}
			.title{
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 0.18rem;
				color: rgba(255,255,255,0.8);
			}
			img {
				position: absolute;
				left: 48%;
				top: 50%;
				transform: translateY(-50%);
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 0.15rem;
				color: #FFFFFF;
			}
		}
	
	}
</style>