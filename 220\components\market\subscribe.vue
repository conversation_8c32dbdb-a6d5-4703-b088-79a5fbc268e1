<template>
	<div class="subscribe">
		<div class="headf">
			<top :title="$t(text)"></top>
		</div>
		<div class="recordBtn flex flex-b">
			<div class="item flex flex-c" @click="clickNext('/market/subscribeRecord?type=0')">
				<div class="icon sgjl"></div>
				{{$t('subscribe').txt12}}
			</div>
			<div class="item flex flex-c" @click="clickNext('/market/subscribeRecord?type=1')">
				<div class="icon zjjl"></div>
				{{$t('subscribe').txt13}}
			</div>
		</div>
		<div class="list">
			<div class="navTab flex flex-a text-center">
				<div class="item" :class="{'sel':navTabIdx==0}" @click="navTabIdx=0">{{$t('subscribe').txt14}}</div>
				<div class="item" :class="{'sel':navTabIdx==1}" @click="navTabIdx=1">{{$t('subscribe').txt15}}</div>
				<div class="item" :class="{'sel':navTabIdx==2}" @click="navTabIdx=2">{{$t('subscribe').txt16}}</div>
			</div>
			<div class="itemBox">
				<div class="item" v-for="(item, index) in xinList" :key="index"
					@click="clickNext('/market/subscribeDetail?item='+JSON.stringify(item))"
					v-if="(navTabIdx==0&&item.isKsg==false)||(navTabIdx==1&&item.isKsg==true)||(navTabIdx==2&&item.isKsg==null)">
					<div class="item-top flex flex-b">
						<div class="item-name flex">{{ item.name }}<span class="flex">{{item.symbol}}</span></div>
						<div>{{$t('subscribe').txt5}} {{$formatDate('DD/MM/YYYY',item.end*1000)}}</div>
					</div>
					<div class="item-middle">
						<div class="item-list text-center">
							{{$t('subscribe').txt1}}
							<span>{{$formatMoney(item.bprice)}}円</span>
						</div>
						<div class="item-list text-center">
							{{$t('subscribe').txt2}}
							<span>{{$formatMoney(item.price)}}円</span>
						</div>
						<div class="item-list text-center">
							{{$t('subscribe').txt3}}
							<span>{{$formatMoney(item.num, 0)}}</span>
						</div>
						<div class="item-list text-center">
							{{$t('subscribe').txt4}}
							<span>{{$formatDate('DD/MM/YYYY',item.start*1000)}}</span>
						</div>
						<div class="item-list text-center">
							{{$t('subscribe').txt5}}
							<span>{{$formatDate('DD/MM/YYYY',item.end*1000)}}</span>
						</div>
						<div class="item-list text-center">
							{{$t('subscribe').txt6}}
							<span>{{changeDate(item.ss_date)}}</span>
						</div>
					</div>
					<div class="item-foot" v-if="item.isKsg==true">
						<div class="link1">{{$t('subscribe').txt7}}</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
	import Vue from "vue";
	import qs from "qs";
	import axios from "axios";
	import {
		Toast
	} from "vant";
	Vue.use(Toast);

	import top from "../bar/toper.vue";

	export default {
		name: "subscribe",
		data() {
			return {
				text: this.$t('subscribe').title,
				navTabIdx: 1,
				xinList: [],
			};
		},
		components: {
			top,
		},
		destroyed() {},
		mounted() {
			this.getXingu();
		},
		methods: {
			changeDate(time) {
				let date = new Date(time.replace('-', '/').replace('-', '/'));
				let Year = date.getFullYear();
				let Moth = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1);
				let Day = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate());
				let Hour = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours());
				let Minute = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes());
				let Sechond = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds());
				let GMT = Day + '/' + Moth + '/' + Year;
				return GMT
			},
			getXingu() {
				this.$server.post("/trade/placinglist", {type: 'jpy', buy_type: 0}).then((res) => {
					let now = new Date().getTime();
					let arr = [];

					res.data.data.forEach(item => {
						// 可申购
						if (item.start * 1000 < now && now < item.end * 1000) {
							item.time = Math.floor((item.end * 1000 - now) / 1000 / 60 / 60 / 24);
							item.isKsg = true; //是否可申购
						} else if (now < item.start * 1000) {
							item.time = Math.floor((item.start * 1000 - now) / 1000 / 60 / 60 / 24);
							// 待申购
							item.isKsg = false;
						} else {
							item.isKsg = null;
						}

						arr.push(item);
					});
					this.xinList = [...new Set(arr)];
				});
			}
		},

	};
</script>

<style lang="less">
	body {
		overflow-x: hidden;
	}

	.subscribe {
		padding-top: .44rem;
		min-height: 100vh;
		width: 100%;
		background: #0F161C;

		.headf {
			position: fixed;
			z-index: 888;
			top: 0;
			left: 0;
			width: 100%;
			height: .44rem;
			border-bottom: .01rem solid #DCDCE0;
		}

		.recordBtn {
			margin: 0.15rem 0.12rem;
			position: relative;
			z-index: 20;

			.item {
				width: 45%;
				height: .28rem;
				background: #5ED5A8;
				border-radius: 0.05rem;
				border: 0.01rem solid rgba(255,255,255,0.2);
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 0.12rem;
				color: #333333;
				text-align: center;
			}
		}

		.list {
			position: relative;
			z-index: 20;

			.navTab {
				margin: .12rem;
				background: #424E4E;
				border-radius: 0.1rem;
				padding: 0.05rem;
				position: relative;
				z-index: 20;
				overflow-x: scroll;
				overflow-y: hidden;
				white-space: nowrap;
				text-align: center;
				.item {
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.14rem;
					color: #B3B8B8;
					line-height: 0.4rem;
					padding: 0.02rem 0.12rem;
					min-width: calc(100%/3);
				}
				.sel {
					color: #000000;
					background: #5ED5A8;
					border-radius: 0.1rem;
				}
			}

			.itemBox {
				height: calc(100vh - 1.6rem);
				overflow: scroll;

				.item {
					.item-top {
						padding: 0.05rem 0.12rem;
						background-color: rgba(94, 213, 168, 0.2);
						border-bottom: .03rem solid #fff;
						font-weight: 400;
						font-size: 0.13rem;
						color: #FE0000;
						.item-name {
							font-family: PingFangSC, PingFang SC;
							font-weight: 600;
							font-size: 0.14rem;
							color: #FFFFFF;
							align-items: flex-end;
							span {
								font-family: PingFangSC, PingFang SC;
								font-weight: 400;
								font-size: 0.12rem;
								color: #FFFFFF;
								margin-left: .05rem;
							}
						}

						.item-state {
							width: .72rem;
							height: .78rem;
							position: absolute;
							right: 0;
							top: 0;
						}
					}

					.item-middle {
						display: flex;
						flex-wrap: wrap;
						margin: 0 .1rem;
						padding: .1rem 0;
						justify-content: space-between;
						.item-list {
							width: 48%;
							//height: 155rpx;
							display: flex;
							align-items: center;
							justify-content: space-between;
							font-weight: 600;
							font-size: 0.11rem;
							color: #718A94;
							padding: .1rem 0;
							border-bottom: 0.01rem solid rgba(255, 255, 255, 0.2);

							span {
								font-weight: 600;
								font-size: 0.11rem;
								color: #FFFFFF;
							}

							.time {
								font-size: .1rem;
							}
						}
					}

					.item-foot {
						margin: 0 0.12rem 0.1rem;
						.link1 {
							height: .32rem;
							background: #5ED5A8;
							border-radius: 0.05rem;
							border: 0.01rem solid rgba(255,255,255,0.2);
							display: flex;
							align-items: center;
							justify-content: center;
							font-weight: 600;
							font-size: 0.12rem;
							color: #333333;
						}

					}
				}
			}
		}
	}
</style>