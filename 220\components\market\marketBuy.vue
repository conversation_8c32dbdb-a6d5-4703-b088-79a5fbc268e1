<template>
	<div class="marketBuy">
		<div class="Header flex flex-c">
			<div class="flex">
				<div class="back" @click="clickBack()">
					<img src="../../assets/v2/back.png" style="width:0.2rem;height:0.15rem;" />
				</div>
				<div class="title flex-column-item">{{details.name}}<span>{{details.symbol}}</span></div>
			</div>
			<div class="favo" @click="addSelect(details)">
				<img src="../../assets/skin/market/favo1.png" v-if="details.is_zixuan" />
				<img src="../../assets/skin/market/favo2.png" v-else />
			</div>
		</div>
		<div class="Info flex flex-b">
			<div class="price" :class="{'red':details.gain>=0,'green':details.gain<0}">{{$formatMoney(details.price)}}円</div>
			<div class="per">
				<div class="tt">{{details.gain>0?'+':''}}{{$formatMoney(details.gainValue)}}円</div>
				<div :class="{'red':details.gain>=0,'green':details.gain<0}">{{details.gain>0?'+':''}}{{$formatMoney(details.gain)}}%
				</div>
			</div>
		</div>
		<div class="record flex flex-b flex_wrap">
			<div class="item flex flex-b">{{$t('sharesDetails').txt1}}<span>{{ $formatMoney(details.open) }}円</span></div>
			<div class="item flex flex-b">{{$t('sharesDetails').txt2}}<span>{{ $formatMoney(details.preClose) }}円</span></div>
			<div class="item  flex flex-b">{{$t('sharesDetails').txt3}}<span>{{ $formatMoney(details.high) }}円</span></div>
			<div class="item flex flex-b">{{$t('sharesDetails').txt4}}<span>{{ $formatMoney(details.low) }}円</span></div>
			<div class="item flex flex-b">
				{{$t('sharesDetails').txt5}}<span>{{$formatMoney(parseFloat(details.volume)/100000000) }}{{$t('sharesDetails').txt7}}</span>
			</div>
			<div class="item  flex flex-b">
				{{$t('sharesDetails').txt6}}
				<span
					v-if="details.Amount">{{$formatMoney(parseFloat(details.Amount)/100000000) }}{{$t('sharesDetails').txt7}}</span>
				<span v-else>-</span>
			</div>
		</div>
		<div class="pageFoot">
			<div class="changeList flex flex-b">
				<div class="changeList-txt">{{$t('sharesDetails').txt24}}</div>
				<div class="changeList-value yellow">{{ $formatMoney(userInfo.jpy) }}{{$currency}}</div>
			</div>
			<div class="changeList flex flex-b">
				<div class="changeList-txt">{{$t('sharesDetails').txt25}}</div>
				<div class="changeList-value blueBg">{{ $formatMoney(fuwu) }}{{$currency}}</div>
			</div>
			<div class="changeList">
				<div class="navType flex">
					<div class="nav-type-pp" :class="{ 'sel': isLimit }" @click="setLimit(true)">
						{{$t('sharesDetails').txt27}}</div>
					<div class="nav-type-pp" :class="{ 'sel': !isLimit }" @click="setLimit(false)">
						{{$t('sharesDetails').txt28}}</div>
				</div>
			</div>
			<div class="changeList" v-show="isLimit">
				<div class="changeList-txt">{{$t('sharesDetails').txt29}}</div>
				<div>
					<num-com :show='isLimit' ref="numPrice" @change="onNum('price', $event)" />
				</div>
			</div>
			<div class="changeList">
				<div class="changeList-txt">{{$t('sharesDetails').txt30}}</div>
				<div class="flex">
					<num-com :show="true" ref="numBuy" @change="onNum('num', $event)" />
					<div class="changeList-a">{{$t('sharesDetails').txt31}}</div>
				</div>
			</div>
			<div class="changeList">
				<div class="changeList-txt">{{$t('sharesDetails').txt33}}</div>
				<div class="navType flex">
					<div class="nav-type-pp" :class="{ 'sel': buyType == 'up' }" @click="buyType = 'up'">
						{{$t('sharesDetails').txt34}}</div>
					<div class="nav-type-pp" :class="{ 'sel': buyType == 'down' }" @click="buyType = 'down'">
						{{$t('sharesDetails').txt35}}</div>
				</div>
			</div>

			<div class="info">
				<div class="info-item"><span>{{ $formatMoney(shizhi) }}{{$currency}}</span>{{$t('sharesDetails').txt36}}
				</div>
				<div class="info-tip">+</div>
				<div class="info-item"><span>{{ $formatMoney(fuwu) }}{{$currency}}</span>{{$t('sharesDetails').txt37}}
				</div>
				<div class="info-tip">=</div>
				<div class="info-item"><span>{{ $formatMoney(parseFloat(shizhi)+parseFloat(fuwu)) }}{{$currency}}</span>{{$t('sharesDetails').txt38}}</div>
			</div>
			<div class="btn-big" @click="onBuy()">{{$t('sharesDetails').txt39}}</div>
			<div class="tip">{{$t('sharesDetails').txt40}}</div>
		</div>
		<van-popup v-model="minilayerShow" position="bottom">
			<div class="minilayer-min">
				<div class="uni-title">{{ details.name + "/" + details.symbol }}</div>
				<div class="uni-content" v-if="isShou">
					<div class="">{{$t('sharesDetails').txt41}}</div>
					<div class="uni-content-box">
						<input v-model="buyObj.handle" type="number" :placeholder="$t('sharesDetails').txt42" />
					</div>
				</div>
				<div class="uni-content" v-if="isWan">
					<div class="">{{$t('sharesDetails').txt43}}</div>
					<div class="uni-content-tit">{{isLimit? $formatMoney(buyObj.price) + '円':$t('sharesDetails').txt44 }}</div>
				</div>
				<div class="uni-content" v-if="isWan">
					<div class="">{{$t('sharesDetails').txt45}}</div>
					<div class="uni-content-tit">{{ $formatMoney(gushu, 0) }}</div>
				</div>
				<div class="uni-content">
					<div class="">{{$t('sharesDetails').txt46}}</div>
					<div class="uni-content-tit">{{ $formatMoney(buyObj.multiple, 0) }}</div>
				</div>
				<div class="uni-content">
					<div class="">{{$t('sharesDetails').txt47}}</div>
					<div class="uni-content-tit">{{ $formatMoney(benjing) }}円</div>
				</div>
				<div class="uni-content">
					<div class="">{{$t('sharesDetails').txt48}}</div>
					<div class="uni-content-tit">{{ $formatMoney(shizhi) }}円</div>
				</div>
				<div class="uni-content">
					<div class="">{{$t('sharesDetails').txt49}}</div>
					<div class="uni-content-tit">{{ $formatMoney(fuwu) }}円</div>
				</div>
				<div class="uni-content">
					<div class="">{{$t('sharesDetails').txt50}}</div>
					<div class="uni-content-tit">{{ $formatMoney(heji) }}円</div>
				</div>
				<div class="uni-content">
					<div class="">{{$t('sharesDetails').txt51}}</div>
					<div class="uni-content-tit">{{ $formatMoney(userInfo.jpy) }}円</div>
				</div>
				<div @click="buyFn" class="btn-big" :class="{gray:flagBuy==false}">{{$t('sharesDetails').txt52}}</div>
			</div>
		</van-popup>
		<div class="pop popLoad" v-if="!flagBuy"></div>
	</div>
</template>

<script>
	import Vue from 'vue';
	import qs from 'qs';
	import axios from 'axios';
	import {
		Toast,
		Popup
	} from 'vant';
	Vue.use(Toast).use(Popup);
	import NumCom from "./NumCom";
	export default {
		name: "marketBuy",
		data() {
			return {
				flagBuy: true,
				symbol: '',
				type: null,
				details: {},
				userInfo: {},
				jhjj: "",
				buyObj: {
					price: 0,
					multiple: 1,
					handle: "",
					buy_num: "",
					total: 0,
				},
				isLimit: false,
				config: {},
				buyType: "up",
				minilayerShow: false,
				isShou: false,
				isWan: true,
			}
		},
		components: {
			NumCom
		},
		destroyed() {

		},
		computed: {
			benjing() {
				// buytype_zh:"买入类型(1手，2金额)"
				// //按手
				if (this.config.buytype == 1) {
					return (this.gushu * this.buyObj.price); /// this.buyObj.multiple; //todo
				}
				// //按万
				return Number(this.buyObj.buy_num) * this.buyObj.price;
			},
			shizhi() {
				let scale = this.config.gtype == 1 ? this.buyObj.multiple : 1;
				return this.benjing * scale;
			},
			gushu() {
				if (this.config.buytype == 1) {
					return parseInt(this.buyObj.buy_num) * 1;
				}
				return 0;
			},
			fuwu() {
				let val = this.shizhi * Number(this.config.buycharge);
				if (val < this.config.minshouxu) {
					return Number(this.config.minshouxu);
				}
				return val;
			},
			heji() {
				return this.benjing + this.fuwu;
			},
		},
		mounted() {
			this.symbol = this.$route.query.symbol;
			this.type = this.$route.query.type;
			this.buyType = this.$route.query.buyType;
			this.getConfig();
			this.getDetail();
			this.getUserInfo();
		},
		methods: {
			getDetail() {
				this.$server.post("/trade/stockdetails", {
					symbol: Number(this.symbol),
          type: 'jpy'
				}).then((res) => {
					let vo = res.data.data || {};
					let gz = this.details.is_zixuan;
					if (res.data.data) {
						this.details = vo;
						this.details.is_zixuan = gz;
					}
					if (typeof(this.details.is_zixuan) == "undefined") {
						this.$getOp(this.details.symbol, this.type, b => {
							this.details.is_zixuan = b;
							this.details = {
								...this.details
							}
						});
					}
					this.$refs.numBuy.setVal(1, 1, "");
					this.$refs.numPrice.setVal(vo.price, 0)
				});
			},
			addSelect(obj) {
				if (!obj.is_zixuan) {
					this.$server.post("/user/addOptional", {
						symbol: this.symbol,
            type: 'jpy'
					}).then((res) => {
						if (res.data.status === 1) {
							obj.is_zixuan = 1;
							this.details = {
								...obj
							};
							this.$forceUpdate()
						}
					});
				} else {
					this.$server.post("/user/removeOptional", {
						symbol: obj.symbol,
            type: 'jpy'
					}).then((res) => {
						if (res.data.status === 1) {
							obj.is_zixuan = 0;
							this.details = {
								...obj
							};
						}
					});
				}
			},
			getUserInfo() {
				this.$server.post("/user/getUserinfo", {}).then((res) => {
					if (res.data.status === 1) {
						this.userInfo = res.data.data;
					}
				});
			},
			getConfig() {
				this.$server.post('/common/config', {type: 'jpy'}).then(res => {
					console.log(res)
					let list = res.data.data;
					let listLength = list.length;
					let val = {}
					for (var a = 0; a < listLength; a++) {
						var row = list[a];
						val[row.name] = row.value;
						val[row.name + "_zh"] = row.title;
					}
					this.config = val;
				});
			},
			onBuy() {
				this.showHouse(this.buyType == "up" ? 0 : 1);
			},
			showHouse(index) {
				this.minilayerShow = true;
				this.getPrice();
			},
			getPrice() {
				this.buyObj.total = ((this.details.latestPrice * this.buyObj.handle * 100) / this.buyObj.multiple).toFixed(
					2);
			},
			buyFn() {
				let that = this;
				if (this.buyObj.handle === 0) {
					Toast({
						message: `${this.$t("common.sharesDetails.noHandle")}`,
						duration: 2000
					});
				} else {
					//普通购买
					let details = this.details;
					this.flagBuy = false;
					this.$forceUpdate();
					var tmp = {
						symbol: Number(details.symbol),
						zhang: that.buyObj.buy_num,
						ganggan: !!that.buyObj.multiple ? that.buyObj.multiple : undefined,
            gdlx: this.isLimit ? 2 : 1,
						buyzd: this.buyType == 'up' ? 1 : 2,
            is_type: 0,
						buy_price: Number(this.buyObj.price),
            type: 'jpy'
					}

					this.$server.post("/trade/buy_stock", tmp).then((res) => {
						this.flagBuy = true;
						if (res.data.status === 1) {
							Toast({
								message: this.$t(res.data.msg),
								duration: 2000
							});
							that.minilayerShow = false;
							setTimeout(function() {
								that.clickNext('/position/position')
							}, 1000)
						} else {
							Toast({
								message: this.$t(res.data.msg),
								duration: 2000
							});
						}
					});
				}
			},
			setLimit(b) {
				this.isLimit = b;
				if (!b) {
					this.buyObj.price = this.details.price;
				} else {
					this.$refs.numPrice.setVal(this.details.price, 0)
				}
			},
			onNum(type, evt) {
				if (type == "price") {
					this.buyObj.price = evt.value;
				} else if (type == 'gangan') {
					this.buyObj.multiple = evt.value;
				} else {
					this.buyObj.buy_num = evt.value;
				}
			},
		}
	}
</script>

<style lang="less">
	.marketBuy {
		background: #0F161C;
		min-height: 100vh;
		padding-top: .47rem;
		width: 100%;
		overflow: hidden;

		.Header {
			height: .47rem;
			width: 100%;
			background: #424E4E;
			position: fixed;
			top: 0;
			left: 0;
			z-index: 888;

			.back {
				position: absolute;
				top: .12rem;
				left: .16rem;

				img {
					width: .1rem;
				}
			}

			.title {
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 0.16rem;
				color: #FFFFFF;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
				line-height: 0.2rem;

				span {
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.14rem;
					color: #FFFFFF;
				}
			}

			.favo {
				position: absolute;
				top: .12rem;
				right: .16rem;

				img {
					width: .2rem;
					height: .2rem;
				}
			}
		}

		.Info {
			padding: 0.15rem 0.12rem;
			border-bottom: 0.05rem solid #40464c;

			.price {
				position: relative;
				z-index: 10;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 0.23rem;

				&.red {
					.per {
						background: #F53E3E;
					}
				}

				&.green {
					.per {
						background: #00B258;
					}
				}
			}

			.per {
				font-weight: 500;
				font-size: 0.18rem;

				.tt {
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.18rem;
					color: #FFFFFF;
					text-align: right;
				}
			}
		}

		.record {
			position: relative;
			z-index: 5;
			margin: .08rem .12rem;

			.item {
				width: 47%;
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 0.11rem;
				color: #718A94;
				padding: 0.08rem 0;
				border-top: 0.01rem solid rgba(255, 255, 255, 0.16);

				&:first-child {
					border-top: none;
				}

				&:nth-child(2) {
					border-top: none;
				}

				span {
					font-family: PingFangSC, PingFang SC;
					font-weight: 600;
					font-size: 0.11rem;
					color: #FFFFFF;
				}
			}
		}

		.pageFoot {
			padding: .1rem .12rem .16rem;

			.changeList {
				margin-bottom: .1rem;
				.changeList-txt {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.12rem;
					color: #FFFFFF;
					margin: 0.05rem 0;
				}

				.navType {
					width: 100%;
					background: #424E4E;
					border-radius: 0.1rem;
					padding: 0.05rem;

					.nav-type-pp {
						width: 50%;
						height: 0.4rem;
						padding: 0 .16rem;
						display: flex;
						align-items: center;
						justify-content: center;
						font-family: PingFangSC, PingFang SC;
						font-weight: 500;
						font-size: 0.14rem;
						color: #B3B8B8;

						&:last-child {
							border: 0;
						}

						&.sel {
							background: #5ED5A8;
							border-radius: 0.1rem;
							color: #000000;
						}

						&.blueBg {
							background: #E3EEFF;
							color: #3B82F6;
						}

						&.red {
							background: #FFEFF3;
							color: #FA2256;
						}

						&.green {
							background: #EFFFFA;
							color: #19C09A;
						}
					}

					.nav-type-pl {
						background: rgba(76, 61, 137, .35);
						color: #ECE7E3;
					}
				}

				.changeList-value {
					font-size: .14rem;
					font-family: FZLanTingHeiT-R-GB;
					font-weight: bold;
					color: #24272C;
					display: flex;
					align-items: center;
					justify-content: center;
				}

				.changeList-a {
					height: .37rem;
					font-size: .14rem;
					font-family: FZLanTingHeiT-R-GB;
					font-weight: 400;
					color: #A2A2A2;
					padding: 0 .12rem .1rem;
					display: flex;
					align-items: center;
					justify-content: center;
				}

				.blueBg {
					color: #fff;
				}

				.yellow {
					color: #FFAA65;
				}
			}

			.ml-14 {
				margin-left: .07rem;
			}

			.btn-big {
				margin-top: .47rem;
				-webkit-transition-duration: .5s;
				-moz-transition-duration: .5s;
				-o-transition-duration: .5s;
			}

			.btn:active {
				background: rgba(76, 61, 137, .6);
			}

			.tip {
				margin-top: .3rem;
				padding-bottom: .3rem;
				font-size: .12rem;
				font-family: FZLanTingHeiT-R-GB;
				font-weight: 400;
				color: #A2A2A2;
				line-height: .17rem;
			}

			.info {
				margin-top: .3rem;
				display: flex;
				align-items: center;
				justify-content: space-between;
				.info-item {
					background: #5ED5A8;
					border-radius: .1rem;
					flex: 1;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: space-between;
					font-size: .12rem;
					font-family: FZLanTingHeiT-R-GB;
					font-weight: 400;
					color: #fff;
					box-sizing: border-box;
					padding: .1rem 0.05rem;
					height: 0.8rem;
					text-align: center;

					span {
						font-size: .14rem;
						font-family: FZLanTingHeiT-R-GB;
						font-weight: 500;
						color: #000;
						margin: .03rem 0 .03rem 0;
					}
				}

				.info-tip {
					height: .51rem;
					font-size: .16rem;
					font-family: FZLanTingHeiT-R-GB;
					font-weight: 400;
					color: #fff;
					padding: 0 .04rem;
					display: flex;
					align-items: center;
					justify-content: center;
				}
			}
		}

		.van-popup {
			border-radius: .12rem .12rem 0 0;
		}

		.minilayer-min {
			background: #fff;
			border-radius: .12rem .12rem 0 0;
			padding-bottom: .15rem;

			.uni-title {
				height: .6rem;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: .15rem;
				font-family: PingFangSC-Semibold, PingFang SC;
				font-weight: 600;
				color: #141414;
			}

			.uni-content {
				height: .45rem;
				background: #E4E4E4;
				border-radius: .03rem;
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin: 0 .15rem;
				padding: 0 .16rem;
				margin-bottom: .12rem;
				font-size: .14rem;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #333;

				.uni-content-tit {
					font-size: .16rem;
					color: #000000;
				}

				.uni-content-box {
					background: #666;
					border: .05rem solid #000;
					padding: 0 .15rem;

					input {
						font-size: .13rem;
						font-family: PingFangSC-Regular, PingFang SC;
						font-weight: 400;
						color: #333;
					}
				}
			}

			.btn-big {
				margin: .15rem 0.12rem;
				-webkit-transition-duration: .5s;
				-moz-transition-duration: .5s;
				-o-transition-duration: .5s;

				&.gray {
					background: #888;
				}
			}
		}
	}
</style>