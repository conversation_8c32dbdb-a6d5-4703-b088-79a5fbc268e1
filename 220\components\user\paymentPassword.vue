 <template>
	<div>
	<van-popup v-model="show" position="top" class="pupbg" :close-on-click-overlay='false' @click-overlay='changed'>
	
		<div class="kun">
			<h6>支付密码</h6>
			<ul class="shuru">
				<li>
					<input placeholder="请输入支付密码" type="password" v-model="password"/>
				</li>
			</ul>
			
			<div class="button" @click="change">确定</div>
		</div>
	
		
	</van-popup>
	</div>
</template>
<script type="text/javascript">
import Vue from 'vue';
import qs from 'qs';
import axios from 'axios';
import { Popup  } from 'vant';
Vue.use(Popup );
import { Toast  } from 'vant';
Vue.use(Toast );
export default {
	name:"password",
	data(){
		return {
			password:''
			
		}
	},
	props:['show'],
	components:{
		
	},
	methods:{
		change(){
			if(this.password==''){
				Toast({
					message:'请输入密码',
					duration:2000
				});
				return false;
			}
			this.$emit('closepop',this.password)
		},
		changed(){
			this.$emit('closepop',)
		}
		
	},
	destroyed() {
		
	},
	mounted(){
		
	},
}
</script>
<style type="text/css" lang="less" scoped="scoped">

input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder{
  color: #666;
  font-size: 0.14rem;
}
.kun{
	width:3.47rem;
	margin:0 auto;
	margin-top:1.6rem;
	background: #fff;
	padding-bottom:0.26rem;
	border-radius: 0.06rem;
	overflow: hidden;
	h6{
		height:0.58rem;
		background: #F6F8F9;
		text-align: center;
		line-height: 0.58rem;
		color:#000;
		font-size:0.16rem;
	}
	.shuru{
		width:3.4rem;
		margin:0 auto;
		li{
			height:0.41rem;
			width:3.2rem;
			margin:0 auto;
			margin-top:0.35rem;
			background: rgba(51, 51, 51, 0.08);
			border-radius: 0.21rem;
			display: flex;
			input{
				width:2.2rem;
				text-align: left;
				color:#000;
				font-sizE:0.14rem;
				background: transparent;
				padding-left:0.2rem;
			}
		}
	}
	.button{
		width:1.71rem;
		height:0.41rem;
		border-radius: 0.21rem;
		background: linear-gradient(139deg, #82C1FA 0%, #2B76E5 100%);
		color:#fff;
		margin:0 auto;
		margin-top:0.25rem;
		font-size:0.15rem;
		text-align: center;
		line-height: 0.41rem;
	}
}
</style>
