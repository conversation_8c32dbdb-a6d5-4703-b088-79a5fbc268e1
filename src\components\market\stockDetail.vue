<template>
	<div class="page">
		<top-back :title="$t('股票詳情')"></top-back>
		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<div class="icon scsc animate__animated animate__fadeIn" :class="is_zixuan ? 'cutzx' : 'addzx'" @click="addSelect(details)"></div>
			<div class="top-data">
				<div class="tp flex flex-b">
					<div class="">
						<div class="name">{{ details.local_name }}</div>
						<div class="code">{{ details.symbol }}</div>
					</div>
					<!-- <div class="time">({{ $formatDate("YYYY/MM/DD hh:mm:ss", details.time * 1000)}})</div> -->
					<div class="t-r">
						<div class="price t-r" :class="details.gain >= 0 ? 'red' : 'green'">{{ $formatMoney(details.price) }}</div>
						<div class="flex" :class="details.gain >= 0 ? 'red' : 'green'" style="margin-top: 0.1rem;">
							<!-- <div class="icon" :class="details.gain >= 0 ? 'up1' : 'down1'"></div> -->
							<div class="t">{{ Number(details.gainValue).toFixed(2) }}</div>
							<div class="t">({{ Number(details.gain) > 0 ? "+" : ""}}{{ Number(details.gain).toFixed(2) }}%)
							</div>
						</div>
					</div>
				</div>
				<div class="list flex flex-b">
					<div class="flex flex-b item ">
						<div class="t1">{{ $t('今開') }}</div>
						<div class="t2 red">
							{{isNaN(Number(details.open).toFixed(2))? "0": $formatMoney(Number(details.open).toFixed(2))}}
						</div>
					</div>

					<div class="flex flex-b item">
						<div class="t1">{{ $t('昨收') }}</div>
						<div class="t2 green">
							{{isNaN(Number(details.preClose).toFixed(2))? "0": $formatMoney(Number(details.preClose).toFixed(2))}}
						</div>
					</div>

					<div class="flex flex-b item">
						<div class="t1">{{ $t('最高') }}</div>
						<div class="t2">
							{{ $formatMoney(Number(details.high).toFixed(2)) }}
						</div>
					</div>

					<div class="flex flex-b item">
						<div class="t1">{{ $t('最低') }}</div>
						<div class="t2">
							{{ $formatMoney(Number(details.low).toFixed(2)) }}
						</div>
					</div>

					<div class="flex flex-b item">
						<div class="t1">{{ $t('成交量') }}</div>
						<div class="t2">{{ $formatMoney(details.volume, 0) }}</div>
					</div>
					<div class="flex flex-b item">
						<div class="t1">{{ $t('成交額') }}</div>
						<div class="t2">
							{{ $formatMoney(Number(details.turnoverM).toFixed(2)) }}
						</div>
					</div>
				</div>
			</div>
			<!-- 切換顯示 -->
			<!-- <div class="nav-box flex">
				<div class="nav-item" v-for="(item, index) in tabList" :key="index"
					:class="{ active: currmentIndex == item.type }" @click="changeNav(item.type)">{{ item.name }}
				</div>
			</div> -->
			<template v-if="currmentIndex == 1">
				<k-line :details="details" :symbol="symbol"></k-line>
				<div class="buy animate__animated animate__fadeIn">
					<div class="tab flex flex-b">
						<!-- <div class="tab-item flex flex-c" v-if="buyIndex==1">市場交易
							<img src="../../assets/v4/xl.png" style="width: 0.12rem;height: 0.07rem;" alt="" />
						</div>
						<div class="tab-item flex flex-c" v-if="buyIndex==2">限價交易
							<img src="../../assets/v4/xl.png" style="width: 0.12rem;height: 0.07rem;" alt="" />
						</div> -->
						<div class="tab-item flex flex-c" :class="{ active: !isLimit }" @click="setLimit(false)">{{ $t('市場交易') }}</div>
						<div class="tab-item flex flex-c" :class="{ active: isLimit }" @click="setLimit(true)">{{ $t('限價交易') }}</div>
					</div>
					<div class="flex flex-b">
						<!-- 限价 -->
						<template v-if="isLimit">
							<div class="items flex flex-b">
								<!-- <div class="tt flex-1">買入價格</div> -->
								<div class="flex-2" style="margin-top: 0.1rem;">
									<van-stepper v-model="buyPrice" />
								</div>
							</div>
							<div class="items flex flex-b">
								<!-- <div class="tt flex-1">買入張數(1張=1,000股)</div> -->
								<div class="flex-2" style="margin-top: 0.1rem;">
									<van-stepper v-model="buyAmount" />
								</div>
							</div>
							<!-- <div class="ipt">
								<div class="flex-1">{{ $t("sharesDetails").txt13 }}</div>
								<div class="flex-2">
									<van-stepper v-model="buyGang" />
								</div>
							</div> -->
						</template>
						<!-- 市价 -->
						<template v-if="!isLimit">
							<!-- <div class="ipt">
								<div class="flex-1">{{ $t("sharesDetails").txt11 }}</div>
								<div class="flex-2">
									<van-stepper v-model="buyPrice" />
								</div>
							</div> -->
							<div class="items flex flex-b">
								<!-- <div class="tt flex-1">買入張數(1張=1,000股)</div> -->
								<div class="flex-2" style="margin-top: 0.1rem;">
									<van-stepper v-model="buyAmount" />
								</div>
							</div>
							<!-- <div class="ipt">
								<div class="flex-1">
									{{ $t("sharesDetails").txt13 }}
								</div>
								<div class="flex-2">
									<van-stepper v-model="buyGang" />
								</div>
							</div> -->
						</template>
					</div>
					<div class="">
						<!-- <div class="items flex flex-b">
							<div class="tt flex-1">張數</div>
							<div class="tt1 flex-2">{{ $formatMoney(buyGu) }}</div>
						</div>
						<div class="items flex flex-b">
							<div class="tt flex-1">市價</div>
							<div class="tt1 flex-2">{{ $formatMoney(buySz) }}</div>
						</div>
						<div class="items flex flex-b">
							<div class="tt flex-1">手續費</div>
							<div class="tt1 flex-2">{{ $formatMoney(buyFuwu) }}</div>
						</div>
						<div class="items flex flex-b">
							<div class="tt flex-1">合計</div>
							<div class="tt1 flex-2">{{ $formatMoney(buyTotal) }}</div>
						</div>
						<div class="items flex flex-b">
							<div class="tt flex-1">可用餘額</div>
							<div class="tt1 flex-2">
								{{ $formatMoney(userInfo.zar) }}
							</div>
						</div> -->
						<div class="items flex flex-b" style="width: 100%;margin-top: 0.1rem;">
							<!-- <div class="tt flex-1">買賣方向</div> -->
							<div class="btns flex flex-b flex-2">
								<div class="bt" :class="{ active: buyType == 2 }" @click="changeBuyType(2)">{{ $t('跌') }}</div>
								<div class="bt bt1" :class="{ active: buyType == 1 }" @click="changeBuyType(1)">{{ $t('漲') }}</div>
							</div>
						</div>
					</div>
					<!-- <div class="item" @click="buyStock" style="margin: 0.12rem;">
						<div class="defbtn">買入</div>
					</div> -->
				</div>
			</template>
			<!-- 概括信息 -->
			<gkData :symbol="symbol" v-if="currmentIndex == 2" />
			<!-- 新闻列表 -->
			<newsList v-if="currmentIndex == 3" />
		</van-pull-refresh>
		<van-popup v-model="showBuy" position="bottom" :round="true" closeable>
			<div class="popup2">
				<div class="title2" :class="{ active: !isLimit }" @click="setLimit(false),showBuy=false,buyIndex=1">{{ $t('市場交易') }}
				</div>
				<div class="title2" :class="{ active: isLimit }" @click="setLimit(true),showBuy=false,buyIndex=2">{{ $t('限價交易') }}
				</div>
			</div>
		</van-popup>
		<!-- 取消顯示彈出 -->
		<van-popup v-model="show" position="bottom" :round="true" closeable>
			<div class="popup">
				<div class="title">
					<div class="name">{{ $t(details.name) }}</div>
					<div class="code">{{ $t(details.symbol) }}</div>
				</div>
				<div class="flex flex-b flex-wrap" style="margin: 0.2rem 0;">
					<!-- <div class="item flex flex-b">
						<div class="t">{{ $t("new").a59 }}</div>
						<div class="t1">{{ isLimit ? $formatMoney(buyPrice) : $t("sharesDetails").txt17 }}
						</div>
					</div> -->
					<div class="item flex">
						<div class="t">{{ $t('張數') }}:</div>
						<div class="t1">{{ $formatMoney(buyGu) }}</div>
					</div>
					<div class="item flex">
						<div class="t">{{ $t('市價') }}:</div>
						<div class="t1">{{ $formatMoney(buySz) }}</div>
					</div>
					<div class="item flex">
						<div class="t">{{ $t('手續費') }}:</div>
						<div class="t1">{{ $formatMoney(buyFuwu) }}</div>
					</div>
					<div class="item flex">
						<div class="t">{{ $t('合計') }}:</div>
						<div class="t1">{{ $formatMoney(buyTotal) }}</div>
					</div>
					<div class="item flex">
						<div class="t">{{ $t('可用餘額') }}:</div>
						<div class="t1">{{ $formatMoney(userInfo.zar) }}</div>
					</div>
				</div>
				<div class="b-btn" @click="buyStock">{{ $t('提交') }}</div>
			</div>
		</van-popup>
		<loading ref="loading" />
	</div>
</template>

<script>
	import kLine from "./components/k-line.vue";
	import gkData from "./components/gkData.vue";
	import newsList from "./components/newsList.vue";

	export default {
		name: "",
		props: {},
		data() {
			return {
				currmentIndex: 1,
				isLoading: false,
				userInfo: {},
				show: false,
				buyPrice: "",
				buyAmount: "",
				buyGang: "",
				buyType: 1,
				isLimit: false,
				details: {},
				detailTime: null,
				symbol: "",
				is_zixuan: false,
				cfg: {},
				navList: [{
						name: this.$t("menu").href2,
						type: 0,
					},
					{
						name: this.$t("newt").t42,
						type: 1,
					},
					{
						name: this.$t("newt").t43,
						type: 2,
					},
				],
				showBuy: false,
				buyIndex: 1,
				title: ''
			};
		},
		components: {
			kLine,
			gkData,
			newsList
		},
		created() {
			this.symbol = this.$route.query.symbol;
			this.requestDetail(true);
			this.getConfig();
			this.getUserInfo();
			this.detailTime = setInterval(() => {
				this.requestDetail();
			}, 5000);
		},
		beforeDestroy() {
			!!this.detailTime && clearInterval(this.detailTime);
		},
		computed: {
			tabList() {
				return [{
						name: this.$t("行情"),
						type: 1
					},
					{
						name: this.$t("概括"),
						type: 2
					},
					{
						name: this.$t("資訊"),
						type: 3
					},
				];
			},
			// 股数
			buyGu() {
				if (this.cfg.buytype == 1) {
					// 买入类型(1手)
					return parseInt(this.buyAmount) * 1000;
				}
				return 0;
			},
			// 市值
			buySz() {
				let scale = this.cfg.gtype == 1 ? this.buyGang : 1;
				return this.buyBj * scale;
			},
			// 本金
			buyBj() {
				//按手
				if (this.cfg.buytype == 1) {
					return this.buyGu * this.buyPrice;
				}
				//按万
				return Number(this.buyAmount) * this.buyPrice;
			},
			// 服务费
			buyFuwu() {
				let val = this.buySz * Number(this.cfg.buycharge);

				if (val < this.cfg.minshouxu) {
					return Number(this.cfg.minshouxu);
				}
				return val;
			},
			// 合计
			buyTotal() {
				return this.buyBj + this.buyFuwu;
			},
		},
		methods: {
			goBack() {
				this.$router.go(-1)
			},
			changeNav(type) {
				this.currmentIndex = type;
			},
			// 下拉刷新
			onRefresh() {
				this.currmentIndex = 1;
				this.requestDetail(true);
				this.getConfig();
				this.getUserInfo();
			},
			setLimit(type) {
				this.isLimit = type;
				if (type == 2) this.buyPrice = this.details.price; //初始价格
			},
			changeBuyType(type) {
				this.buyType = type;
				this.show=true
			},
			getUserInfo() {
				this.$server.post("/user/getUserinfo", {}).then((res) => {
					if (res.status == 1) {
						this.userInfo = res.data;
					}
				});
			},
			// 获取配置logo
			async getConfig() {
				const res = await this.$server.post("/common/config", {
					type: "zar"
				});
				let val = {};
				res.data.forEach((vo) => {
					val[vo.name] = vo.value;
				});

				let arr = val.ganggang.split("/");
				this.buyGang = parseInt(arr[0]); //杠杆倍数给默认值

				this.cfg = val;
			},
			requestDetail(isInit) {
				this.$server
					.post("/trade/stockdetails", {
						symbol: this.symbol,
						type: "zar"
					})
					.then((res) => {
						this.isLoading = false; //下拉刷新状态

						this.details = res.data;
						this.title = this.details.local_name + this.details.symbol
						if (isInit) {
							this.buyPrice = this.details.price; //初始价格
							this.getMine();
						}
					});
			},
			buyStock() {
				this.$refs.loading.open(); //开启加载
				//普通购买
				this.$server
					.post("/trade/buy_stock", {
						symbol: this.details.symbol,
						zhang: this.buyAmount,
						ganggan: this.buyGang,
						buyzd: this.buyType, //1 up ，2down
						buy_price: this.buyPrice,
						is_type: 0,
						gdlx: this.isLimit ? 2 : 1,
						// type: this.isLimit ? 2 : 1,
						type: "zar",
					})
					.then((res) => {
						this.$refs.loading.close(); //关闭加载
						if (res.status == 1) {
							this.show = false;
							this.$toast(this.$translateServerText(res.msg));
							setTimeout(() => {
								this.$toPage("/trade/index"); //跳转持仓
							}, 1000);
						}
					});
			},
			getMine() {
				this.$server.post("/user/Optionallist", {
					type: "zar"
				}).then((res) => {
					if (res.status == 1) {
						// 判断当前是否在自选列表里面
						let arr = res.data.filter(
							(item) => item.symbol == this.details.symbol
						);
						if (arr.length) this.is_zixuan = true;
					}
				});
			},
			addSelect(obj) {
				this.$refs.loading.open(); //加载

				if (!this.is_zixuan) {
					this.$server
						.post("/user/addOptional", {
							symbol: obj.symbol,
							type: "zar"
						})
						.then((res) => {
							this.$refs.loading.close();

							if (res.status == 1) {
								this.is_zixuan = true;
							}
						});
				} else {
					this.$server
						.post("/user/removeOptional", {
							symbol: obj.symbol,
							type: "zar"
						})
						.then((res) => {
							this.$refs.loading.close();

							if (res.status == 1) {
								this.is_zixuan = false;
							}
						});
				}
			},
		},
	};
</script>

<style scoped lang="less">
	::v-deep .van-stepper__minus {
		width: 0.25rem !important;
		height: 0.25rem !important;
		font-weight: bold !important;
		border: none !important;
		border-radius: 0.03rem !important;
		background: transparent !important;
		color: #999 !important;
	}

	::v-deep .van-stepper__plus {
		width: 0.25rem !important;
		height: 0.25rem !important;
		font-weight: bold !important;
		border: none !important;
		border-radius: 0.03rem !important;
		background: transparent !important;
		color: #999 !important;
	}

	::v-deep .van-stepper {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 0.38rem;
		background: #434446;
		border-radius: 0.06rem;
		border: 0.01rem solid #A0A0A0;
		padding: 0 0.1rem;

		.van-stepper__input {
			flex: 1;
			background: transparent;
			height: 100%;
			margin: 0;
			font-family: PingFang SC;
			font-weight: 500;
			font-size: 0.14rem;
			color: #FFFFFF;
		}
	}

	::v-deep .van-stepper__minus,.van-stepper__plus {
		background: transparent !important;
		width: 0.36rem;
		height: 0.36rem;
		color: #999 !important;
	}
	.page {
		padding: 0.5rem 0rem 0;
		min-height: 100vh;
		.scsc {
			position: fixed;
			z-index: 999;
			right: 0.12rem;
			top: 0.16rem;
		}
	}

	.popup {
		padding: 0.2rem 0.1rem;
		background: linear-gradient(0deg, #434446, #18191B);
		border-radius: 0.1rem 0.1rem 0rem 0rem;
		.title {
			margin-bottom: 0.1rem;
			text-align: center;

			.name {
				font-family: PingFang SC;
				font-weight: 500;
				font-size: 0.14rem;
				color: #FFFFFF;
			}

			.code {
				font-family: PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
				color: #999999;
			}
		}

		.item {
			width: 48%;
			padding: 0.05rem 0;

			.t {
				font-family: PingFang SC;
				font-weight: 400;
				font-size: 0.12rem;
				color: #999999;
			}

			.t1 {
				margin-left: 0.05rem;
				font-family: PingFang SC;
				font-weight: 400;
				font-size: 0.12rem;
				color: #fff;
			}
		}

		.b-btn {
			margin: 0.1rem 0 0;
		}
	}

	.popup2 {
		padding: 0.12rem;

		.title2 {
			text-align: center;
			padding: 0.12rem 0;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 0.16rem;
			color: #333333;
		}
	}

	.buy {
		padding: 0.1rem 0.12rem 0.25rem;
		background: #434446;
		border-radius: 0.25rem 0.25rem 0rem 0rem;
		.tab {
			height: 0.38rem;
			background: #18191B;
			border-radius: 0.19rem;
			padding: 0.03rem;
			.tab-item {
				width: 48%;
				height: 0.31rem;
				border-radius: 0.16rem;
				font-family: PingFang SC;
				font-weight: 500;
				font-size: 0.13rem;
				color: #666666;
				line-height: 0.31rem;
				&.active {
					color: #000000;
					background: #8DFD99;
					position: relative;
					// &::after {
					// 	position: absolute;
					// 	content: '';
					// 	bottom: 0;
					// 	left: 0;
					// 	width: 100%;
					// 	height: 0.03rem;
					// 	background-color: #01655d;
					// }
				}
			}
		}

		.ipt {
			margin: 0.1rem 0;
			padding: 0.1rem;
			background: #fff;
		}

		.items {
			width: 48%;
			padding: 0.06rem 0;
			.tt {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 0.14rem;
				color: #fff;
			}

			.tt1 {
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 0.14rem;
				color: #000;
				text-align: right;
			}
		}
		.btns {
			.bt {
				text-align: center;
				width: 48%;
				height: 0.5rem;
				background: linear-gradient(90deg, #98EF86, #C7F377);
				border-radius: 0.25rem;
				font-family: PingFang SC;
				font-weight: 500;
				font-size: 0.16rem;
				color: #000000;
				line-height: 0.5rem;

				&.bt1 {
					background: #FF5683;
				}
			}
		}

		.b-btn {
			margin: 0.2rem 0;
		}
	}

	.nav-box {
		margin: 0.12rem;
		padding: 0.02rem;
		height: 0.4rem;
		background: #FFFFFF;
		border-radius: 0.04rem 0.04rem 0.04rem 0.04rem;
		.nav-item {
			width: calc(100%/3);
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 0.14rem;
			color: #6F6F6F;
			line-height: 0.36rem;
			text-align: center;
			position: relative;
			// &::after {
			// 	content: "";
			// 	width: 100%;
			// 	height: 0.03rem;
			// 	position: absolute;
			// 	bottom: 0;
			// 	left: 50%;
			// 	transform: translateX(-50%);
			// 	background: transparent;
			// 	border-radius: 0.3rem;
			// }
			&.active {
				height: 0.36rem;
				background: linear-gradient( 90deg, #1B167A 0%, #4383C7 100%);
				border-radius: 0.04rem 0.04rem 0.04rem 0.04rem;
				color: #fff;
				&::after {
					background: #E5C79F;
				}
			}
		}
	}

	.top-data {
		margin: 0.12rem 0.12rem 0;
		padding: 0.12rem;
		background: #232429;
		border-radius: 0.19rem;
		.tp {
			.name {
				font-family: PingFang SC;
				font-weight: 500;
				font-size: 0.14rem;
				color: #FFFFFF;
			}

			.code {
				font-family: PingFang SC;
				font-weight: 400;
				font-size: 0.11rem;
				color: #666666;
				margin-top: 0.1rem;
			}

			.time {
				margin: 0.1rem 0 0.05rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.1rem;
				color: #999999;
			}

			.price {
				font-family: PingFang SC;
				font-weight: 500;
				font-size: 0.15rem;
			}

			.t {
				font-family: PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
			}
		}

		.red {
			color: #EE1515;
		}

		.green {
			color: #00CB29;
		}
	}

	.list {
		margin: 0.12rem 0 0;
		flex-wrap: wrap;
		.item {
			width: 46%;
			line-height: 0.2rem;
			.t1 {
				font-family: PingFang SC;
				font-weight: 400;
				font-size: 0.11rem;
				color: #999999;
			}

			.t2 {
				font-family: PingFang SC;
				font-weight: 400;
				font-size: 0.11rem;
				color: #999999;
			}
		}
	}
</style>