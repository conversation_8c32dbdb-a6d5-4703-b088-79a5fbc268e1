<template>
	<div>
		<div class="sousuo" @click="tianjia">
			<span></span>
			<input :placeholder="$t('名称/代码')" disabled />
		</div>
		
	</div>
</template>
<script type="text/javascript">
export default {
	name:"serch",
	props:[""],
	data(){
		return {
			
		}
	},
	components:{
		
	},
	methods:{
		tianjia(){
			this.$router.push({path:'/Quotes/Inquire'})
		},
	},
	mounted(){
		
		
	},
}
</script>
<style type="text/css" lang="less" scoped="scoped">
	input::-webkit-input-placeholder{
	  color: #FFD2CC;
	  font-size: 0.14rem;
	}
.sousuo{
	width: 3.5rem;
	height: 0.34rem;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 0.17rem;
	margin:0 auto;
	display: flex;
	span{
		width:0.16rem;
		height:0.16rem;
		background: url(../../assets/img/home/<USER>
		background-size:100%;
		margin-left:0.1rem;
		margin-top:0.08rem;
	}
	input{
		height:0.34rem;
		margin-left:0.07rem;
		width:2.8rem;
		background: transparent;
	}
}

</style>
