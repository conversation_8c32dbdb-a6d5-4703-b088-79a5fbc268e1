<template>
	<div class="page ">
		<top-back :title="$t('關於我們')"></top-back>

		<div class="list">
			<!-- <div class="flex flex-c">
				<img src="../../assets/v5/gywmIcon.png" style="width: 3.36rem;height:2.19rem;" alt="" />
			</div> -->
			<div class="item" v-html="info"></div>
		</div>

		<no-data v-if="!this.info"></no-data>
	</div>
</template>

<script>
	export default {
		name: "aboutUs",
		props: {},
		data() {
			return {
				info: "",
			};
		},
		components: {},
		methods: {
			getInfo() {
				this.$server
					.post("/common/wenben", {
						name: this.$t("关于我们"),
						type: "twd"
					})
					.then((res) => {
						this.info = res.data.content;
					});
			},
		},
		created() {
			this.getInfo();
		},
		computed: {},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.6rem 0.1rem 0;
		min-height: 100vh;
	}

	.list {
		.item {
			line-height: 0.2rem;
			margin-bottom: 0.1rem;
		}
	}
</style>