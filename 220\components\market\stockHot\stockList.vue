<template>
	<div class="stockList">
		<div class="header">
			<top :title="$t('market').stockHot.title"></top>
		</div>
		<div class="box">
			<div class="tabs">
				<div class="icon lb" @click="show = true">
					<svg t="1710491696230" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4269" width="24" height="24">
						<path d="M0 51.2h1024v102.4H0zM0 307.2h819.2v102.4H0zM0 563.2h614.4v102.4H0zM0 819.2h409.6v102.4H0z" fill="#333333" p-id="4270"></path>
					</svg>
				</div>
				<div class="cot">
					<div class="tab">
						<div class="tab-item" :class="{ active: type == item.type }" v-for="(item, i) in typeList" :key="i" @click="changeType(item.type)">{{ $t(item.name) }}</div>
					</div>
				</div>
			</div>
		</div>
		
	</div>
</template>

<script>
	import Vue from 'vue';
	import qs from 'qs';
	import axios from 'axios';
	import {
		Toast
	} from 'vant';
	Vue.use(Toast);
	import top from "../../bar/toper.vue";
	export default {
		name: "stockHot",
		data() {
			return {
				type1: 0,
				typeList1: [
					{name: '股票价格',type: '0'},
					{name: '交易量',type: '1'},
					{name: '投资者',type: '2'},
					{name: '金融',type: '3'},
					{name: '利润',type: '4'},
					{name: '股票',type: '5'},
					{name: '卖空',type: '6'}
				],
				typeList: [
					{name: '上升率',type: 'returns_top',arr: [{name: '天',type: 'returns_top'},{name: '1周',type: 'one_week_returns_top'},{name: '1个月',type: 'one_month_returns_top'},{name: '3个月',type: 'three_month_returns_top'},{name: '6个月',type: 'six_month_returns_top'}]},
					{name: '下降率',type: 'returns_bottom',arr: [{name: '天',type: 'returns_bottom'},{name: '1周',type: 'one_week_returns_bottom'},{name: '1个月',type: 'one_month_returns_bottom'},{name: '3个月',type: 'three_month_returns_bottom'},{name: '6个月',type: 'six_month_returns_bottom'},]},
					{name: '申报价',type: 'new_high_price'},
					{name: '新低价',type: 'new_low_price'},
					{name: '上限',type: 'upper_limit_price'},
					{name: '下限',type: 'lower_limit_price'},
					{name: '突破上限',type: 'out_of_upper_limit_price'},
					{name: '突破下限',type: 'out_of_lower_limit_price'}
				],
				type:''
			}
		},
		components: {
			top
		},
		methods: {
			getInfo() {
				this.$server.post('/hanguo/sczb', { 
					type: this.type ,
				}).then(res => {
					if (res.data.status == 1) {
						let data = Object.values(res.data.data);
						data.forEach(item => {
							// 涨跌幅
							item.bd = (((item.close - item.prev_close) / item.prev_close) * 100).toFixed(2);
						});
			
						this.list = [...data];
					}
				});
			},
			clickDetail(item){
				var link = "/market/marketDetail?symbol="+item.code;
				this.clickNext(link);
			}
		},
		destroyed() {
			if(this.timeInit){
				clearInterval(this.timeInit)
			}
		},
		mounted() {
			var _this = this;
			this.getInfo();
		},
	}
</script>

<style lang="less">
	.stockList{
		padding-top:.44rem;
		background: #f8f8f8;
		min-height: 100vh;
		.header{
			width:100%;height:.44rem;
			background: #fff;
			font-weight: 500;
			font-size: .18rem;
			color: #000000;
			position: fixed;top:0;left:0;z-index: 888;
			img{
				width:.17rem;height:.16rem;
				right:.16rem;top:.16rem;
				position: absolute;
			}
		}
		.box{
			margin:.12rem;padding:.15rem;
			align-items: center;
			position: relative;z-index: 300;
			background: #FFFFFF;
			box-shadow: 0 0 .08rem 0 rgba(0,0,0,0.08);
			border-radius: .1rem;
		}
		.tabs {
			position: relative;
			padding-left: .4rem;
			border-bottom: .01rem solid #f7f8f8;
			.lb {
				position: absolute;
				left: .1rem;
				top: 50%;
				transform: translateY(-50%);
			}
		
			.cot {
				overflow: scroll;
				.tab {
					/* min-width: 260vw; */
					white-space: nowrap;
					overflow:scroll;
					.tab-item {
						display:inline-block;
						color: #999999;
						min-width: 10%;
						position: relative;
						padding: .1rem .15rem;
						text-align: center;
						&::after {
							content: '';
							width: 100%;
							height: .02rem;
							position: absolute;
							bottom: 0;
							left: 0;
						}
						&.active {
							font-weight: 600;
							color: #385aa0;
							&::after {
								background: #385aa0;
							}
						}
					}
				}
			}
		}
	}
</style>