<template>
	<div class="positionDetail">
		<div class="headf">
			<top :title="$t('positionDetail').title"></top>
		</div>
		<div class="topHead">
			<div class="tit">{{$t('other').txt15}}</div>
			<div class="top-name">
				<div class="item flex flex-b">
					<div class="flex" style="align-items: flex-end;">
						<div>{{sharesNewDetails.stock_name}}</div>
						<div class="code">{{sharesNewDetails.stock_code}}</div>
					</div>
					<span :class="parseFloat(gain)>=0?'green':'red'">{{$formatMoney(gain)}}%</span>
					<div class="status flex">{{statusStr[sharesNewDetails.status]}}</div>
				</div>
				<div class="item flex flex-b">
					<div>{{$t('positionDetail').txt19}}</div>
					<span>{{$formatMoney(gainValue)}}円</span>
				</div>
			</div>
		</div>
		<div class="tit">{{$t('positionDetail').txt18}}</div>
		<div class="list">
			<div class="list-item">
				<div class="item-left">
					{{$t('positionDetail').txt2}}
				</div>
				<div class="item-right" v-if="parseInt(sharesNewDetails.buyzd)===1">{{$t('positionDetail').txt14}}</div>
				<div class="item-right" v-if="parseInt(sharesNewDetails.buyzd)===2">{{$t('positionDetail').txt15}}</div>
			</div>
			<div class="list-item">
				<div class="item-left">
					{{$t('positionDetail').txt3}}
				</div>
				<div class="item-right">
					{{sharesNewDetails.strategy_num}}
				</div>
			</div>
			<div class="list-item">
				<div class="item-left">
					{{$t('positionDetail').txt4}}
				</div>
				<div class="item-right">
					{{$formatDate('DD EE YY hh:mm',sharesNewDetails.buy_time*1000)}}
				</div>
			</div>
			<div class="list-item">
				<div class="item-left">
					{{$t('positionDetail').txt5}}
				</div>
				<div class="item-right">
					{{$formatMoney(sharesNewDetails.buy_price)}}円
				</div>
			</div>
			<div class="list-item" v-if="sharesNewDetails.sell_time">
				<div class="item-left">
					{{$t('positionDetail').txt12}}
				</div>
				<div class="item-right">
					{{$formatDate('DD EE YY hh:mm',sharesNewDetails.sell_time*1000)}}
				</div>
			</div>
			<div class="list-item" v-if="sharesNewDetails.sell_time">
				<div class="item-left">
					{{$t('positionDetail').txt13}}
				</div>
				<div class="item-right">
					{{$formatMoney(sharesNewDetails.sell_price)}}円
				</div>
			</div>
			<div class="list-item">
				<div class="item-left">
					{{$t('positionDetail').txt6}}
				</div>
				<div class="item-right">
					{{$formatMoney(sharesNewDetails.stock_num,0)}}
				</div>
			</div>
			<div class="list-item">
				<div class="item-left">
					{{$t('positionDetail').txt7}}
				</div>
				<div class="item-right">
					{{$formatMoney(sharesNewDetails.credit)}}円
				</div>
			</div>
			<div class="list-item">
				<div class="item-left">
					{{$t('positionDetail').txt8}}
				</div>
				<div class="item-right">
					{{$formatMoney(sharesNewDetails.ganggang,0)}}
				</div>
			</div>
			<div class="list-item">
				<div class="item-left">
					{{$t('positionDetail').txt9}}
				</div>
				<div class="item-right">
					{{$formatMoney(sharesNewDetails.market_value)}}円
				</div>
			</div>
			<div class="list-item">
				<div class="item-left">
					{{$t('positionDetail').txt10}}
				</div>
				<div class="item-right">
					{{sharesNewDetails.type==1?$t('positionDetail').txt17:$t('positionDetail').txt16}}
				</div>
			</div>
		</div>
	</div>
</template>

<script>
	import Vue from "vue";
	import qs from "qs";
	import axios from "axios";
	import {
		Dialog,
		Toast
	} from "vant";
	Vue.use(Dialog).use(Toast);
	import top from "../bar/toper.vue";

	export default {
		name: "positionDetail",
		data() {
			return {
				id: '',
				userInfo: {},
				sharesNewDetails: {
					spread: 0,
				},
				type: 0,
				num: '0',
				stock_code: '',
				statusStr: [this.$t('position').txt28, this.$t('position').txt29, this.$t('position').txt31, this.$t(
					'position').txt32, ''],
				nowprice: 0,
				gain: 0,
				gainValue: 0
			};
		},
		components: {
			top,
		},
		computed: {

		},
		beforeDestroy() {

		},
		mounted() {
			this.id = this.$route.query.id;
			this.nowprice = this.$route.query.nowprice;
			this.gain = this.$route.query.gain;
			this.gainValue = this.$route.query.gainValue;
			this.getStockInfo();
		},
		methods: {
			getStockInfo() {
				this.$server.post('/trade/stockdetail', {
					id: this.id,
          type: 'jpy'
				}).then(res => {

					if (res.data.status === 1 || res.data.status == '1') {
						this.sharesNewDetails = res.data.data;
					}
				})
			}
		},

	};
</script>

<style lang="less">
	.positionDetail {
		background: #0f161c;
		min-height: 100vh;
		padding-bottom: 1rem;
		.tit{
			padding: 0.05rem 0.12rem;
			background-color: rgba(94, 213, 168, 0.2);
			display: flex;
			align-items: center;
			justify-content: space-between;
			border-bottom: .03rem solid #fff;
			font-family: PingFangSC, PingFang SC;
			font-weight: 600;
			font-size: 0.14rem;
			color: #FFFFFF;
		}
		.topHead {
			.top-name {
				font-size: .16rem;
				font-family: FZLanTingHeiT-R-GB;
				font-weight: 400;
				color: #fff;
				padding: 0 0.12rem;
				line-height: 0.5rem;
				.item{
					border-bottom: 0.01rem solid rgba(255, 255, 255, 0.16);
					.code {
						margin-left:0.05rem;
						font-family: PingFangSC, PingFang SC;
						font-weight: 500;
						font-size: 0.1rem;
						color: #B3B8B8;
					}
					span {
						font-size: .16rem;
						display: block;
						font-family: FZCuHei-B03T;
						font-weight: bold;
					}
					.status {
						font-family: PingFangSC, PingFang SC;
						font-weight: 500;
						font-size: 0.13rem;
						color: #999;
					}
				}
				
			}
		}

		.list {
			padding: 0 0.12rem;
			.list-item {
				border-bottom: 0.01rem solid rgba(255, 255, 255, 0.16);
				line-height: 0.5rem;
				justify-content: space-between;
				display: flex;
				align-items: center;

				.item-left {
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.13rem;
					color: #B3B8B8;
				}

				.item-right {
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.13rem;
					color: #FFFFFF;
				}
			}
		}
	}
</style>