<template>
	<div class="page ">
		<top-back :title="$t('個資法告知事項')"></top-back>

		<div class="list">
			<div class="item">
				金和順投資有限公司
			<br/><br/>	個人資料保護法第八條第一項告知事項
			<br/><br/>	金和順投資有限公司（以下稱「本公司」）依據個人資料保護法第8條第1項規定，特此向您告知下列事項，請詳細閱讀：

			<br/><br/>	一、個人資料蒐集目的：
			<br/><br/>	本公司蒐集您的個人資料目的如下：
			<br/><br/>	1.為辦理受託買賣國內外有價證券、財富管理、衍生性金融商品交易、短期票券、承銷、股務、期貨、其他經目的事業主管機關許可及符合本公司營業登記項目之業務，所為之個人資料蒐集、處理及利用。
			<br/><br/>	2.為辦理客戶管理、行銷、服務、業務推廣及相關統計調查、研究分析，及依主管機關規定之必要事項。
			<br/><br/>	3.為遵循美國海外帳戶稅收遵循法（FATCA）等國內外相關法令要求。

			<br/><br/>	個人資料之類別：
			<br/><br/>	蒐集的個人資料類型包括但不限於：
			<br/><br/>	辨識個人資料（C001姓名、出生年月日、身分證號碼、聯絡電話、電子郵件、地址）。
			<br/><br/>	財務辨識資料（例如銀行帳戶號碼）。
			<br/><br/>	財務資料、信用評等資料及投資相關資訊（如交易紀錄、資產與投資狀況等）。

			<br/><br/>	個人資料利用期間：
			<br/><br/>	依下列原則訂定：
			<br/><br/>	依法令規定或主管機關指定之保存期間。
			<br/><br/>	本公司執行業務所必須之期間。
			<br/><br/>	與您同意之個人資料保存期限。

			<br/><br/>	個人資料利用地區：
			<br/><br/>	本公司個人資料之利用地區包含：
			<br/><br/>	本公司營業所在地區。
			<br/><br/>	因業務需要訂有契約之相關合作機構所在地區。
			<br/><br/>	國際傳輸個人資料需未受中央主管機關限制之地區。
			<br/><br/>	其他依法或主管機關要求必要提供之國內外機構所在地。

			<br/><br/>	個人資料利用之對象：
			<br/><br/>	本公司將於法令許可範圍內，依據業務需要，將您的個人資料提供給下列對象：
			<br/><br/>	本公司及其關係企業。
			<br/><br/>	因業務合作所訂有契約關係之金融機構、律師、會計師、顧問、合作機構等。
			<br/><br/>	財團法人金融聯合徵信中心、票據交換所、聯合信用卡處理中心、金融資訊服務中心、交割銀行、中華民國證券集中保管公司及其他主管機關授權之相關機構。
			<br/><br/>	國內外政府機構、國內外稅務機關，或依法有權要求提供之第三方。

			<br/><br/>個人資料利用之方式：
			<br/><br/>	透過自動化機器或其他非自動化方式，蒐集、處理及利用您的個人資料，以提供您所需服務。

			<br/><br/>	個人資料之權利行使方式：
			<br/><br/>	您可透過下列方式向本公司申請：
			<br/><br/>	查詢、閱覽或請求製給複製本（本公司得依法酌收成本費用）。
			<br/><br/>	補充、更正個人資料。
			<br/><br/>	停止蒐集、處理或利用、請求刪除個人資料（依法執行業務所必須者不在此限）。

				<br/><br/>️ 拒絕提供個人資料之影響：
			<br/><br/>	若您拒絕提供所需之個人資料，將導致本公司無法執行業務上必要之審核與處理，或提供完整的服務，可能拒絕或終止業務往來。
			<br/><br/>	若您未配合提供資料以遵循美國海外帳戶稅收遵循法（FATCA），本公司須依法將您的帳戶列為「不合作帳戶」（Recalcitrant Account），並可能依法關閉您的帳戶。

				<br/><br/>客戶聯繫方式：
			<br/><br/>	若您有任何與個資告知事項相關疑問或申請，請透過以下方式聯絡我們：
			<br/><br/>	客服專線：09-5813-3434
				<br/><br/>電子郵件：<EMAIL>

			<br/><br/>	特此通知，敬請知悉並感謝您的配合與信任。
			<br/><br/>	金和順投資有限公司
			<br/><br/>	2025年3月6日

			</div>
		</div>

	</div>
</template>

<script>
	export default {
		name: "aboutUs",
		props: {},
		data() {
			return {
				info: "",
			};
		},
		components: {},
		methods: {
			getInfo() {
				this.$server
					.post("/common/wenben", {
						name: this.$t("关于我们"),
						type: "twd"
					})
					.then((res) => {
						this.info = res.data.content;
					});
			},
		},
		created() {
			this.getInfo();
		},
		computed: {},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.6rem 0.1rem 0;
		min-height: 100vh;
	}

	.list {
		.item {
			line-height: 0.2rem;
			margin-bottom: 0.1rem;
			color: #fff;
		}
	}
</style>