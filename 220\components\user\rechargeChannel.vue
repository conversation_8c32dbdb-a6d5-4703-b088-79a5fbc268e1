 <template>
 	<div class="rechargeChannel">
		<div class="head">
			<top :title="text" :rech="true"></top>
		</div>
		<div class="tongd flex flex-b">
			<a v-if="i < 5" :class="{xuan:bnm==i}" v-for="(item,i) in logList" :key='i' @click="dianji(i)">{{$t("recharge").txt8}}{{i}}</a>
		</div>
		<div class="kun">
			<ul class="shuru">
				<li>
					<p>
						{{$t("recharge").txt10}}:
						<span class="red">{{money}}</span>
					</p>
				</li>
				<li>
					<p>
						{{$t("recharge").txt11}}:
						<span>{{$t('recharge').txt9}}</span>
						<!-- <span v-if="shuju">{{shuju.name}}</span> -->
					</p>
					<a @click="copy(shuju.name)"></a>
				</li>
				<li>
					<p>
						{{$t("recharge").txt12}}:
						<span>{{$t('recharge').txt9}}</span>
						<!-- <span v-if="shuju">{{shuju.bankcard}}</span> -->
					</p>
					<a @click="copy(shuju.bankcard)"></a>
				</li>
				<li>
					<p>
						{{$t("recharge").txt13}}:
						<span>{{$t('recharge').txt9}}</span>
						<!-- <span v-if="shuju">{{shuju.bankname}}</span> -->
					</p>
					<a @click="copy(shuju.bankname)"></a>
				</li>
				<li>
					<p>
						{{$t("recharge").txt14}}:
						<span>{{$t('recharge').txt9}}</span>
						<!-- <span v-if="shuju">{{shuju.bankperson}}</span> -->
					</p>
					<a @click="copy(shuju.bankperson)"></a>
				</li>
			</ul>
			<div class="upload">
				<div>{{$t('recharge').txt15}}</div>
				<div class="flex flex-c">
					<div class="upload-box text-center">
						<div class="img">
							<img :src="imgIco" v-if="img" />
							<img src="../../assets/skin/mine/bandCardImg2.png" v-if="!img" />
							<input accept="image/*" type="file" @change="changeing($event)" />
						</div>
					</div>
				</div>
			</div>
			<div class="btn-big" @click="shurer">{{$t('recharge').txt6}}</div>
		</div>

 	</div>
 </template>
 <script type="text/javascript">
 	import Vue from 'vue';
 	import qs from 'qs';
 	import axios from 'axios';
	import top from "../bar/toper.vue";
	import imgurl from "../../lib/common.js";
 	import {
 		Popup
 	} from 'vant';
 	Vue.use(Popup);
 	import {
 		Toast
 	} from 'vant';
 	Vue.use(Toast);
 	export default {
 		name: "rechargeChannel",
 		data() {
 			return {
				text: this.$t('recharge').txt7,
 				bnm: 1,
				logList:[],
				shuju:{},
				money:'',
				img:'',
				imgIco:''
 			}
 		},
		components: {
			top,
		},
 		methods: {
			getInfo(){
				this.$server.post("/common/recharge_channel",{type: 'jpy'}).then((str) => {
					if (str.data.status == 1) {
						let arr = [];
						for (let key in str.data.data) {
							let obj = str.data.data[key];
							arr.push(obj);
						}
						this.logList = arr;
					} else {
						Toast({
							message: this.$t(str.data.msg),
							duration: 2000,
						});
					}
				});
			},
 			/* change() {
 				this.$emit('closepop')
 			}, */
 			dianji(e) {
 				//this.$emit('bianhuan', e)
 				this.bnm = e;
 			},
 			copy(e) {

 				this.$copyText(e).then(e => {

 					Toast({
 						message: this.$t("复制成功"),
 						duration: 2000
 					});
 				}, e => {

 				})
 			},
 			shurer() {
				let _this = this
				var datas = qs.stringify({
					money: this.money,
          rjpz:this.img,
          type: 'jpy'
				});
				
				this.$server.post("/user/chongzhi", datas).then((res) => {
					if(res.data.status == 1){
						Toast({
							message: this.$t("充值提交成功"),
							duration: 2000,
						});
						setTimeout(function() {
							_this.$router.push({
								path: "/user/usercenter"
							});
						}, 2000)
					}else {
						Toast({
							message: this.$t(res.data.msg),
							duration: 2000,
						});
					}
					
				})
 			},
			changeing(e) {
				var file = e.target.files[0];
				var that = this;
			
				var formdata = new FormData();
			
				formdata.append("card", file);
			
				this.$server.post("/common/upload1", formdata).then((res) => {
					if (res.data.status == 1) {
						that.img = res.data.data;
						Toast({
							message: this.$t("bandCard").tip6,
							duration: 3000,
						});
						// this.imgz = "api/"+res.data.data;
						this.imgIco = imgurl.url.imgUrls + res.data.data;
					}
			    })
			},

 		},
 		destroyed() {

 		},
 		mounted() {
			this.money = this.$route.query.money;
			this.getInfo();
 		},
 	}
 </script>
 <style type="text/css" lang="less" scoped="scoped">
 	input::-webkit-input-placeholder,
 	textarea::-webkit-input-placeholder {
 		color: #CCC;
 		font-size: 0.12rem;
 	}
	.rechargeChannel{
		background:#0f161c;
		min-height: 100vh;
		.tongd {
			padding: 0.05rem 0.1rem 0;
			display: flex;
			flex-wrap: wrap;
			background-color: #424e4e;
			border-bottom: 0.01rem solid #5ED5A8;
			a {
				width: 22%;
				background: rgba(0, 0, 0, 0.2);
				border-radius: 0.1rem 0.1rem 0rem 0rem;
				font-weight: 500;
				font-size: 0.12rem;
				color: #B3B8B8;
				padding: 0.1rem 0;
				text-align: center;
				position: relative;
				
				&.xuan {
					background: #0F161C;
					color: #fff;
					border: 0.01rem solid #5ED5A8;
					border-bottom: none;
					position: relative;
					&::after{
						content: '';
						position: absolute;
						left: 0;
						bottom: -0.05rem;
						width: 100%;
						height: 0.05rem;
						background: #0F161C;
					}
				}
			}
		}
		.kun {
			margin: 0.12rem;
			margin-top:0rem;
			padding-bottom: 0.26rem;
			border-radius: 0.06rem;
			overflow: hidden;
		
			
		
			.shuru {
				width: 3.5rem;
				margin: 0 auto;
		
				li {
					display: flex;
					padding: .16rem 0;
					justify-content: space-between;
					border-bottom: 0.01rem solid rgba(255, 255, 255, 0.16);
					align-items: center;
		
					p {
						font-weight: 500;
						font-size: 0.13rem;
						color: #B3B8B8;
						flex: 1;
		
						span {
							font-weight: 500;
							font-size: 0.13rem;
							color: #5ED5A8;
							word-break: break-all;
						}
						.red{
							color:#ED3833;font-size: .18rem;
						}
					}
					
					a {
						width: 0.15rem;
						height: 0.16rem;
						background: url(../../assets/v2/copy.png)no-repeat center/100%;
						background-size: 100%;
					}
				}
			}
		
			.btn-big {
				margin-top: 0.25rem;
				
			}
		}
		.upload{
			font-weight: 500;
			font-size: .15rem;
			color: #fff;
			margin:.3rem 0;
			.upload-box{
				width:1.61rem;
				font-weight: 400;
				font-size: .1rem;
				color: #28B2AD;
				margin-top:.25rem;
				position:relative;
				.img{
					width:1.7rem;
					height:1.06rem;
					border-radius: .04rem;
					margin: 0 auto .1rem auto;
					position: relative;
					input{
						width:100%;height:100%;
						opacity: 0;
						position: absolute;top:0;left:0;
					}
				}
				img{
					width:100%;height:100%;display: block;
				}
				
			}
		}
	}
 	
 </style>