<template>
	<div class="exchange">
		<div class="headf">
			<top :title="$t(text)"></top>
		</div>

		<div class="cot">
			<template v-if="changeIndex">
				<div class="input-box">
					<div class="mb-20">
						<div class="flex flex-b mb-20">
							<div class="title flex">
								<img src="../../assets/skin/mine/usd.png" />
								{{ $t(to) }}
							</div>
							<div class="user" v-if="changeIndex">
								<span>{{ $t("mine").money.txt3 }}:</span>
								<span class="txt">{{ money }}{{ $t(to) }}</span>
							</div>
						</div>

						<input v-model="usMoney" @input="usInput" class="ipt flex-1" :placeholder="$t('exchange').txt1"
							type="text" />
					</div>
					<div class="tips" v-if="false">1{{ $t(to) }} = {{ hl }} {{ $t(from) }}</div>
				</div>

				<!-- 切换input显示顺序 -->
				<div class="changeIco text-center" @click="exchangeInput">
					<img src="../../assets/skin/mine/exchange.png" />
				</div>

				<div class="input-box">
					<div class="mb-20">
						<div class="title mb-20 flex">
							<img src="../../assets/skin/mine/riyuan.png" />
							{{ $t(from) }}
						</div>
						<input v-model="jpMoney" @input="jpInput" class="ipt flex-1" type="text" disabled />
					</div>
					<div class="tips">1{{ $t(to) }} = {{ hl }} {{ $t(from) }}</div>
				</div>
			</template>

			<template v-if="!changeIndex">
				<div class="input-box">
					<div class="mb-20">
						<div class="flex flex-b mb-20">
							<div class="title flex">
								<img src="../../assets/skin/mine/riyuan.png" />
								{{ $t(from) }}
							</div>
							<div class="user">
								<span>{{ $t("mine").money.txt3 }}:</span>
								<span class="txt">{{ money }}{{ $t(from) }}</span>
							</div>
						</div>

						<input v-model="jpMoney" @input="jpInput" class="ipt flex-1" :placeholder="$t('exchange').txt1"
							type="text" />
					</div>
					<div class="tips" v-if="false">1{{ $t(from) }} = {{ hl }} {{ $t(to) }}</div>
				</div>

				<!-- 切换input显示顺序 -->
				<div class="changeIco text-center" @click="exchangeInput">
					<img src="../../assets/skin/mine/exchange.png" />
				</div>

				<div class="input-box">
					<div class="mb-20">
						<div class="title mb-20 flex">
							<img src="../../assets/skin/mine/usd.png" />
							{{ $t(to) }}
						</div>
						<input v-model="usMoney" @input="usInput" class="ipt flex-1" type="text" disabled />
					</div>
					<div class="tips">1{{ $t(from) }} = {{ hl }} {{ $t(to) }}</div>
				</div>
			</template>
		</div>
		<div class="btn-big" @click="submit">{{ $t('mine').money.btn3 }}</div>
	</div>
</template>

<script>
	import Vue from "vue";
	import qs from "qs";
	import axios from "axios";
	import top from "../bar/toper.vue";
	import {
		Toast
	} from "vant";
	Vue.use(Toast);
	import {
		Skeleton
	} from "vant";

	export default {
		name: "exchange",
		components: {
			top
		},
		data() {
			return {
				text: this.$t('mine').money.btn3,
				jpMoney: "",
				usMoney: "",
				inputStatus: 0,
				abalance: "",
				changeIndex: false,
				from: "JPY", //开头
				to: "USD", //结尾
				fromName: "jpy", //开头
				toName: "usd", //结尾
				biList: [
					{
						value: "usd",
						name: "USD",
						title: "USD",
					},
					{
						value: "jpy",
						name: "JPY",
						title: "JPY",
					},
				],
				showPopup: false,
				change: 0,
				hl: "",
				userInfo: {},
				money: "",
				moneyType: {
					USD: "usd",
          JPY: "jpy",
				},
				password: ''
			};
		},
		computed: {
			formDataNum() {
				return (num) => {
					// 如果小于1，就不做处理
					if (num < 1000) {
						return num;
					}
					let nums = String(parseFloat(num).toFixed(2));
					let ms = nums
						.replace(/[^\d\.]/g, "")
						.replace(/(\.\d{2}).+$/, "$1")
						.replace(/^0+([1-9])/, "$1")
						.replace(/^0+$/, "0");
					let txt = ms.split(".");
					// console.log('txt', txt);
					while (/\d{4}(,|$)/.test(txt[0])) {
						txt[0] = txt[0].replace(/(\d)(\d{3}(,|$))/, "$1,$2");
					}
					nums = txt[0] + (txt.length > 1 ? "." + txt[1] : "");
					return nums;
				};
			},
		},
		created() {
			this.from = this.biList[1].name; //开头
			this.to = this.biList[0].name; //结尾
			this.fromName = this.biList[1].value; //开头
			this.toName = this.biList[0].value; //结尾
			this.getUserInfo();
			//根据币种判断汇率
			this.$server.post("/common/config", {type: 'all'}).then((res) => {
				for (let i = 0; i < res.data.data.length; i++) {
					if (res.data.data[i].name == this.fromName + this.toName) {
						this.hl = res.data.data[i].value;
					}
				}
			});
		},
		mounted() {},
		onLoad(value) {
			// console.log(value);
			this.abalance = value.abalance;
		},
		methods: {
			getUserInfo() {
				this.$server.post("/user/getUserinfo").then((res) => {
					if (res.data.status == 1) {
						this.userInfo = res.data.data;
						this.money = this.userInfo[this.fromName];
					}
				});
			},
			changeBtn(index) {
				this.showPopup = true;
				this.change = index;
				this.getUserInfo();
			},
			selectPopup(index) {
				this.showPopup = false;
				if (this.change == 0) {
					if (this.to == this.biList[index].name) {
						Toast({
							message: this.$t("同样的货币不能转"),
							duration: 2000,
						});
						return;
					}
					this.from = this.biList[index].name; //开头

					let type = this.biList[index].title; //开头

					console.log("this.from", this.from, type);
					// 每次选中币种，更新一下可用余额
					this.money = this.userInfo[this.moneyType[type]];
				} else {
					if (this.from == this.biList[index].name) {
						Toast({
							message: this.$t("同样的货币不能转"),
							duration: 2000,
						});
						return;
					}
					this.to = this.biList[index].name; //结尾
				}
				//判断汇率
				if (this.changeIndex) {
					for (let i = 0; i < this.biList.length; i++) {
						if (this.from.indexOf(this.biList[i].title) != -1) {
							this.toName = this.biList[i].value; //开头
						}
						if (this.to.indexOf(this.biList[i].title) != -1) {
							this.fromName = this.biList[i].value; //开头
						}
					}
				} else {
					for (let i = 0; i < this.biList.length; i++) {
						if (this.from.indexOf(this.biList[i].title) != -1) {
							this.fromName = this.biList[i].value; //开头
						}
						if (this.to.indexOf(this.biList[i].title) != -1) {
							this.toName = this.biList[i].value; //开头
						}
					}
				}
				//根据币种判断汇率
				this.$server.post("/common/config").then((res) => {
					for (let i = 0; i < res.data.data.length; i++) {
						if (res.data.data[i].name == this.fromName + "to" + this.toName) {
							this.hl = res.data.data[i].value;
						}
					}
				});
			},
			exchangeInput() {
				this.changeIndex = !this.changeIndex;

				this.jpMoney = "";
				this.usMoney = "";

				//判断汇率
				if (this.changeIndex) {
					for (let i = 0; i < this.biList.length; i++) {
						if (this.from.indexOf(this.biList[i].title) != -1) {
							this.toName = this.biList[i].value; //开头
						}
						if (this.to.indexOf(this.biList[i].title) != -1) {
							this.fromName = this.biList[i].value; //开头
						}
					}
				} else {
					for (let i = 0; i < this.biList.length; i++) {
						if (this.from.indexOf(this.biList[i].title) != -1) {
							this.fromName = this.biList[i].value; //开头
						}
						if (this.to.indexOf(this.biList[i].title) != -1) {
							this.toName = this.biList[i].value; //开头
						}
					}
				}
				//根据币种判断汇率
				this.$server.post("/common/config", {type: 'all'}).then((res) => {
					for (let i = 0; i < res.data.data.length; i++) {
						if (res.data.data[i].name == this.fromName + this.toName) {
							this.hl = res.data.data[i].value;
						}
					}
				});

				this.getUserInfo();
			},
			submit() {
				if (!this.jpMoney && !this.usMoney) {
					Toast({
						message: this.$t("exchange").txt2,
						duration: 2000,
					});
					return;
				}

				if (this.to == this.from) {
					Toast({
						message: this.$t("exchange").txt3,
						duration: 2000,
					});
					return;
				}

				let params = {};
				let val;
				if (!this.inputStatus) {
					// 日元转换成美元
					val = this.jpMoney.replace(/\,/g, "");

				} else {
					// 美元转换成日元
					val = this.usMoney.replace(/\,/g, "");
				}
				params = {
					money: val,
					from: this.fromName,
					to: this.toName
				};

				this.$server.post("/user/rotation", params).then((res) => {
					if (res.data.status == 1) {
						// console.log("submit",res)
						Toast({
							message: this.$t(res.data.msg),
							duration: 2000,
						});
						this.jpMoney = "";
						this.usMoney = "";
					} else {
						Toast({
							message: this.$t(res.data.msg),
							duration: 2000,
						});
					}
				});
			},
			jpInput() {
				this.inputStatus = 0;
				if (!this.jpMoney) {
					this.usMoney = "";
					return;
				}

				this.changeNum(this.jpMoney);
			},
			usInput() {
				this.inputStatus = 1;
				if (!this.usMoney) {
					this.jpMoney = "";
					return;
				}
				this.changeNum(this.usMoney);
			},
			changeNum(value) {
				if (!this.inputStatus) {
					// 日元转换成美元
					let val = this.jpMoney.replace(/\,/g, "");
					this.usMoney = (val * this.hl).toFixed(6);

					// 这里用作输入显示千分，
					this.usMoney = this.moneyInput(this.usMoney);
					this.jpMoney = this.moneyInput(val);
				} else {
					// 美元转换成日元
					let val = this.usMoney.replace(/\,/g, "");
					this.jpMoney = (val * this.hl).toFixed(6);
					// 这里用作输入显示千分，
					this.jpMoney = this.moneyInput(this.jpMoney);
					this.usMoney = this.moneyInput(val);
				}
			},
			moneyInput(num) {
				// 如果小于1，就不做处理
				if (num < 1000) {
					return num;
				}
				// let nums = String(parseFloat(num).toFixed(2));
				let nums = num;
				let ms = nums
					.replace(/[^\d\.]/g, "")
					.replace(/(\.\d{2}).+$/, "$1")
					.replace(/^0+([1-9])/, "$1")
					.replace(/^0+$/, "0");
				let txt = ms.split(".");
				// console.log('txt', txt);
				while (/\d{4}(,|$)/.test(txt[0])) {
					txt[0] = txt[0].replace(/(\d)(\d{3}(,|$))/, "$1,$2");
				}
				nums = txt[0] + (txt.length > 1 ? "." + txt[1] : "");
				return nums;
			},
		},
	};
</script>

<style scoped lang="less">
	.exchange {
		background: #0F161C;
		min-height: 100vh;
		width: 100%;
		overflow: hidden;
		position: relative;

		.headf {
			width: 100%;
			height: 0.47rem;
			background: #fff;
			font-weight: 500;
			font-size: .16rem;
			color: #333;
		}

		.pd15 {
			padding: 0.15rem;
		}

		.popup-box {
			width: 100%;
			height: 100%;
			position: fixed;
			top: 0;
			bottom: 0;
			background: rgba(1, 1, 1, 0.3);
			z-index: 999;

			.popup-body {
				width: 95%;
				padding: 0.15rem 0;
				position: absolute;
				bottom: 0;
				left: 50%;
				transform: translate(-50%, 0);

				.popup-list {
					width: 100%;
					background: #ffffff;

					.popup-item {
						width: 100%;
						line-height: 0.5rem;
						font-size: 0.14rem;
						color: #000000;
						text-align: center;
						border-bottom: 1px solid #ededed;
					}
				}

				.popup-cancel {
					width: 100%;
					line-height: 0.5rem;
					font-size: 0.14rem;
					color: #000000;
					text-align: center;
					background: #ffffff;
				}
			}
		}

		.mb20 {
			margin-bottom: 0.2rem;
		}

		.cot {
			margin: 0 .12rem;
			align-items: center;
			position: relative;
			z-index: 300;

			.input-box {
				padding: .2rem 0;

				.title {
					font-weight: 600;
					font-size: .18rem;
					color: #fff;

					img {
						width: .33rem;
						height: .33rem;
						margin-right: .05rem;
					}
				}

				.ipt {
					background: #000000;
					border-radius: 0.1rem;
					padding: 0.1rem;
					width: 100%;
					height: .52rem;
					color: #fff;

				}

				.user {
					font-size: .14rem;
					color: #fff;

					.txt {
						color: #fff;
					}
				}

				.tips {
					font-weight: 400;
					font-size: .14rem;
					color: #F53333;
				}
			}

			.changeIco {
				img {
					width: .38rem;
					height: .38rem;
				}
			}
		}

		.btn-big {
			margin: .3rem .12rem;
		}


		input::-webkit-input-placeholder,
		textarea::-webkit-input-placeholder {
			color: #999;
			font-size: 0.15rem;
		}


		.dhbtn {
			width: 100%;
			height: 0.4rem;
			//background: linear-gradient(90deg, #fce051, #ff8335);
			background: linear-gradient(90deg, #77e1e3, #224aae);
			border-radius: 0.05rem;
			color: #fff;
			font-size: 0.14rem;
			text-align: center;
			line-height: 0.4rem;
			// margin-top: 0.3rem;
		}
	}
</style>