 <template>
 	<div class="news">
		<div class="header">
			<topCom></topCom>
		</div>
		<div class="img" @click="showG=false" v-if="showG">
			<div class="icon close02"></div>
		</div>
		<div class="list">
			<div class="item" v-for="(item, index) in newsList" :key="index" @click="goDetail(item)">
				<div class="item-box">
					<div class="col">
						<div class="item-date">{{$formatDate('YYYY/MM/DD',item.created*1000)}}</div>
						<div class="item-title">{{ item.title.split('u3000').join('') }}</div>
					</div>
					<img :src="item.img" v-if="item.img" />
				</div>
			</div>
		</div>
 		<bottomnav :on='4'></bottomnav>
 	</div>
 </template>
 <script type="text/javascript">
 	import Vue from 'vue';
 	import qs from 'qs';
 	import axios from 'axios';
 	import {
 		Toast
 	} from 'vant';
 	Vue.use(Toast);
 	import top from '../bar/toper.vue'
	import bottomnav from "../bar/bottomnav.vue";
	import topCom from '../bar/topCom.vue'
 	export default {
 		name: "news",
 		data() {
 			return {
 				text: this.$t('home').txt6,
 				newsList: [],
				showG:true
 			}
 		},
 		components: {
 			top,bottomnav,topCom
 		},
 		methods: {
 			goDetail(e) {
				window.localStorage.setItem("newsDetail",JSON.stringify(e))
 				this.$router.push({
 					path: '/home/<USER>',
 				})
 			},
 			getNews() {
 				this.$server.post('/common/newss', {
          exchange:'jp',
          lang:'jp'
        }).then(str => {
					if (str.data.status == 1) {
						this.newsList = str.data.data.result
					}
				})
 			},
 		},
 		destroyed() {

 		},
 		mounted() {
 			this.getNews();
 		},
 	}
 </script>
 <style type="text/css" lang="less" scoped="scoped">
 	input::-webkit-input-placeholder,
 	textarea::-webkit-input-placeholder {
 		color: #777;
 		font-size: 0.14rem;
 	}

 	.news {
		max-width: 100%;
		overflow:hidden;
		padding-top:.44rem;
		background-color: #0f161c;
		min-height: 100vh;
		.header{
			height:.44rem;
			width:100%;
			font-weight: 500;
			font-size: .16rem;
			color: #24272C;
			position: fixed;top:0;left:0;z-index: 888;
		}
		.img{
			position: fixed;
			bottom: 0.6rem;
			width: 100%;height: 0.7rem;
			background: url('../../assets/v2/xwbg.png') no-repeat center/100%;
			.close02{
				margin-left: 3.5rem;
				margin-top: 0.1rem;
			}
		}
		.topThree{
			//padding:30rpx;
			position: relative;
			width:100%;
			
			.itemBox{
				border: 1px solid #28B2AD;
				border-radius: .1rem;
				position: relative;z-index: 200;
				margin:.11rem;padding:.2rem .15rem .05rem .15rem;
			}
			.item{
				margin-bottom: .25rem;
			}
			.order{
				width:.17rem;height:.19rem;
				flex:none;
				font-weight: 500;
				color: #FFFFFF;margin-right:.1rem;
				position:relative;z-index: 1;
				img{
					width:100%;height:100%;
					position: absolute;top:0;left:0;
					z-index:1;margin:0;
				}
				span{
					position: relative;z-index: 2;
					margin-bottom: .02rem;
				}
			}
			.txt{
				white-space: nowrap;overflow: hidden;
				text-overflow: ellipsis;
				font-size: .13rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				color: #231E17;
			}
		}
		
		.list{
			margin-top:.1rem;
			background-color: #1b232a;
			padding-bottom: 1.5rem;
			.item{
				padding: 0.1rem;
				display: flex;
				align-items: center;
				border-bottom:.01rem solid rgba(255, 255, 255, 0.16);
				img{
					width: .8rem;
					height: .68rem;
					border-radius: .12rem;
					flex:none;
				}
				.item-box{
					width: 100%;
					display: flex;
					justify-content: space-between;
					align-items: center;
					
					.col{
						height: .68rem;
						display: flex;flex-direction: column;
						justify-content: inherit;
					}
				}
				.item-title{
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 0.16rem;
					color: #FFFFFF;
					overflow: hidden;
					text-overflow:ellipsis;
					display:-webkit-box;
					-webkit-line-clamp:2;
					-webkit-box-orient:vertical;
					margin-right:.1rem;
				}
				.item-date{
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 0.14rem;
					color: #718A94;
				}
			}
		}
 	}
 	
 </style>