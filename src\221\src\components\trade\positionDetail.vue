<template>
	<div class="page ">
		<top-back title="持倉詳情"></top-back>
		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<div class="wbg">
				<div class="toph flex">
					<div class="name">{{ item.stock_name || "-" }}</div>
					<div class="code">{{ item.stock_code || "-" }}</div>
				</div>
				<div class="inner flex flex-b flex-wrap">
					<div class="inner-item">
						<div class="t1">損益</div>
						<div class="t2" :class="currentItem.gain > 0 ? 'red' : 'green'">{{ currentItem.gain > 0 ? "+" : ""}}{{ $formatMoney(currentItem.yingkui)||0 }}</div>
					</div>
					<div class="inner-item">
						<div class="t1">報酬率</div>
						<div class="t2" :class="currentItem.gain > 0 ? 'red' : 'green'">{{ currentItem.gain > 0 ? "+" : ""}}{{ $formatMoney(currentItem.gain,2)||0 }}%</div>
					</div>
					<div class="inner-item">
						<div class="t1">總股數</div>
						<div class="t2">{{ $formatMoney(currentItem.stock_num) }}</div>
					</div>
					<div class=" inner-item">
						<div class="t1">買入價</div>
						<div class="t2">
							{{ $formatMoney1(currentItem.buy_price,2) }}
						</div>
					</div>
					<div class="inner-item">
						<div class="t1">持倉市值</div>
						<div class="t2">
							{{ $formatMoney(parseFloat(currentItem.market_value) + parseFloat(currentItem.yingkui)) }}
						</div>
					</div>
					<div class="inner-item">
						<div class="t1">成本</div>
						<div class="t2">
							{{ $formatMoney(currentItem.buy_price * currentItem.stock_num) }}
						</div>
					</div>
				</div>
				<!-- <div class="st" :class="{ pc: item.sell_time }">{{ statusStr[item.status] }}</div> -->
				<!-- <div>
					<div class="yk mt5" :class="currentItem.gain > 0 ? 'green' : 'red'">
						{{isNaN(Number(currentItem.yingkui).toFixed(2))? "0": Number(currentItem.yingkui).toFixed(2)}}
					</div>
				</div> -->
			</div>

			<div class="list">
				<div class="cot">
					<div class="list-item">
						<div class="item-left">買賣類型</div>
						<div class="item-right" v-if="parseInt(item.buyzd) === 1">買漲</div>
						<div class="item-right" v-if="parseInt(item.buyzd) === 2">買跌</div>
					</div>
					<div class="list-item">
						<div class="item-left">訂單號</div>
						<div class="item-right">{{ item.strategy_num }}</div>
					</div>
					<div class="list-item">
						<div class="item-left">買入價格</div>
						<div class="item-right">{{ $formatMoney1(item.buy_price,2) }}</div>
					</div>
					<div class="list-item">
						<div class="item-left">買入股數</div>
						<div class="item-right">{{ $formatMoney(item.stock_num) }}</div>
					</div>
					<div class="list-item">
						<div class="item-left">買入時間</div>
						<div class="item-right">
							{{ $formatDate("YYYY/MM/DD", item.buy_time * 1000) }}
						</div>
					</div>
					<div class="list-item" v-if="item.sell_time">
						<div class="item-left">賣出時間</div>
						<div class="item-right">
							{{ $formatDate("YYYY/MM/DD", item.sell_time * 1000) }}
						</div>
					</div>
					<div class="list-item" v-if="item.sell_time">
						<div class="item-left">賣出價格</div>
						<div class="item-right">{{ $formatMoney(item.sell_price) }}</div>
					</div>
					<!-- <div class="list-item">
						<div class="item-left">{{ $t("positionDetail").txt7 }}</div>
						<div class="item-right">{{ $formatMoney(item.credit) }}</div>
					</div> -->
					<!-- <div class="list-item">
						<div class="item-left">{{ $t("positionDetail").txt8 }}</div>
						<div class="item-right">{{ $formatMoney(item.ganggang) }}</div>
					</div> -->
					<div class="list-item" v-if="item.sell_time">
						<div class="item-left">市值</div>
						<!-- <div class="item-right">{{ $formatMoney(item.market_value) }}</div> -->
						<div class="item-right">{{ $formatMoney(parseFloat(item.sell_price * item.stock_num))}}
						</div>
					</div>
					<div class="list-item">
						<div class="item-left">類型</div>
						<div class="item-right">
							{{ item.type == 1 ? "市價" : "配對交易" }}
						</div>
					</div>
					<div class="list-item">
						<div class="item-left">買進手續費</div>
						<div class="item-right">
							{{ $formatMoney(item.buy_poundage) || 0 }}
						</div>
					</div>
					<div class="list-item" v-if="item.sell_time">
						<div class="item-left">賣出手續費</div>
						<div class="item-right">
							{{ $formatMoney(item.sell_poundage) || 0 }}
						</div>
					</div>
				</div>
			</div>
		</van-pull-refresh>
	</div>
</template>
<script>
	export default {
		name: "positionDetail",
		data() {
			return {
				isLoading: false,
				currentItem: {
					// gain:10.,
					// yingkui:1,
				},
				item: {
					
				},
				statusStr: ["持倉中", "已平倉", "準備平倉", "掛單中"],
			};
		},
		computed: {},
		created() {
			this.currentItem = this.$storage.get("currentItem");
			this.getDetail();
		},
		methods: {
			// 下拉刷新
			onRefresh() {
				this.getDetail();
			},
			// 获取详情
			getDetail() {
				this.$server
					.post("/trade/stockdetail", {
						id: this.currentItem.id,
						type: "twd",
					})
					.then((res) => {
						this.isLoading = false;
						if (res.status == 1) {
							this.item = res.data;
						}
					});
			},
		},
	};
</script>
<style scoped lang="less">
	.page {
		padding: 0.5rem 0 0;
		min-height: 100vh;
	}
	.wbg {
		margin: 0.12rem;
		background: #232429;
		border-radius: 0.13rem;
		overflow: hidden;
		.toph{
			padding: 0.12rem;
		}
		.name {
			font-family: PingFang SC;
			font-weight: 500;
			font-size: 0.15rem;
			color: #FFFFFF;
		}
		
		.code {
			font-family: PingFang SC;
			font-weight: 400;
			font-size: 0.11rem;
			color: #999999;
			margin-left: 0.05rem;
		}
		.inner{
			padding: 0.12rem;
			.inner-item{
				width: 46%;
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 0.06rem 0;
				.t1{
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 0.13rem;
					color: #999999;
				}
				.t2{
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 0.13rem;
					color: #fff;
				}
			}
		}
		.st {
			background: #CC3333;
			border-radius: 0.02rem 0.02rem 0.02rem 0.02rem;
			padding: 0.05rem 0.1rem;
			font-size: 0.12rem;
			color: #ffffff;
			&.pc {
				background: #4A4A4A;
				color: #FFFFFF;
			}
		}
		.per {
			font-weight: 500;
			font-size: 0.12rem;
			margin-top: 0.05rem;
		}
		
		.yk {
			font-weight: 500;
			margin-top: 0.1rem;

			.c {
				width: 0.12rem;
				height: 0.12rem;
				background: #6970af;
				border-radius: 50%;
				margin-right: 0.05rem;
			}

			&.mt5 {
				margin-top: 0.05rem;
			}
		}
	}

	.list {
		.cot {
			margin: 0 0.12rem;
			background: #232429;
			border-radius: 0.13rem;
			padding: 0.12rem;
			.list-item {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 0.06rem 0;
				.item-left{
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 0.13rem;
					color: #999999;
				}
				.item-right{
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 0.13rem;
					color: #fff;
				}
			}
		}
	}
</style>