<template>
  <!-- 编辑自选 -->
  <div class="page">
    <top-back :title="$t('编辑自选')"></top-back>

    <van-pull-refresh
      v-model="isLoading"
      @refresh="onRefresh"
      :loosing-text="$t('new').t3"
      :loading-text="$t('new').t4"
      :pulling-text="$t('new').t5"
    >
      <van-skeleton title :row="26" :loading="loading">
        <div class="cot">
          <no-data v-if="isShow"></no-data>

          <div class="list" v-if="chooseList.length">
            <div class="flex flex-b titles">
              <div class="flex-1 flex">
                <div
                  class="icon"
                  :class="show ? 'yxz' : 'wxz'"
                  @click="chooseAll"
                ></div>
                {{ $t("new").a3 }}
              </div>
            </div>

            <div
              class="list-item flex flex-b"
              v-for="(item, index) in chooseList"
              :key="index"
              @click="
                $toDetail(`/market/stockDetail?symbol=${item.symbol}`, item)
              "
            >
              <div class="flex-2 flex ">
                <div
                  v-if="item.showIcon"
                  class="icon wxz animate__animated animate__fadeIn"
                  :class="{ yxz: item.choose }"
                  @click.stop="changeItem(index)"
                ></div>

                <div>
                  <div class="name">{{ item.name || "-" }}</div>
                  <div class="code">{{ item.symbol || "-" }}</div>
                </div>
              </div>

              <div class=" price flex-1">
                {{ $formatMoney(item.price) || 0 }}
              </div>

              <div
                class="flex-1 per t-r flex flex-e"
                :class="{
                  green: Number(item.gain) < 0,
                }"
              >
                <div class="t" :class="item.gainValue > 0 ? 'red' : 'green'">
                  {{ item.gainValue > 0 ? "+" : ""
                  }}{{ $formatMoney(item.gainValue) || 0 }}
                </div>
                <div class="t t1" :class="item.gainValue > 0 ? 'red' : 'green'">
                  {{ item.gain > 0 ? "+" : "" }}{{ item.gain || 0 }}%
                </div>
              </div>
            </div>
          </div>

          <div class="btn flex flex-c" @click="delItem">
            <div class="icon sczx"></div>
            {{ $t("new").a4 }}
          </div>
        </div>
      </van-skeleton>
    </van-pull-refresh>

    <loading ref="loading" />
  </div>
</template>

<script>
export default {
  name: "editSelf",
  props: {},
  data() {
    return {
      isUp: false, //默认涨幅在前
      list: [],
      loading: true,
      loading1: true,
      isShow: false,
      isLoading: false,
      show: false,
      chooseList: [],
      lastSymbol: "",
    };
  },
  computed: {},
  created() {
    this.getMine();
    this.getIndexList();
  },
  mounted() {
    // this.$refs.firstLoading.open();
  },
  methods: {
    onRefresh() {
      this.isShow = false;
      this.getMine();
    },
    changeList() {
      if (!this.chooseList.length) return;
      this.chooseList.forEach((item) => {
        item.showIcon = true;
      });
      this.show = true;
    },
    cancle() {
      this.chooseList.forEach((item) => {
        item.showIcon = false;
      });
      this.show = false;
    },
    changeItem(index) {
      this.chooseList.forEach((item, i) => {
        if (index == i) {
          item.choose = !item.choose;
        }
      });

      this.show = this.chooseList.every((item) => item.choose); //列表里面切换，改变全选按钮的状态
    },
    chooseAll() {
      this.show = !this.show;
      this.chooseList.forEach((item) => {
        item.choose = !item.choose;
      });
    },
    delItem() {
      let arr = this.chooseList.filter((item) => item.choose);
      if (arr.length) {
        this.lastSymbol = arr[arr.length - 1].symbol; //记录已选中最后一项的值
        // console.log("lastSymbol", this.lastSymbol);
        this.$refs.loading.open(); //开启加载
        arr.forEach((item) => {
          this.removeOptional(item);
        });
      }
    },
    changeListup() {
      this.isUp = !this.isUp;
      let arr = [];
      let arr1 = [];
      this.chooseList.forEach((item) => {
        if (item.gain > 0) {
          arr.push(item);
        } else {
          arr1.push(item);
        }
      });

      if (this.isUp) {
        arr.sort((a, b) => b.gain - a.gain);
        arr1.sort((a, b) => b.gain - a.gain);
        this.chooseList = [...arr, ...arr1]; //涨在前、高在前
      } else {
        arr.sort((a, b) => a.gain - b.gain);
        arr1.sort((a, b) => a.gain - b.gain);
        this.chooseList = [...arr1, ...arr]; //跌在前、低在前
      }
    },
    getMine() {
      this.$server.post("/transaction/Optionallist", { offset: 0 }).then((res) => {
        this.isLoading = false; //下拉刷新状态
        // this.$refs.firstLoading.close(); //关闭初始加载的效果
        this.loading = false;
        if (res.status == 1) {
          if (this.show) {
            // 如果是已经选择删除，但是列表还有数据
            res.data.forEach((item) => {
              item.showIcon = true;
              item.choose = false;
            });
          } else {
            res.data.forEach((item) => {
              item.showIcon = true;
              item.choose = false;
            });
          }

          this.chooseList = res.data;
          this.changeListup(); //重新加载排序，涨在前

          if (!res.data.length) {
            this.show = false;
            this.isShow = true;
          }
          // console.log("this.chooseList ", this.chooseList);
        }
      });
    },
    removeOptional(item) {
      this.$server
        .post("/transaction/removeOptional", { symbol: item.symbol })
        .then((res) => {
          if (res.status == 1) {
            if (this.lastSymbol == item.symbol) {
              this.$refs.loading.close(); //删除最后一项成功，结束加载中
              this.getMine();
            }
          }
        });
    },
  },
};
</script>

<style scoped lang="less">
.page {
  padding: 0.5rem 0 0.6rem;
  min-height: 100vh;
}

.red {
  color: #c04649;
}
.green {
  color: #4f8672;
}
::v-deep .van-skeleton__row{
  background-color:transparent !important;
}
::v-deep .van-skeleton__title{
  background-color:transparent !important;
}
.cot {
  .list {
    .titles {
      padding: 0.1rem;
      div {
        font-size: 0.12rem;
        color: #757575;
      }
      .icon {
        margin-right: 0.05rem;
      }
    }
    .list-item {
      padding: 0.1rem;
      border-bottom: 0.01rem solid #f4f4f4;
      .icon {
        margin-right: 0.1rem;
      }
      .name {
        font-size: 0.12rem;
        color: #000000;
      }
      .code {
        font-size: 0.1rem;
        color: #c4c4c4;
      }
      .price {
        font-size: 0.12rem;
        color: #0c0c0c;
        text-align: center;
      }
      .per {
        .t {
          font-size: 0.12rem;
          color: #0c0c0c;
          &.t1 {
            margin-left: 0.1rem;
          }
        }
      }
    }
  }
}

.red {
  color: #ba3b3a !important;
}
.green {
  color: #39B44C !important;
}

.btn {
  font-weight: 500;
  text-align: center;
  padding: 0.2rem 0;
  border-top: 0.01rem solid #e4e4e4;
  font-size: 0.14rem;
  color: #c62827;
  width: 100%;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 999;
  .icon {
    margin-right: 0.05rem;
  }
}
</style>
