<template>
	<div class="page ">
		<!-- <top-menu :home="true"></top-menu> -->
		<div class="top-fixed">
			<div class="header flex flex-b">
				<div class="flex-1"></div>
				<div class="tit flex-1 t-c">{{ $t('trade.title') }}</div>
				<div class="flex flex-1 flex-e" style="margin-left: 0.1rem;">
					<div class="icon ring animate__animated animate__fadeIn" @click="$toPage('/information/userInfo')">
					</div>
				</div>
			</div>
		</div>
		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('refresh.releaseToRefresh')"
			:loading-text="$t('refresh.loading')" :pulling-text="$t('refresh.pullToRefresh')">
			<!-- 切换列表内容显示 -->
			<div class="money">
				<div class="flex-column-item tops">
					<div class="t">{{ $t('trade.profitFunds') }}</div>
					<div class="num" v-if="show">{{ $formatMoney(ykAssets) || 0}}</div>
					<div class="num" v-else>****</div>
				</div>
				<!-- <div class="flex-column-item tops">
					<div class="t flex">總資產</div>
					<div class="num flex" @click="show = !show">
						{{ show ? $formatMoney(totalAssets) || 0 : "****" }}
						<div class="icon animate__animated animate__fadeIn" :class="show ? 'bageye' : 'bagby'">
						</div>
					</div>
				</div> -->
				<div class="sz-num flex flex-b">
					<div class="szItem flex">
						<div class="t">{{ $t('trade.availableFunds') }}</div>
						<div class="t1">{{ show?($formatMoney(userInfo.twd) || 0):'*****' }}
						</div>
					</div>
					<div class="szItem flex">
						<div class="t">{{ $t('trade.positionValue') }}</div>
						<div class="t1">{{ show?($formatMoney(szAssets) || 0):'*****' }}
						</div>
					</div>
					<!-- <div class="szItem">
						<div class="flex flex-c" style="position: relative;">
							<div class="t">{{currmentIndex==2?'總盈虧':'今日盈虧'}} </div>
							<div style="margin-left: .1rem;position: absolute;right: 0;" @click="show=!show" class="icon animate__animated animate__fadeIn" :class="show ? 'bageye' : 'bagby'">
							</div>
						</div>
						<div class="t1 red" v-if="show">{{ percent > 0 ? "+" : "" }}{{ percent||0 }}%</div>
						<div class="t1 red" v-else>****</div>
					</div> -->
					<!-- <div class="szItem flex flex-b">
						<div class="t">使用中資金</div>
						<div class="t1">
							{{ show?($formatMoney(freezeAssets) || 0):'*****' }}
						</div>
					</div> -->
				</div>
				<!-- <div class="btnss flex flex-b">
					<div class="flex-column-item" @click="$toPage('/information/recharge')">
						<div class="icon h14"></div>
						<div class="t">儲值</div>
					</div>
					<div class="flex-column-item" @click="$toPage('/information/cashOut')">
						<div class="icon h15"></div>
						<div class="t">提領</div>
					</div>
					<div class="flex-column-item" @click="$toPage('/home/<USER>')">
						<div class="icon h16"></div>
						<div class="t">借貸</div>
					</div>
					<div class="flex-column-item" @click="$toPage('/information/fundRecord')">
						<div class="icon h17"></div>
						<div class="t">資金明細</div>
					</div>
				</div> -->
			</div>
			<div class="nav-box flex">
				<div class="nav-item flex flex-c" v-for="(item, i) in navList" :key="i" @click="changeNav(item.type)"
					:class="{ active: currmentIndex == item.type }">
					{{ item.name }}
				</div>
			</div>
			<!-- 申购 -->
			<div class="sg-list" v-if="currmentIndex === 0">
				<van-skeleton title :row="26" :loading="loading1">
					<no-data v-if="!xinguList.length"></no-data>
					<div class="sg-item" v-for="(item, index) in xinguList" :key="index">
						<div class="top flex flex-b">
							<div class="flex">
								<div class="name">{{ item.stock_name || "-" }}</div>
								<div class="code">{{ item.stock_code || "-" }}</div>
							</div>
							<div v-if="Number(item.lucky_total)&&Number(item.subs_value)&&Number(item.subs_value)!=Number(item.rjmoney)" class="status animate__animated animate__fadeIn" @click="clickRenJiao(item)">{{ $t('認繳') }}
							</div>
							<div class="status animate__animated animate__fadeIn">{{ $t(item.xgstate) }}</div>
						</div>
						<div class="data flex flex-b">
							<div class="data-item flex flex-b">
								<div class="t">{{ $t('承銷價') }}</div>
								<div class="t1 ">{{ $formatMoney(item.apply_price) || "-" }}</div>
							</div>
							<div class="data-item flex flex-b" v-if="item.status == 1 || item.status == 2 || item.status == 3">
								<div class="t">{{ $t('中籤張數') }}</div>
								<div class="t1">{{ $formatMoney(Number(item.lucky_total) / 1000, 0) || "-" }}<span>{{ $t('張') }}</span>
								</div>
							</div>
							<div class="data-item flex flex-b">
								<div class="t">{{ $t('申購張數') }}</div>
								<div class="t1">{{ $formatMoney(Number(item.apply_total) / 1000, 0) || "-" }}<span>{{ $t('張') }}</span>
								</div>
							</div>
							<div class="data-item flex flex-b" v-if="item.status == 1 || item.status == 2 || item.status == 3">
								<div class="t">{{ $t('應認繳金額') }}</div>
								<div class="t1">{{ $formatMoney(item.subs_value) || "-" }}</div>
							</div>
							<!-- <div class="data-item flex flex-b ">
								<div class="t">報酬率</div>
								<div class="t1 price">{{ item.newstock.rate }}%</div>
							</div> -->
							<div class="data-item flex flex-b ">
								<div class="t">{{ $t('市值') }}</div>
								<div class="t1 price">{{ item.xgstate == "待中籤" ? "-" : $formatMoney(item.market_value) }}
								</div>
							</div>
							<div class="data-item flex flex-b" v-if="item.status == 1 || item.status == 2 || item.status == 3">
								<div class="t">{{ $t('已認繳金額') }}</div>
								<div class="t1">
									{{ $formatMoney(item.rjmoney) || "-" }}
								</div>
							</div>
						</div>
					</div>
				</van-skeleton>
			</div>
			<!-- 競拍 -->
			<div class="sg-list" v-if="currmentIndex === 4">
				<van-skeleton title :row="26" :loading="loading5">
					<no-data v-if="!xinguListJp.length"></no-data>
					<div class="sg-item" v-for="(item, index) in xinguListJp" :key="index">
						<div class="top flex flex-b">
							<div class="">
								<div class="name">{{ item.stock_name || "-" }}</div>
								<div class="code">{{ item.stock_code || "-" }}</div>
							</div>
							<div class="status animate__animated animate__fadeIn">
								{{ $t(item.xgstate) }}
							</div>
						</div>

						<div class="data flex flex-b">
							<div class="data-item flex flex-b">
								<div class="t">{{ $t('承銷價') }}</div>
								<div class="t1 ">
									{{ $formatMoney(item.apply_price) || "-" }}
								</div>
							</div>
							<div class="data-item flex flex-b">
								<div class="t">{{ $t('中籤張數') }}</div>
								<div class="t1">
									{{ $formatMoney(Number(item.lucky_total) / 1000, 0) || "-" }}<span>{{ $t('張') }}</span>
								</div>
							</div>
							<div class="data-item flex flex-b">
								<div class="t">{{ $t('申購張數') }}</div>
								<div class="t1">
									{{ $formatMoney(Number(item.apply_total) / 1000, 0) || "-" }}<span>{{ $t('張') }}</span>
								</div>
							</div>
							<div class="data-item flex flex-b">
								<div class="t">{{ $t('應認繳金額') }}</div>
								<div class="t1">
									{{ $formatMoney(item.subs_value) || "-" }}
								</div>
							</div>
							<!-- <div class="data-item flex flex-b ">
								<div class="t">報酬率</div>
								<div class="t1 price">{{ item.newstock.rate }}%</div>
							</div> -->

							<div class="data-item flex flex-b ">
								<div class="t">{{ $t('市值') }}</div>
								<div class="t1 price">
									{{ item.xgstate == "待中籤"? "-": $formatMoney(item.market_value)}}
								</div>
							</div>
							<div class="data-item flex flex-b">
								<div class="t">{{ $t('已認繳金額') }}</div>
								<div class="t1">
									{{ $formatMoney(item.rjmoney) || "-" }}
								</div>
							</div>
						</div>
					</div>
				</van-skeleton>
			</div>
			<!-- 进行中 -->
			<div class="cy-list" v-if="currmentIndex === 1">
				<van-skeleton title :row="26" :loading="loading3">
					<no-data v-if="!positionList.length"></no-data>
					<div class="cy-item" v-for="(item, index) in positionList" :key="index" @click="sellItem(item)">
						<div class="flex flex-b top">
							<div class="flex">
								<div class="name">{{ item.stock_name }}</div>
								<div class="code">{{ item.stock_code }}</div>
							</div>
							<div class="flex sy">
								<div class="t">{{ $t('總損益') }}</div>
								<div
									>
									<div :class="{red: parseFloat(item.yingkui) >= 0,green: parseFloat(item.yingkui) < 0, }">
										{{ parseFloat(item.yingkui) > 0 ? "+" : "" }}{{ $formatMoney(item.yingkui) }}
									</div>
									<!-- <div class="per">
										{{ item.yingkui > 0 ? "+" : "" }}{{ Number(item.gain).toFixed(2) }}%
									</div> -->
								</div>
							</div>
						</div>
						<div class="inner flex flex-b">
							<div class="inner-item flex flex-b">
								<div class="t1">{{ $t('報酬率') }}</div>
								<div class="t2" :class="{red: parseFloat(item.yingkui) >= 0,green: parseFloat(item.yingkui) < 0, }">{{ item.yingkui > 0 ? "+" : "" }}{{ Number(item.gain).toFixed(2) }}%
								</div>
							</div>
							<div class="inner-item flex flex-b">
								<div class="t1">{{ $t('總股數') }}</div>
								<div class="t2">{{ $formatMoney(item.stock_num) }}</div>
							</div>
							<div class=" inner-item flex flex-b">
								<div class="t1">{{ $t('買入價') }}</div>
								<div class="t2">
									{{ $formatMoney1(item.buy_price,2) }}
								</div>
							</div>
							<div class="inner-item flex flex-b">
								<div class="t1">{{ $t('持倉市值') }}</div>
								<div class="t2">{{ $formatMoney(parseFloat(item.market_value) + parseFloat(item.yingkui))||0 }}
								</div>
							</div>
							<div class="inner-item flex flex-b">
								<div class="t1">{{ $t('成本') }}</div>
								<div class="t2">{{ $formatMoney(item.buy_price * item.stock_num) }}
								</div>
							</div>
							<div class="flex flex-b inner-item">
								<div class="t1">{{ $t('總手續費') }}</div>
								<div class="t2">{{ $formatMoney(item.all_poundage) }}</div>
							</div>
							<div class="flex flex-b inner-item">
								<div class="t1">{{ $t('時間') }}</div>
								<div class="t2">{{ $formatDate("YYYY/MM/DD", item.buy_time * 1000) }}</div>
							</div>
						</div>
						<!-- <div class="btn">{{ statusStr[item.status] }}</div> -->
						<div class="flex flex-b btns">
							<div class="btn" @click.stop="goItem(item)">{{ $t('詳情') }}</div>
							<div class="btn bt" style="margin: 0 .1rem;">{{ item.status == 3 ? $t("掛單中") : $t("持倉中") }}</div>
							<div class="btn bt1" @click.stop="sellstrategy(item.id, item.status)">
								{{ $t('賣出') }}
							</div>
						</div>
					</div>
				</van-skeleton>
			</div>
			<!-- 已终止 -->
			<div class="cy-list" v-if="currmentIndex === 2">
				<van-skeleton title :row="26" :loading="loading3">
					<no-data v-if="!positionCloseList.length"></no-data>
					<div class="cy-item" v-for="(item, index) in positionCloseList" :key="index" @click="goItem(item)">
						<div class="flex flex-b top">
							<div class="flex">
								<div class="name">{{ item.stock_name }}</div>
								<div class="code">{{ item.stock_code }}</div>
							</div>
							<div class="flex sy">
								<div class="t">{{ $t('總損益') }}</div>
								<div :class="{ red: parseFloat(item.yingkui) >= 0, green: parseFloat(item.yingkui) < 0, }">{{ parseFloat(item.yingkui) > 0 ? "+" : "" }}{{ $formatMoney(item.yingkui) }}
								</div>
							</div>
						</div>
						<div class="inner flex flex-b">
							<div class="flex flex-b inner-item">
								<div class="t1">{{ $t('報酬率') }}</div>
								<div class="t2" :class="{red: parseFloat(item.yingkui) >= 0,green: parseFloat(item.yingkui) < 0, }">{{ item.yingkui > 0 ? "+" : "" }}{{ Number(item.gain).toFixed(2) }}%
								</div>
							</div>
							<div class="flex flex-b inner-item">
								<div class="t1">{{ $t('總股數') }}</div>
								<div class="t2">{{ $formatMoney(item.stock_num) }}</div>
							</div>
							<div class="flex flex-b inner-item">
								<div class="t1">{{ $t('成交價') }}</div>
								<div class="t2">{{ $formatMoney(item.sell_price,2) }}</div>
							</div>
							<div class="flex flex-b inner-item">
								<div class="t1">{{ $t('持倉市值') }}</div>
								<div class="t2">
									{{ $formatMoney(parseFloat(item.market_value) + parseFloat(item.yingkui)) }}
								</div>
							</div>
							<div class="flex flex-b inner-item">
								<div class="t1">{{ $t('成本') }}</div>
								<div class="t2">
									{{ $formatMoney(item.buy_price * item.stock_num) }}
								</div>
							</div>
							<div class="flex flex-b inner-item">
								<div class="t1">{{ $t('總手續費') }}</div>
								<div class="t2">
									{{ $formatMoney(item.all_poundage) }}
								</div>
							</div>
							<div class="flex flex-b inner-item">
								<div class="t1">{{ $t('時間') }}</div>
								<div class="t2">{{ $formatDate("YYYY/MM/DD", item.sell_time * 1000) }}
								</div>
							</div>
						</div>
						<!-- <div class="btn pc">{{ statusStr[item.status] }}</div> -->
						<!-- <div class="flex flex-b btns">
							<div class="btn" @click.stop="goItem(item)">{{ $t("详情") }}</div>
							<div class="btn bt">{{ statusStr[item.status] }}</div>
						</div> -->
					</div>
				</van-skeleton>
			</div>
			<!-- 跟单记录 -->
			<div class="" v-if="currmentIndex === 3">
				<van-skeleton title :row="26" :loading="loading2">
					<no-data v-if="!gdData.length"></no-data>
					<div class="gd-list" v-if="gdData.length">
						<div class="gd-item" v-for="(item, index) in gdData" :key="index">
							<div class="flex flex-b">
								<div class="flex">
									<div class="name">{{ item.name || "-" }}</div>
									<div class="code">{{ item.sender || "-" }}</div>
								</div>
								<!-- <div class="flex flex-e">
									<div class="status" :class="{ end: item.status }">{{ item.status ? "已終止" : "進行中" }}
									</div>
								</div> -->
							</div>
							<div class="center flex flex-b">
								<div class="flex flex-b flex-column-item">
									<div class="flex tt" :class="item.dprofit.indexOf('-') > -1 ? 'green' : 'red'">
										{{ item.aprofit || "-" }}
										<div class="icon" :class="item.dprofit.indexOf('-') > -1 ? 'down' : 'up'"></div>
									</div>
									<div class="t">{{ $t('總配息') }}</div>
								</div>
								<!-- <div class="flex flex-b flex-column-item">
									<div class="tt" :class="item.dprofit.indexOf('-') > -1 ? 'green' : 'red'">
										{{$formatMoney(item.ryl,2)||0}}%
									</div>
									<div class="t">日盈利</div>
								</div> -->
								<div class="flex flex-b flex-column-item">
									<div class="tt" :class="item.dprofit.indexOf('-') > -1 ? 'green' : 'red'">
										<!-- {{ item.aprofit || "-" }} -->
										{{$formatMoney(item.zyl,2)||0}}%
									</div>
									<div class="t">{{ $t('總盈利') }}</div>
								</div>
							</div>
							<!-- 内层列表 v-if="item.isShow"-->
							<template v-if="false">
								<div class="inner-list" v-if="item.allstock && item.allstock.length">
									<!-- <div class="flex flex-b title">
										<div class="">{{ $t("new").a66 }}</div>
										<div class="t-r">{{ $t("new").a67 }}</div>
									</div> -->
									<div class="inner-item flex flex-b" v-for="(items, i) in item.allstock" :key="i"
										v-if="items">
										<div class="t1">
											{{ items.symbol || items.code || "-" }}
										</div>
										<div class="t-r" :class="items.gain < 0 ? 'green' : 'red'">
											{{ items.gain > 0 ? "+" : "" }}{{ items.gain }}
										</div>
									</div>
								</div>
							</template>
							<!-- <div class="flex flex-b pad10">
								<div class="sylb flex" @click="changeGdshow(index)">
									{{ item.isShow ? $t("收起") : $t("展开") }}
									<div class="icon animate__animated animate__fadeIn"
										:class="item.isShow ? 's1' : 'x1'"></div>
								</div>
							</div> -->
							<!-- 时间 -->
							<div class="time flex flex-b flex-1">
								<div class="">
									<span>{{ $t('買入時間') }}</span>
									{{ $formatDate("YYYY/MM/DD", item.buy_time * 1000) }}
								</div>
								<!-- <div class="icon tm animate__animated animate__fadeIn"></div>
								<div class="">
									<span class="">鎖倉天數</span>
									{{ item.scday }}
								</div> -->
							</div>
							<div class="info flex flex-b flex-wrap">
								<div class="infoItem flex flex-b">
									<div class="t1">{{ $t('投資金額') }}</div>
									<div class="t2">{{ $formatMoney(item.money) || 0 }}</div>
								</div>
								<div class="infoItem flex flex-b">
									<div class="t1">{{ $t('日配息') }}</div>
									<div class="t2">{{$formatMoney(item.ryl,2)||0}}%</div>
								</div>
								<!-- <div class="flex flex-b">
									<div class="t1">買入價格</div>
									<div class="t2">{{ $formatMoney(item.price) || 0 }}</div>
								</div>
								<div class="flex flex-b">
									<div class="t1">買入數量</div>
									<div class="t2">-</div>
								</div>
								<div class="infoItem flex flex-b">
									<div class="t1">昨日收益</div>
									<div class="t2">{{ item.dprofit || "-" }}</div>
								</div>
								<div class="infoItem flex flex-b">
									<div class="t1">總收益</div>
									<div class="t2">{{ item.aprofit || "-" }}</div>
								</div> -->
								<!-- <div class="infoItem flex flex-b">
									<div class="t1">單號</div>
									<div class="t2">{{ item.strategy_num || "-" }}</div>
								</div> -->
							</div>
						</div>
					</div>
				</van-skeleton>
			</div>
			<!-- 持仓中弹出层 -->
			<van-popup v-model="show1" round position="center" :style="{ width: '80%' }">
				<div class="popup">
					<div class="title">{{ statusStr[detailItem.status] }}</div>
					<div class="pdd">
						<div class="flex flex-c num">
							<div class="">
								<div class="t num-font" :class="{ red: Number(detailItem.yingkui) >= 0 }">
									{{ Number(detailItem.yingkui) > 0 ? "+" : "" }}{{isNaN(Number(detailItem.yingkui).toFixed(2)) ? 0 : $formatMoney(detailItem.yingkui)||0}}
								</div>
								<!-- <div class="t1">{{ $t("new").a70 }}</div> -->
							</div>
						</div>
						<div class="flex flex-b">
							<div class="bt" @click="show1 = false">{{ $t('取消') }}</div>
							<div class="bt bt1" @click="sellstrategy(detailItem.id, detailItem.status)">
								{{ detailItem.status == 3 ? $t("掛單中") : $t("平倉") }}
							</div>
						</div>
					</div>
				</div>
			</van-popup>
		</van-pull-refresh>
		<tab-bar :current="2"></tab-bar>
		<loading ref="loading" />
	</div>
</template>

<script>
	import moneyCom1 from '../components/money1.vue'
	import * as echarts from "echarts";
	export default {
		name: "trade",
		props: {},
		data() {
			return {
				readNum: 0,
				percent: "",
				chartData: [],
				chartData1: [],
				show: true,
				show1: false,
				myChart: null,
				statusStr: ["持倉中", "已平倉", "準備平倉", "掛單中"],
				loading1: true,
				loading2: true,
				loading3: true,
				loading4: true,
				loading5: true,
				isLoading: false,
				cfg: {},
				detailItem: {},
				look: true,
				positionList: [
					// {
					// 	stock_name: "stock_name",
					// 	stock_code: "stock_code",
					// 	xgstate: "审核中",
					// 	yingkui: 1000,
					// 	stock_num: 1000,
					// 	buy_price: 1000,
					// 	buy_time: "2024-03-02",
					// 	market_value: 1000,
					// 	status: 3,
					// },
				],
				positionCloseList: [
					// {
					// 	stock_name: "stock_name",
					// 	stock_code: "stock_code",
					// 	xgstate: "审核中",
					// 	yingkui: 1000,
					// 	stock_num: 1000,
					// 	buy_price: 1000,
					// 	sell_time: "2024-03-02",
					// 	market_value: 1000,
					// 	sell_price: 100,
					// 	status: 3,
					// },
				],
				gdData: [
					// {
					// 	dprofit: "10%",
					// 	name: "name",
					// 	code: "code",
					// 	yprofit: "100%",
					// 	isShow: false,
					// 	allstock: [{
					// 		symbol: "symbol",
					// 		code: "code",
					// 		gain: 10,
					// 	}, ],
					// 	buy_time: "2024-03-02",
					// 	locktime: "2024-03-02",
					// },
				],
				xinguList: [
					// {
					// 	stock_name: "stock_name",
					// 	stock_code: "stock_code",
					// 	xgstate: "审核中",
					// 	apply_price: 1000,
					// 	lucky_total: 1000,
					// 	apply_total: 1000,
					// 	rjmoney: 1000,
					// 	buy_time: "2024-03-02",
					// 	market_value: 1000,
					// 	status: 1,
					// },
				],

				currmentIndex: 1,
				positionlistinteval: null,
				userInfo: {},
				freezeAssets: 0,
				szAssets: 0,
				ykAssets: 0,
				totalAssets: 0,
				xinguListJp: [
					// {
					// 	stock_name: "stock_name",
					// 	stock_code: "stock_code",
					// 	xgstate: "审核中",
					// 	apply_price: 1000,
					// 	lucky_total: 1000,
					// 	apply_total: 1000,
					// 	rjmoney: 1000,
					// 	buy_time: "2024-03-02",
					// 	market_value: 1000,
					// 	status: 1,
					// },
				],
				chooseList:[]
			};
		},
		components: {
			moneyCom1
		},
		computed: {
			navList() {
				return [{
						name: this.$t("申購"),
						type: 0,
					},
					{
						name: this.$t("ETF募集"),
						type: 3,
					},
					{
						name: this.$t("競拍"),
						type: 4,
					},
					{
						name: this.$t("庫存"),
						type: 1,
					},
					{
						name: this.$t("歷史記錄"),
						type: 2,
					},
				];
			}
		},
		created() {
			this.getNew()
			this.readData();
			this.getConfig();
			this.initData();
			this.changeTime();
			if (this.$route.query.idx) {
				this.currmentIndex = this.$route.query.idx
			}
		},
		mounted() {
			this.getNew()
			// this.$refs.firstLoading.open();
		},
		beforeDestroy() {
			clearInterval(this.positionlistinteval);
		},
		methods: {
			getNew() {
				this.$server.post("/trade/productlist", {
					type: "twd"
				}).then((res) => {

					this.chooseList = res.data;
				});
			},
			readData() {
				this.$server.post("/user/notice", {
					type: "twd"
				}).then((res) => {
					if (res.status == 1) {
						let list = res.data;
						let length = list.length
						let a
						for (a = 0; a < length; a++) {
							let read = localStorage.getItem("readMsg")
							let oldRead = JSON.parse(read)
							let hasValue = oldRead.id.includes(list[a].id.toString())
							if (!hasValue) {
								this.readNum += 1
							}
						}
					}
				});
			},
			clickRenJiao(item) {
				this.$server.post('/user/torenjiao', {
					id: item.id,
					type:'twd'
				}).then(res => {
					if (res.status === 1) {
						this.$toast(this.$translateServerText(res.msg));
					}
					setTimeout(() => {
						this.changeNav(this.currmentIndex);
					}, 1000)
				});
			},
			// 下拉刷新
			onRefresh() {
				this.getConfig();
				this.initData();
			},
			// 获取配置
			async getConfig() {
				const res = await this.$server.post("/common/config", {
					type: "twd"
				});
				let val = {};
				res.data.forEach((vo) => {
					val[vo.name] = vo.value;
				});
				let imgurl = this.$server.url.imgUrl.replace("/api", "");
				val.logo = imgurl + val.logo;

				if (process.env.NODE_ENV === "development") {
					val.logo = require("../../assets/login/logo.png");
				}
				this.cfg = val;
			},
			changeGdshow(index) {
				let arr = this.gdData.map((item, i) => {
					if (index == i) {
						item.isShow = !item.isShow;
					}
					return item;
				});
				this.gdData = arr;
			},
			getGd() {
				let that = this
				this.$server
					.post("/trade/userproductlist", {
						type: "twd"
					})
					.then((res) => {
						this.$refs.loading.close(); //关闭加载
						this.loading2 = false;
						if (res.status == 1) {
							res.data.forEach((item) => {
								item.isShow = false;
								if (item.allstock && item.allstock.length) {
									item.allstock = item.allstock.filter(Boolean);
								}
							});
							this.gdData = res.data;
							this.gdData.forEach(item=>{
								let itemData = that.chooseList.find(_item=>{
									return _item.sender == item.sender
								})
								that.$set(item,'ryl',parseFloat(itemData.dprofit.replace('%','')))
								that.$set(item,'zyl',(parseFloat(itemData.ssjg)-parseFloat(itemData.mjjg))/parseFloat(itemData.mjjg)*100)
							})
						}
					});
			},
			goItem(item) {
				this.$refs.loading.open(); //开启加载
				this.$storage.save("currentItem", item);
				setTimeout(() => {
					this.$refs.loading.close();

					this.$toPage("/trade/positionDetail");
				}, 1000);
			},
			sellItem(item) {
				this.detailItem = item;
				this.show1 = true;
			},
			changeTime() {
				this.positionlistinteval = setInterval(() => {
					this.changeType();
				}, 10 * 1000);
			},
			changeType() {
				this.initData();
			},
			changeNav(index) {
				this.currmentIndex = index;
				clearInterval(this.positionlistinteval);
				this.$refs.loading.open(); //开启加载

				switch (index) {
					case 0:
						this.getXingu();
						break;
					case 1:
					case 2:
						this.positionList = [];
						this.positionCloseList = [];
						this.changeType();
						this.changeTime();
						break;
					case 3:
						this.getGd();
						break;
					case 4:
						this.getXinguJp();
						break;
					default:
						break;
				}
			},
			// 平仓
			sellstrategy(id, status) {
				if (status == 3) {
					// 撤单
					this.cancelstrategy(id);
					return;
				}
				this.$server
					.post("/trade/sell_stock", {
						id,
						type: "twd",
					})
					.then((res) => {
						if (res.status == 1) {
							this.$toast(this.$translateServerText(res.msg));
							this.show1 = false;
							this.changeNav(2); //平倉成功切換顯示已平倉
							this.initData(true);
						}
					});
			},
			cancelstrategy(id) {
				this.$server
					.post("/trade/cancel_stock", {
						id,
						type: "twd",
					})
					.then((res) => {
						if (res.status == 1) {
							this.$toast(this.$translateServerText(res.msg));
							this.show1 = false;
							this.initData(true);
						}
					});
			},
			getXingu() {
				this.$server
					.post("/trade/usernewstocklist", {
						type: "twd",
						buy_type: 0
					})
					.then((res) => {
						this.$refs.loading.close(); //关闭加载
						this.loading1 = false;
						if (res.status == 1) {
							this.xinguList = res.data;
						}
					});
			},
			getXinguJp() {
				this.$server
					.post("/trade/usernewstocklist", {
						type: "twd",
						buy_type: 1
					})
					.then((res) => {
						this.$refs.loading.close(); //关闭加载
						this.loading5 = false;
						if (res.status == 1) {
							this.xinguListJp = res.data;
						}
					}).finally(e => {
						this.$refs.loading.close(); //关闭加载
						this.loading5 = false;
					});
			},
			// 持仓&p平仓列表
			initData() {
				let url = "";
				if (this.currmentIndex == 1) {
					url = "/trade/userstocklist";
				} else {
					url = "/trade/userstocklists";
				}

				this.$server.post(url, {
					type: "twd"
				}).then(async (res) => {
					this.isLoading = false; //下拉刷新状态
					this.loading3 = false;
					if (res.status == 1) {
						let arr = res.data;

						let szArr = []; //列表市值
						let ykArr = []; //列表盈亏
						let arr1 = []; //認繳的资金  买入本金+盈利(持仓冻结)
						let arr2 = [];

						arr.forEach((item) => {
							szArr.push(Number(item.market_value) + Number(item.yingkui));
							ykArr.push(Number(item.yingkui));
							arr1.push(
								Number(item.buy_price) * Number(item.stock_num) +
								Number(item.yingkui)
							);
							arr2.push(Number(item.buy_price) * Number(item.stock_num));

						});

						this.szAssets = szArr.reduce((a, b) => a + b, 0);
						this.ykAssets = ykArr.reduce((a, b) => a + b, 0);
						this.freezeAssets = arr1.reduce((a, b) => a + b, 0);

						let total2 = arr2.reduce((a, b) => a + b, 0);
						this.percent = ((this.ykAssets / (total2 || 1)) * 100).toFixed(2); //总盈亏比例

						const res1 = await this.$server.post("/user/getUserinfo", {
							type: "twd",
						});
						if (res.status == 1) {
							this.userInfo = res1.data;
						}

						// 总资产 可用+持仓资金
						this.totalAssets = Number(this.userInfo.twd) + this.freezeAssets;

						if (this.currmentIndex == 1) {
							this.positionList = arr;
						} else {
							this.positionCloseList = arr;
						}

						this.chartData = [
							// {
							//   value: this.totalAssets || 0,
							//   name: "",
							// },
							{
								value: this.szAssets || 0,
								name: "",
							},
							{
								value: this.ykAssets || 0,
								name: "",
							},
							{
								value: this.userInfo.dollar || 0,
								name: "",
							},
						];
						// this.getEcharts();
					}
					this.$refs.loading.close(); //关闭加载
				});
			},
			getEcharts() {
				let that = this;
				if (that.myChart !== null) {
					echarts.dispose(that.myChart);
				}

				let chartDom = document.getElementById("main");
				that.myChart = echarts.init(chartDom);
				let option;
				option = {
					color: ["#6970AF", "#F1BABB", "#A2DDDD"], // 顺时针
					tooltip: {
						trigger: "item",
					},
					// 頂部圖例
					legend: {
						top: "5%",
						left: "center",
						show: false,
					},
					series: [{
						name: "",
						type: "pie",
						// radius: ['40%', '70%'], //圆环
						radius: "100%",
						center: ["50%", "50%"],
						avoidLabelOverlap: false,
						label: {
							show: false,
							position: "center",
						},
						emphasis: {
							label: {
								show: true,
								fontSize: "40",
								fontWeight: "bold",
							},
						},
						labelLine: {
							show: false,
						},
						data: this.chartData,
					}, ],
				};

				option && that.myChart.setOption(option);
			},
		},
	};
</script>

<style scoped lang="less">
	::v-deep .van-skeleton__row {
		background-color: transparent !important;
	}

	::v-deep .van-skeleton__title {
		background-color: transparent !important;
	}

	::v-deep .van-skeleton__row {
		background-color: transparent !important;
	}

	::v-deep .van-skeleton__title {
		background-color: transparent !important;
	}

	.top-fixed {
		width: 100%;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 999;

		.header {
			height: 0.5rem;
			padding: 0.15rem;
			width: 100%;
			background: #18191B;
			position: relative;

			.tit {
				font-family: PingFang SC;
				font-weight: 500;
				font-size: 0.16rem;
				color: #FFFFFF;
			}
		}

	}

	::v-deep .van-popup {
		border-radius: 0.04rem;
	}

	.btnss {
		padding: 0.15rem 0 0;
		text-align: center;
		.icon {
			margin: 0.05rem 0;
		}

		.t {
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 0.12rem;
			color: #A2A2A2;
		}
	}

	.page {
		min-height: 100vh;
		padding: 0.5rem 0 1rem;

		.nav-box {
			margin: 0 0.12rem;
			padding: 0.02rem;
			height: 0.47rem;
			background: #232429;
			border-radius: 0.23rem;

			.nav-item {
				flex: 1;
				font-family: PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
				color: #999999;
				line-height: 0.31rem;
				border-radius: 0.04rem 0.04rem 0.04rem 0.04rem;

				&.active {
					height: 0.31rem;
					background: #8DFD99;
					border-radius: 0.16rem;
					color: #000000;
					position: relative;
					// &::after {
					// 	position: absolute;
					// 	content: '';
					// 	bottom: 0;
					// 	left: 0;
					// 	width: 100%;
					// 	height: 0.03rem;
					// 	background-color: #e5c79f;
					// }
				}
			}
		}

		#main {
			width: 0.66rem;
			height: 0.66rem;
			border-radius: 50%;
		}

		.money {
			margin: 0.12rem;
			background: linear-gradient(-45deg, #78DB83, #E4F8A8);
			border-radius: 0.13rem;
			padding: 0.2rem 0.12rem 0.12rem;
			.tops {
				margin-bottom: 0.1rem;
				.t {
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 0.13rem;
					color: #000000;
				}
				.num {
					margin-top: 0.05rem;
					font-family: PingFang SC;
					font-weight: 600;
					font-size: 0.21rem;
					color: #000000;
					.icon {
						margin-left: 0.1rem;
					}
				}
			}

			.sz-num {
				margin-top: 0.12rem;
				background: rgba(255, 255, 255, 0.4);
				border-radius: 0.21rem;
				.szItem {
					width: 50%;
					padding: .12rem;
					text-align: center;
					.t {
						font-family: PingFang SC;
						font-weight: 400;
						font-size: 0.14rem;
						color: #999999;
					}

					.t1 {
						font-weight: 600;
						font-family: PingFang SC;
						font-weight: 400;
						font-size: 0.14rem;
						color: #000;
					}
				}
			}

			.txt {
				height: 0.29rem;
				background: #000000;
				padding: 0 0.1rem;

				.t1 {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.12rem;
					color: #B6B2AD;
				}

				.t2 {
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.14rem;
				}
			}
		}
	}

	.cy-list {
		margin: 0.12rem;
		.cy-item {
			background: #232429;
			border-radius: 0.13rem;
			margin-bottom: 0.1rem;
			.top {
				padding: 0.12rem;
				.name {
					font-family: PingFang SC;
					font-weight: 500;
					font-size: 0.15rem;
					color: #FFFFFF;
				}

				.code {
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 0.11rem;
					color: #999999;
					margin-left: 0.05rem;
				}
			}

			.sy {
				align-items: flex-start;

				div {
					font-family: PingFang TC, PingFang TC;
					font-weight: 500;
					font-size: 0.14rem;
					color: #FF2929;
				}

				.t {
					font-family: PingFang TC, PingFang TC;
					font-weight: 400;
					font-size: 0.12rem;
					color: #ABAAB9;
					margin-right: 0.05rem;
				}

				.per {
					margin-top: 0.05rem;
				}
			}

			.btns {
				padding: 0.12rem;
				.btn {
					height: 0.26rem;
					line-height: 0.26rem;
					border-radius: 0.16rem;
					border: 0.01rem solid #8dfd99;
					width: 48%;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 0.12rem;
					color: #8dfd99;
					text-align: center;

					&.bt {
						border: none;
						background: #8dfd99;
						color: #000;
					}
				}
				.bt1{
					border: none;
					color:#fff;
					background: linear-gradient(90deg, #ff0000 0%, #ff0000 100%);
				}
			}

			.time {
				padding: 0.12rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
				color: #999999;
			}

			.inner {
				margin: 0 0.12rem;
				padding: 0.12rem;
				flex-wrap: wrap;
				background: #434446;
				border-radius: 0.09rem;
				.inner-item {
					line-height: 0.3rem;
					width: 46%;

					.t1 {
						font-family: PingFang SC;
						font-weight: 400;
						font-size: 0.13rem;
						color: #999999;
					}

					.t2 {
						font-family: PingFang SC;
						font-weight: 400;
						font-size: 0.13rem;
						color: #fff;

						span {
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							font-size: 0.13rem;
							color: #333333;
						}
					}
				}
			}
		}
	}

	::v-deep .van-popup {
		border-radius: 0.1rem;
		background: #232429;
		border-radius: 0.13rem;
	}

	.popup {
		background: #232429;
		border-radius: 0.13rem;
		border: none;
		.title {
			font-size: 0.16rem;
			padding: 0.15rem 0;
			color: #fff;
			text-align: center;
		}

		.pdd {
			padding: 0 0.15rem 0.15rem;
			text-align: center;

			.num {
				margin: 0.15rem 0;
			}

			.icon {
				margin-right: 0.15rem;
				width: 0.24rem;
				height: 0.24rem;
			}

			.t {
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 0.26rem;
				color: #333333;
			}

			.t1 {
				font-size: 0.12rem;
				color: #999;
			}

			.bt {
				width: 48%;
				margin-top: 0.15rem;
				height: 0.36rem;
				border-radius: 0.16rem;
				border: 0.01rem solid #8dfd99;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 0.12rem;
				color: #8dfd99;
				line-height: 0.36rem;

				&.bt1 {
					border: none;
					color: #000;
					background: #8dfd99;
				}
			}
		}
	}

	.gd-list {
		padding: 0.12rem;
		.gd-item {
			padding: 0.12rem;
			background: #232429;
			border-radius: 0.13rem;
			margin-bottom: 0.1rem;
			.name {
				font-family: PingFang SC;
				font-weight: 500;
				font-size: 0.15rem;
				color: #FFFFFF;
			}

			.code {
				font-family: PingFang SC;
				font-weight: 400;
				font-size: 0.11rem;
				color: #999999;
				margin-left: 0.05rem;
			}

			.center {
				padding: 0.1rem 0.6rem;
				background: #434446;
				border-radius: 0.09rem;
				line-height: 0.26rem;
				margin: 0.1rem 0;

				.tt {
					font-weight: 400;
					font-size: 0.18rem;
					color: #fff;
				}

				.t {
					font-family: PingFang TC, PingFang TC;
					font-weight: 400;
					font-size: 0.12rem;
					color: #fff;
				}

				.icon {
					margin-left: 0.05rem;
				}
			}

			.status {
				background: #CC3333;
				border-radius: 0.02rem 0.02rem 0.02rem 0.02rem;
				padding: 0.05rem 0.1rem;
				font-size: 0.12rem;
				color: #ffffff;

				&.end {
					background: #4A4A4A;
					color: #FFFFFF;
				}
			}

			.inner-list {
				flex-wrap: wrap;

				div {
					font-size: 0.12rem;
					font-weight: 500;
					color: #999999;
				}

				.inner-item {
					padding: 0.1rem 0;

					.t1 {
						font-size: 0.12rem;
						color: #686868;
					}

					.t-r {
						font-size: 0.12rem;
					}

					.red {
						color: #ba3b3a;
					}

					.green {
						color: #39B44C;
					}
				}
			}

			.time {
				margin-bottom: 0.1rem;
				div,
				span {
					font-size: 0.12rem;
					color: #999;
				}
			}
			.info {
				padding: 0.12rem;
				background: #434446;
				border-radius: 0.09rem;
				.infoItem {
					width: 46%;
					padding: 0.05rem 0;
				}
				.t1 {
					font-family: PingFang TC, PingFang TC;
					font-weight: 400;
					font-size: 0.12rem;
					color: #999;
				}
				.t2 {
					font-family: PingFang TC, PingFang TC;
					font-weight: 400;
					font-size: 0.12rem;
					color: #fff;
				}
			}
			.sylb {
				font-size: 0.12rem;
				color: #1b1b1b;
				margin: 0.05rem auto;
				.icon {
					margin-left: 0.05rem;
				}
			}
		}
	}

	.sg-list {
		margin: 0.12rem;
		.sg-item {
			background: #232429;
			border-radius: 0.13rem;
			overflow: hidden;
			margin-bottom: 0.1rem;
			.top {
				padding: 0.12rem;
				.name {
					font-family: PingFang SC;
					font-weight: 500;
					font-size: 0.15rem;
					color: #FFFFFF;
				}
				.code {
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 0.11rem;
					color: #999999;
					margin-left: 0.05rem;
				}

				.status {
					height: 0.3rem;
					background: #8dfd99;
					border-radius: 0.16rem;
					padding: 0 0.1rem;
					font-family: PingFang SC;
					font-weight: 500;
					font-size: 0.13rem;
					color: #000000;
					line-height: 0.3rem;
					text-align: center;

					&.pc {
						background-color: #f8f8f8;
						color: #000;
					}
				}
			}

			.data {
				margin: 0 0.12rem 0.12rem;
				flex-wrap: wrap;
				padding: 0.12rem;
				background: #434446;
				border-radius: 0.09rem;
				.t {
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 0.13rem;
					color: #999999;
				}

				.t1 {
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 0.13rem;
					color: #fff;

					span {
						font-family: PingFang SC;
						font-weight: 400;
						font-size: 0.13rem;
						color: #fff;
					}
				}

				.price {
					color: #EB2733;
				}

				.data-item {
					line-height: 0.3rem;
					width: 46%;
				}
			}
		}
	}
	.bagby{
		width: .16rem !important;
		height: .16rem !important;
	}
</style>