import Vue from 'vue'
import VueRouter from 'vue-router'


Vue.use(VueRouter)

const routes = [{
		path: '/',
		redirect: '/login/start'
	},
	{
		path: '/login/start',//
		name: 'start',
		component: resolve => (require(["@/components/login/start"], resolve))
	},
	{
		path: '/login/logon',//
		name: 'logon',
		component: resolve => (require(["@/components/login/logon"], resolve))
	},
	{
		path: '/login/logon2',//
		name: 'logon2',
		component: resolve => (require(["@/components/login/logon2"], resolve))
	},
	{
		path: '/login/login',//登录
		name: 'login',
		component: resolve => (require(["@/components/login/login"], resolve))
	},
	{
		path: '/login/register',//注册
		name: 'register',
		component: resolve => (require(["@/components/login/register"], resolve))
	},
	{
		path: '/home/<USER>',//首页
		name: 'hindex',
		component: resolve => (require(["@/components/home/<USER>"], resolve))
	},
	{
		path: '/home/<USER>',//新闻列表
		name: 'moreNews',
		component: resolve => (require(["@/components/home/<USER>"], resolve))
	},
	{
		path: '/home/<USER>',//新闻详情
		name: 'DetailsPage',
		component: resolve => (require(["@/components/home/<USER>"], resolve))
	},
	{
		path: '/user/usercenter',//个人中心
		name: 'usercenter',
		component: resolve => (require(["@/components/user/usercenter"], resolve))
	},
	{
		path: '/user/setting',//设置
		name: 'setting',
		component: resolve => (require(["@/components/user/setting"], resolve))
	},
	{
		path: '/user/loginPassword',//修改登录密码
		name: 'loginPassword',
		component: resolve => (require(["@/components/user/loginPassword"], resolve))
	},
	{
		path: '/user/setPassword',//修改资金密码
		name: 'setPassword',
		component: resolve => (require(["@/components/user/setPassword"], resolve))
	},
	{
		path: '/user/recharge',//充值
		name: 'recharge',
		component: resolve => (require(["@/components/user/recharge"], resolve))
	},
	{
		path: '/user/rechargeChannel',//充值通道
		name: 'rechargeChannel',
		component: resolve => (require(["@/components/user/rechargeChannel"], resolve))
	},
	{
		path: '/user/withdraw',//提现
		name: 'withdraw',
		component: resolve => (require(["@/components/user/withdraw"], resolve))
	},
	{
		path: '/user/WithCard',//银行卡管理
		name: 'WithCard',
		component: resolve => (require(["@/components/user/WithCard"], resolve))
	},
	{
		path: '/user/BankCard',//添加银行卡
		name: 'BankCard',
		component: resolve => (require(["@/components/user/BankCard"], resolve))
	},
	{
		path: '/user/FundingDetails',//资金明细
		name: 'FundingDetails',
		component: resolve => (require(["@/components/user/FundingDetails"], resolve))
	},
	{
		path: '/user/bandCard',//实名认证
		name: 'bandCard',
		component: resolve => (require(["@/components/user/bandCard"], resolve))
	},
	{
		path: '/user/exchange',//兑换
		name: 'exchange',
		component: resolve => (require(["@/components/user/exchange"], resolve))
	},
	{
		path: '/user/changeLanuage',//更换语言
		name: 'changeLanuage',
		component: resolve => (require(["@/components/user/changeLanuage"], resolve))
	},
	{
		path: '/market/market',//市场
		name: 'market',
		component: resolve => (require(["@/components/market/market"], resolve))
	},
	{
		path: '/market/stockHot/stockList',//热门市场
		name: 'stockList',
		component: resolve => (require(["@/components/market/stockHot/stockList"], resolve))
	},
	{
		path: '/market/marketDetail',//详情页
		name: 'marketDetail',
		component: resolve => (require(["@/components/market/marketDetail"], resolve))
	},
	{
		path: '/market/marketBuy',//详情页购买
		name: 'marketBuy',
		component: resolve => (require(["@/components/market/marketBuy"], resolve))
	},
	{
		path: '/position/position',//交易
		name: 'position',
		component: resolve => (require(["@/components/position/position"], resolve))
	},
	{
		path: '/position/positionDetail',//交易详情
		name: 'positionDetail',
		component: resolve => (require(["@/components/position/positionDetail"], resolve))
	},
	{
		path: '/home/<USER>',//搜索
		name: 'search',
		component: resolve => (require(["@/components/home/<USER>"], resolve))
	},
	{
		path: '/market/subscribe',//新股申购
		name: 'subscribe',
		component: resolve => (require(["@/components/market/subscribe"], resolve))
	},
	{
		path: '/market/subscribeDetail',//新股申购 详情
		name: 'subscribeDetail',
		component: resolve => (require(["@/components/market/subscribeDetail"], resolve))
	},
	{
		path: '/market/subscribeRecord',//新股申购 记录
		name: 'subscribeRecord',
		component: resolve => (require(["@/components/market/subscribeRecord"], resolve))
	},
	{
		path: '/common/dayTrading',//日内交易
		name: 'dayTrading',
		component: resolve => (require(["@/components/common/dayTrading"], resolve))
	},
	{
		path: '/common/dividend',//大宗交易
		name: 'dividend',
		component: resolve => (require(["@/components/common/dividend"], resolve))
	},
	{
		path: '/gdindex/gdindex',//量化跟单
		name: 'gdindex',
		component: resolve => (require(["@/components/gdindex/gdindex"], resolve))
	},
	{
		path: '/gdindex/gdList',//更单
		name: 'gdList',
		component: resolve => (require(["@/components/gdindex/gdList"], resolve))
	},
	{
		path: '/common/dividend2',//盘前盘后交易
		name: 'dividend2',
		component: resolve => (require(["@/components/common/dividend2"], resolve))
	},
	{
		path: '/common/dividend3',//抢筹
		name: 'dividend3',
		component: resolve => (require(["@/components/common/dividend3"], resolve))
	},
	
	
	
	
	
	{
		path: '/user/aboutUs',
		name: 'aboutUs',
		component: resolve => (require(["@/components/user/aboutUs"], resolve))
	},
	{
		path: '/user/pj',
		name: 'pj',
		component: resolve => (require(["@/components/user/pj"], resolve))
	},
	{
		path: '/user/jkjl',
		name: 'jkjl',
		component: resolve => (require(["@/components/user/jkjl"], resolve))
	},
	
]

const router = new VueRouter({
	linkActiveClass: "navActive",
	mode: 'hash',
	scrollBehavior(to, from, savePosition) {
		if (savePosition) {
			return savePosition
		} else {
			return {
				x: 0,
				y: 0
			}
		}
	},
	routes
})



export default router