<template>
	<div class="usercenter">
		<div class="head">
			<div class="logoBox flex flex-b">
				<!-- <img class="ico" src="../../assets/skin/mine/<EMAIL>" @click="clickNext('/home/<USER>')" /> -->
				<div class="icon close" @click="hiddenUser()"></div>
				<!-- <div class="flex-1">{{$t('mine').title}}</div> -->
				<div class="flex">
					<div class="border flex" @click="clickNext('/user/setting')">
						<div class="icon set"></div>
						{{ $t("other").txt4 }}
					</div>
					<!--          <div class="border flex">-->
					<!--            <div class="icon xxIcon"></div>-->
					<!--            {{ $t("other").txt5 }}-->
					<!--          </div>-->
				</div>
			</div>
			<div class="userInfo flex flex-b">
				<div class="flex">
					<div class="headerImg flex flex-c">
						<img src="../../assets/skin/mine/toux.png" />
					</div>
					<div>
						<span>{{ userdata.account }}</span>
					</div>
				</div>
			</div>

			<div class="money flex flex-b">
				<div class="">
					<div class="title">{{ $t("mine").money.txt1 }}</div>
					<div class="total" style="white-space: nowrap;">
						{{ $formatMoney(parseFloat(userdata.jpy) + parseFloat(ccsz)) }}円
					</div>
					<!-- <div class="mine"></div> -->
					<div ref="chartContainer" id="main" style="width: 0.51rem;;height:0.51rem;;"></div>
				</div>
				<ul class="flex flex-b flex-wrap text-center">
					<li class="flex-column-item" style="width: 100%">
						<div>{{ $formatMoney(userdata.jpy) }}円</div>
						<div class="flex">
							<div class="c1"></div>
							<span>{{ $t("mine").money.txt3 }}</span>
						</div>
					</li>
					<!--          <li class="flex-column-item">-->
					<!--            <div>{{ $formatMoney(ccsz) }}</div>-->
					<!--            <div class="flex">-->
					<!--              <div class="c1" style="background-color: #3498DA;"></div>-->
					<!--              <span>{{ $t("mine").money.txt2 }}</span>-->
					<!--            </div>-->
					<!--          </li>-->
					<li class="flex-column-item" style="width: 100%">
						<div>{{ $formatMoney(totalYk) }}円</div>
						<div class="flex">
							<div class="c1" style="background-color: #FE0000;"></div>
							<span>{{ $t("mine").money.txt4 }}</span>
						</div>
					</li>
					<!--          <li class="flex-column-item">-->
					<!--            <div>{{ $formatMoney(userdata.usd) }}</div>-->
					<!--            <div class="flex">-->
					<!--              <div class="c1" style="background-color: #7585A2;"></div>-->
					<!--              <span>{{ $t("mine").money.txt5 }}</span>-->
					<!--            </div>-->
					<!--          </li>-->
				</ul>
			</div>
		</div>
		<div class="btn flex flex-b text-center">
			<div class="link flex flex-c" @click="clickNext('/user/recharge')">
				<div class="icon czIcon"></div>
				<div>{{ $t("mine").money.btn1 }}</div>
			</div>
			<div class="link flex flex-c" @click="clickNext('/user/withdraw')">
				<div class="icon txIcon"></div>
				<div>{{ $t("mine").money.btn2 }}</div>
			</div>
		</div>

		<div class="list">
			<div class="tit">{{ $t("other").txt6 }}</div>
			<div class="itemBox flex-wrap flex flex-b">
				<div class="item flex-column-item" style="width: 32%" v-for="(item, index) in list02" :key="index + 'a'"
					@click="item.link=='aipage'?toaipage():clickList(item)">
					<div class="flex flex-c" style="height: 0.18rem;">
						<img :src="require('../../assets/v2/' + item.ico)" :style="{ width: item.icoW }" />
					</div>
					<div style="margin-top: 0.1rem;">{{ item.name }}</div>
				</div>
			</div>
		</div>

		<div class="list">
			<div class="tit">{{ $t("other").txt7 }}</div>
			<div class="itemBox flex-wrap flex flex-b">
				<div class="item flex-column-item" v-for="(item, idx) in list" :key="idx + 'b'"
					@click="clickList(item)">
					<div class="flex flex-c" style="height: 0.18rem;">
						<img :src="require('../../assets/v2/' + item.ico)" :style="{ width: item.icoW }" />
					</div>
					<div style="margin-top: 0.1rem;">{{ item.name }}</div>
				</div>
				<div class="item flex-column-item" v-for="(item, idx) in 4 - (list.length % 4)" :key="idx"
					style="opacity: 0;"></div>
			</div>
		</div>

		<div class="ggximg" @click="showG = false" v-if="showG">
			<div class="icon close02"></div>
		</div>
		<div class="flex flex-e" @click="quit">
			<div class="quit">{{ $t("mine").list.txt8 }}</div>
		</div>

		<van-action-sheet v-model="langShow" @select="selectLang" :actions="actions" :cancel-text="$t('取消')"
			close-on-click-action @cancel="onCancel" />
		<!-- 底部菜单 -->
		<!-- <bottomnav :on='4'></bottomnav> -->
	</div>
</template>
<script type="text/javascript">
	import * as echarts from "echarts";
	import Vue from "vue";
	import qs from "qs";
	import axios from "axios";
	import bottomnav from "../bar/bottomnav.vue";

	import {
		Toast,
		Circle,
		Icon,
		ActionSheet
	} from "vant";

	Vue.use(Toast)
		.use(Circle)
		.use(Icon)
		.use(ActionSheet);
	import {
		getLangName,
		getLang,
		setLang,
		langList
	} from "../../lib/local.js";
	export default {
		name: "usercenter",
		components: {
			bottomnav,
		},
		data() {
			return {
				showG: true,
				userdata: [],
				ktiem: null,
				lang: "",
				langShow: false,
				actions: [{
						name: "中文繁體",
						type: "tw",
					},
					{
						name: "English",
						type: "en",
					},
					{
						name: "日本語",
						type: "jp",
					},
					//{name: "Bahasa Indonesia",type:'yn'}
				],
				langName: "",
				jpy: "",
				ccsz: 0,
				fdyk: 0,
				lsyk: 0,
				type: "/trade",
				list: [{
						name: this.$t("mine").list.txt1,
						ico: "jrk.png",
						icoW: ".2rem",
						link: "/user/WithCard",
					},
					{
						name: this.$t("other").txt9,
						ico: "smrz.png",
						icoW: ".19rem",
						link: "/user/bandCard",
					},
					{
						name: this.$t("mine").list.txt2,
						ico: "jrk.png",
						icoW: ".2rem",
						link: "/user/FundingDetails?num=1",
					},
					{
						name: this.$t("mine").list.txt6,
						ico: "jymm.png",
						icoW: ".14rem",
						link: "/user/setPassword",
					},
					{
						name: this.$t("mine").list.txt5,
						ico: "dlmm.png",
						icoW: ".16rem",
						link: "/user/loginPassword",
					},
					// {
					//   name: this.$t("mine").list.txt3,
					//   ico: "dlmm.png",
					//   icoW: ".16rem",
					//   link: "/user/exchange",
					// },
					// {
					// 	name: this.$t('mine').list.txt8,
					// 	ico: 'dlmm.png',
					// 	icoW: '.16rem',
					// 	link: 'quit'
					// },
					/* {
						name: this.$t("other").txt8,
						ico: "yyqh.png",
						icoW: ".2rem",
						link: "/user/changeLanuage",
						// link: 'changeLanuage'
					}, */
					// {
					// 	name: this.$t('mine').list.txt7,
					// 	ico: 'dlmm.png',
					// 	icoW: '.16rem',
					// 	link: 'kefu'
					// },
				],
				list02: [{
						name: this.$t("home").top2, // 新股申购
						ico: "xgsg.png",
						link: "/market/subscribe",
						icoW: ".21rem",
						isShow: true,
					},
					{
						name: this.$t("home").top9, //日内交易
						ico: "rnjy.png",
						link: "/common/dividend?type=3",
						icoW: ".19rem",
						isShow: true,
					},
          {
            name: this.$t("j2"), //日内交易
            ico: "pqjy.png",
            link: "/common/dividend?type=0",
            icoW: ".19rem",
            isShow: true,
          },
					{
						name: this.$t("home").top14, //大宗交易
						ico: "dzjy.png",
						link: "/common/dividend?type=1",
						//link: "dz",
						icoW: ".2rem",
						isShow: true,
					},
					{
						name: this.$t('home').txt28, //跟单
						ico: 'lhgd.png',
						link: 'aipage',
						icoW: '.19rem',
						isShow: true
					},
					// {
					// 	name: this.$t('home').top3, //跟单
					// 	ico: 'lhgd.png',
					// 	link: '/gdindex/gdindex',
					// 	icoW: '.19rem',
					// 	isShow: true
					// },
					// {
					// 	name: this.$t('home').top10, // 盘前交易
					// 	ico: 'pqjy.png',
					// 	link: '/common/dividend2?type=3',
					// 	icoW: '.19rem',
					// 	isShow: true
					// },
					// {
					// 	name: this.$t('home').top11, //盘后交易
					// 	ico: 'phjy.png',
					// 	link: '/common/dividend2?type=4',
					// 	icoW: '.2rem',
					// 	isShow: true
					// },
					// {
					//   name: this.$t("home").top12, //抢筹
					//   ico: "qc.png",
					//   link: "/common/dividend3",
					//   icoW: ".18rem",
					//   isShow: true,
					// },

					{
						name: this.$t("机构抢筹"),
						ico: "qc.png",
						link: "qc",
						icoW: ".18rem",
						isShow: true,
					},
					{
						name: this.$t("home").top13, //客服
						ico: "zxkf.png",
						link: "kefu",
						icoW: ".18rem",
						isShow: true,
					},
					{
						name: "",
						ico: "qc.png",
						link: "",
						icoW: ".18rem",
						isShow: true,
					},
				],
			};
		},
		computed: {
			totalYk() {
				// 浮動盈虧加上歷史盈虧
				return Number(this.fdyk) + Number(this.lsyk);
			},
		},
		beforeDestroy() {
			clearInterval(this.ktiem);
		},
		created() {},
		methods: {
			initChart() {
				const chartContainer = this.$refs.chartContainer;
				if (!this.chart) {
					this.chart = echarts.init(chartContainer);
				}

				const option = {
					title: {
						text: "",
					},
					series: [{
						type: "pie", // 图表类型为饼图
						data: [{
								value: parseFloat(this.userdata.jpy).toFixed(2),
								name: this.$t("mine").money.txt3,
							}, // 饼图数据
							{
								value: parseFloat(this.ccsz).toFixed(2),
								name: this.$t("mine").money.txt2,
							},
							{
								value: parseFloat(this.totalYk).toFixed(2),
								name: this.$t("mine").money.txt4,
							},
							{
								value: parseFloat(this.userdata.usd).toFixed(2),
								name: this.$t("mine").money.txt5,
							},
						],
						labelLine: {
							show: false, //隐藏指示线
						},
						label: {
							show: false, //隐藏标示文字
						},
						itemStyle: {
							color: function(colors) {
								var colorList = [
									"#55ABCE",
									"#DD4747",
									"#E2B443",
									"#5F2DF0",
									"#f08b69",
								];
								return colorList[colors.dataIndex];
							},
						},
					}, ],
				};
				this.chart.setOption(option);
			},
			hiddenUser() {
				this.$emit("handleHidden", false);
			},
			quit() {
				window.localStorage.removeItem("tokend");
				this.$router.push({
					path: "/login/login",
				});
			},
			getDatas() {
				this.$server.post("/trade/userstocklists", {
					type: 'jpy'
				}).then((str) => {
					if (str.data.status == 1) {
						this.lsyk = 0;
						let list = str.data.data;
						let length = list.length
						let a
						for (a = 0; a < length; a++) {
							this.lsyk += parseFloat(list[a].yingkui)
						}
					} else {
						Toast({
							message: this.$t(str.data.msg),
							duration: 2000,
						});
					}
				});
			},
			onCancel() {},
			selectLang(action) {
				switch (action.name) {
					case "中文繁體":
						setLang("tw");
						break;
					case "English":
						setLang("en");
						break;
					case "日本語":
						setLang("jp");
						break;
					default:
						setLang("en");
				}
				this.lang = action.name;
				location.reload();
			},
			changeLang() {
				this.langShow = true;
			},
			getUser() {
				clearInterval(this.ktiem);

				this.ktiem = setInterval(() => {
					this.getUser();
				}, 2000);

				this.$server.post("/user/getUserinfo").then((str) => {
					if (str.data.status == 1) {
						this.userdata = str.data.data;
						this.initChart();
						if (str.data.data.jpy) {
							this.jpy = str.data.data.jpy;
						} else {
							this.jpy = 0;
						}
						this.userdata.account = this.userdata.account.replace(
							/(\d{3})\d{4}(\d{4})/,
							"$1****$2"
						);
						window.localStorage.setItem(
							"userdata",
							JSON.stringify(str.data.data)
						);
					} else {
						Toast({
							message: this.$t(str.data.msg),
							duration: 2000,
						});
					}
				});
			},
			toaipage() {
				Toast({
					message: this.$t('home').txt29,
					duration: 2000,
				});
			},
			getCc() {
				this.$server.post("/trade/userstocklist", {
					type: 'jpy'
				}).then((str) => {
					if (str.data.status == 1) {
						this.fdyk = 0
						this.ccsz = 0;
						let list = str.data.data;
						let length = list.length
						let a
						for (a = 0; a < length; a++) {
							this.fdyk += parseFloat(list[a].yingkui)
							this.ccsz += parseFloat(list[a].stock_num) * parseFloat(list[a].nowprice)
						}
					}
				});
			},
			clickList(item) {
				// 点击大宗提示
				if (item.link == "dz") {
					if (this.userdata.jpy < ********) {
						Toast({
							message: this.$t("资金未达到1000万日元无法完成"),
							duration: 2000,
						});
						return;
					}

					this.$router.push({
						path: "/common/dividend?type=1",
					});
					return;
				}
				// 点击抢筹提示
				if (item.link == "qc") {
					if (this.userdata.jpy < 5000000) {
						Toast({
							message: this.$t("资金未达到500万日元无法完成"),
							duration: 2000,
						});
						return;
					}

					this.$router.push({
						path: "/common/dividend3",
					});
					return;
				}

				if (item.link == "kefu") {
					this.$server.post("/common/config", {
						type: 'all'
					}).then((res) => {
						if (parseInt(res.status) === 200) {
							let list = res.data.data;
							let listLength = list.length;
							let a;
							for (a = 0; a < listLength; a++) {
								if (list[a].name === "kefu") {
									this.openInBrowser(list[a].value);
								}
							}
						}
					});
					return;
				} else if (item.link == "quit") {
					window.localStorage.removeItem("tokend");
					this.$router.push({
						path: "/login/login",
					});
				} else if (item.link == "changeLanuage") {
					this.langShow = true;
				} else {
					this.$router.push({
						path: item.link,
					});
				}
			},
		},
		destroyed() {},
		mounted() {
			this.lang = getLangName();
			this.getUser();
			this.getCc();
			this.getDatas();
		},
	};
</script>
<style type="text/css" lang="less" scoped="scoped">
	.ggximg {
		margin-bottom: 0.1rem;
		width: 100%;
		height: 0.7rem;
		background: url("../../assets/v2/xwbg.png") no-repeat center/100%;
		position: relative;

		.close02 {
			position: absolute;
			top: 0.05rem;
			right: 0.05rem;
		}
	}

	// 新
	.usercenter {
		padding-bottom: 0.68rem;
		background-color: #1b232a;

		.head {
			.logoBox {
				padding: 0.15rem 0.1rem;
				font-weight: 400;
				font-size: 0.18rem;
				color: #ffffff;
				position: relative;

				.ico {
					width: 0.2rem;
					position: absolute;
					top: 0.26rem;
					left: 0.12rem;
				}

				.logo {
					width: 0.7rem;
				}

				.lang {
					font-size: 0.12rem;
					margin-right: 0.12rem;
				}

				.setting {
					position: absolute;
					top: 0.2rem;
					right: 0.12rem;

					img {
						width: 0.3rem;
					}
				}

				.border {
					margin-left: 0.1rem;
					padding: 0.02rem 0.1rem;
					background: rgba(255, 255, 255, 0.1);
					border-radius: 0.05rem;
					border: 0.01rem solid rgba(255, 255, 255, 0.2);
					font-weight: 500;
					font-size: 0.13rem;
					color: #5ed5a8;
				}
			}

			.userInfo {
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 0.13rem;
				color: #ffffff;

				.headerImg {
					width: 0.6rem;
					height: 0.6rem;
					margin-right: 0.05rem;

					img {
						width: 0.4rem;
						height: 0.4rem;
						border-radius: 50%;
					}
				}

				.shiMing {
					margin-right: 0.2rem;
					height: 0.3rem;
					background: linear-gradient(-81deg, #19cec7, #62e0da);
					border-radius: 0.13rem;
					border: 0.01rem solid #fff;
					padding: 0 0.07rem;

					img {
						width: 0.13rem;
						display: block;
						margin-right: 0.06rem;
					}

					.item {
						height: 0.3rem;
						font-weight: 500;
						font-size: 0.13rem;
						color: #e9eaf0;
					}
				}
			}

			.money {
				padding: 0.1rem;
				margin: 0.1rem 0.1rem;
				background: #ffffff;
				border-radius: 0.05rem;

				.title {
					font-weight: 600;
					font-size: 0.11rem;
					color: #333333;
				}

				.total {
					font-weight: 500;
					font-size: 0.11rem;
					color: #333333;
				}

				.mine {
					margin-top: 0.1rem;
					width: 0.51rem;
					height: 0.51rem;
					background-color: #7585a2;
					border-radius: 50%;
				}

				ul {
					width: 75%;

					li {
						width: 50%;
						font-weight: 600;
						font-size: 0.14rem;
						color: #333333;
						margin-bottom: 0.07rem;

						span {
							font-size: 0.14rem;
							font-weight: 400;
							color: #ababab;
							display: block;
						}

						.c1 {
							margin-right: 0.02rem;
							width: 0.08rem;
							height: 0.08rem;
							background: #5ed5a8;
							border-radius: 50%;
						}
					}
				}
			}
		}

		.btn {
			padding: 0 0.1rem 0.2rem;

			.link {
				width: 48%;
				height: 0.33rem;
				font-weight: 500;
				font-size: 0.12rem;
				color: #333333;
				background: #5ed5a8;
				border-radius: 0.05rem;
				border: 0.01rem solid rgba(255, 255, 255, 0.2);
			}
		}

		.list {
			font-weight: 500;
			font-size: 0.15rem;
			color: #ababab;

			.tit {
				padding: 0.02rem 0.1rem;
				background: rgba(94, 213, 168, 0.2);
				font-weight: 600;
				font-size: 0.12rem;
				color: #ffffff;
			}

			.itemBox {
				margin: 0.1rem;
			}

			.item {
				margin-bottom: 0.1rem;
				width: 24%;
				padding: 0.1rem 0.05rem;
				background: rgba(94, 213, 168, 0.06);
				border-radius: 0.05rem;
				border: 0.01rem solid rgba(255, 255, 255, 0.2);
				font-weight: 500;
				font-size: 0.12rem;
				color: #ffffff;
				height: 0.8rem;
				text-align: center;
				display: flex;
				flex-direction: column;
				justify-content: space-between;

				&:last-child {
					opacity: 0;
				}
			}
		}

		.quit {
			margin-right: 0.1rem;
			background: #fe0000;
			border-radius: 0.05rem;
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			font-size: 0.14rem;
			color: #171d22;
			padding: 0.05rem 0.25rem;
		}

		input::-webkit-input-placeholder,
		textarea::-webkit-input-placeholder {
			color: #777;
			font-size: 0.14rem;
		}
	}
</style>