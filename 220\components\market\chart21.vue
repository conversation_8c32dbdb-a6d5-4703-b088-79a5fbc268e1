<template>
	<div class="wrapper" style="width: 100%; padding: 0 4rpx; box-sizing: border-box">
		<div class="panel-box">
			<div class="panel-title">
				<ul class="chart-title">
					<li class="" v-for="(item, idx) in ktype" v-bind:key="idx" @click="changeType(idx)"
						:class="type == idx ? 'on li-type' : 'li-type'">
						{{ item }}
						<span></span>
					</li>
				</ul>
			</div>
			<div class="panel-body">
				<div class="chart-box" :style="'height:' + windowHeight + 'upx;'" ref="myEchart2"></div>
			</div>
		</div>
		<div class="table">
			<div class="panel"></div>
		</div>
	</div>
</template>

<script>
	import {
		init,
		dispose
	} from "klinecharts";

	import {
		getThemeOptions,
		getTooltipOptions,
		api
	} from "./chartStyle";

	var myChart = null;
	var timeChart = null;


	const dataSize = 150;
	export default {
		components: {},
		props: {
			code: {
				type: String,
				default () {},
			},
			stype: {
				type: String,
				default () {},
			},
			detail: {
				type: Object,
				default () {},
			},
		},
		data() {
			return {
				windowHeight: 150, //实时屏幕高度
				emptyShow: true,
				ChartsTime: [], // 分时图时间
				ChartstFuturesTime: [], // 分时图时间
				timer: null,
				isOptionOpt: false, // 是否自选
				type: 0, // 类型
				styleName: "red",
				ktype: [this.$t('sharesDetails').txt15, this.$t('sharesDetails').txt19, this.$t('sharesDetails').txt20,
					this.$t('sharesDetails').txt21
				],
				ktypes: ["1", "1D", "1W", "1M"],

				// 分时数据
				data: {
					stockCode: "",
					amounts: [],
					price: [],
					rates: [],
					time: [],
					volumes: [],
				},
				data2: {
					// 5分钟及以上的数据
					date: [],
					stockCode: "",
					stockName: "",
					values: [],
					volumes: [],
				},
				k_top_closing: 99999999999,
			};
		},
		watch: {
			code(newVal, oldVal) {
				if (newVal !== oldVal) {
					this.getDate2(this.type);
					if (this.$store.state.haslogin) {
						this.getOpation();
					}
				}
			},
		},
		computed: {

		},
		created() {
			this.changeType(this.type);
			this.timer = setInterval(() => {
				this.getDate2();
			}, 5000);
		},
		beforeDestroy() {
			myChart = null;
			timeChart = null;
			clearInterval(this.timer);
			dispose(this.$refs.myEchart2);
			dispose(this.$refs.myEchart);
		},
		mounted() {

			if (this.$store.state.haslogin) {
				this.getOpation();
			}
		},
		methods: {
			changeType(val) {
				let _this = this
				clearInterval(this.timer);
				setTimeout(() => {
					if (myChart) {
						myChart = null;
						dispose(this.$refs.myEchart2);
					}
					_this.getDate2(val);
				}, 50);
				this.type = val;
			},
			handleCommand(val) {
				// 选择分钟
				this.type = 2;
				clearInterval(this.timer);
				this.getDate2(val);
			},
			async getDate2(val, time) {
				let startTime = time || (new Date().getTime() * 0.001) | 0;

				// 获取分时段数据
				let opts = {
					stype: this.stype,
					code: this.code,
					time: val,
					type: this.ktypes[this.type],
					ma: 5,
					size: dataSize,
					startTime,
				};
				//console.log(opts)
				let data = await api.getMinKEcharts(this, opts);
				if (data.status === 0) {
					this.initEchartMap2(data.data, time);
				} else {
					Toast(this.$t('api')[data.msg]);
				}
			},

			async getDate() {
				// 获取分时数据
				let opts = {
					stockCode: this.code,
					type: this.type,
					state: 0,
					stockSpell: "",
					pageNum: 1,
					pageSize: 10,
				};
				if (opts.stockCode === "" || opts.stockCode === null) {
					return;
				}
				let data = await api.getMinuteLine(this, opts);
				if (data.status === 0) {
					this.emptyShow = false;
					this.initEchartMap(data.data);
				} else {
					this.emptyShow = true;
					this.$message.success(this.$t('api')[data.msg]);
				}
			},

			// 分时端图
			initEchartMap2(data, time) {
				let this_ = this;

				if (myChart == null) {
					myChart = init(this.$refs.myEchart2); //createChart(this.$refs.myEchart2);
					myChart.setStyleOptions(getThemeOptions("dark"));
					myChart.createTechnicalIndicator("VOL", true, {
						height: 50
					});
					myChart.createTechnicalIndicator("MA", true, {
						id: "candle_pane"
					});
					// myChart.createTechnicalIndicator("KDJ", true, { height: 80 });
					myChart.setStyleOptions(
						getTooltipOptions("standard", "always", "always")
					);

					myChart.loadMore((timestamp) => {
						//   this.getDate2(this.type, (timestamp * 0.001) | 0);
						//   setTimeout(() => {
						//     const firstData = myChart.getDataList()[0];
						//     console.log('时间',timestamp ,new Date(timestamp).toString())
						//     myChart.applyMoreData(
						//       generatedKLineDataList(timestamp, firstData.close, 2),
						//       true
						//     );
						//   }, 2000);
					});
				}
				myChart.applyNewData(data);
			},
			async getOpation() {
				// 获取是不是已添加自选
				let opts = {
					code: this.code,
				};
				let data = await api.isOption(this, opts);
				if (data.status === 0) {
					// 0 --> 未添加
					this.isOptionOpt = false;
				} else {
					this.isOptionOpt = true;
				}
			},
			async addOptions() {
				let data = await api.addOption(this, {
					from: 2,
					code: this.code,
				});
				if (data.status === 0) {
					this.$message.success(this.$t('sharesDetails').txt22);
					this.isOptionOpt = true;
				} else {
					this.$message.error(this.$t('api')[data.msg]);
				}
			},
			async deteleOptions() {
				let data = await api.delOption(this, {
					code: this.code,
				});
				if (data.status === 0) {
					this.$message.success(this.$t('sharesDetails').txt23);
					this.isOptionOpt = false;
				} else {
					this.$message.error(this.$t('api')[data.msg]);
				}
			},

			initEchartMap(data) {
				// if(timeChart==null){
				timeChart = init(this.$refs.myEchart); //createChart(this.$refs.myEchart2);
				timeChart.setStyleOptions(getThemeOptions("dark"));
				timeChart.setStyleOptions({
					candle: {
						type: "area"
					},
				});
				// }
				if (this.type == 0) {
					timeChart.applyNewData(data);
				}
			},
		},
	};
</script>
<style lang="less" scoped>
	ul {
		padding-left: 0 !important;
	}

	.panel-box {
		.panel-title {
			margin-bottom: .06rem;
		}

		.panel-body {
			position: relative;

			.chart-box {
				height: 2.5rem;
			}
		}
	}

	.chart-title {
		margin: 0.1rem 0.12rem;
		overflow: scroll;
		white-space: nowrap;
		background: #424E4E;
		border-radius: 0.1rem;
		padding: 0.05rem;
		.li-type {
      width: 25%;
			height: 0.3rem;
			list-style: none;
			display: inline-flex;
			align-items: center;
			justify-content: center;
			padding: 0 0.15rem;
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			font-size: 0.14rem;
			color: #B3B8B8;
      box-sizing: border-box;
			&:last-child{
				margin-right: none;
			}
			&.on {
				font-weight: 600;
				color: #000000;
				background: #5ED5A8;
				border-radius: 0.1rem;
			}
		}

		.chart-type {
			color: #999999;
			font-size: 1rem;
			padding: 7px 2px;
			font-weight: 600;
			text-decoration: none;
			text-align: center;
		}
	}

	.detail-box {
		height: 40px;
		line-height: 40px;

		.name {
			color: #efbb53;
			font-size: 16px;
			margin-left: 8px;
		}

		.code {
			color: #9e9e9e;
			margin-left: 6px;
		}
	}

	.empty {
		padding-top: 100px;
		color: #5a657e;
		font-size: 1.2rem;

		.iconfont {
			display: block;
			font-size: 120px;
			color: #5a657e;
			margin-bottom: 20px;
		}
	}
</style>