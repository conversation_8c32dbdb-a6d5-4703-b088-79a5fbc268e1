<template>
	<div class="page">
		<top-back title="信用融資"></top-back>
		<div class="nav-box">
			<div class="nav-item" v-for="(item, idx) in navList" :key="idx" :class="{ active: navIdx === item.type }"
				@click="changeNav(item.type)">
				{{ $t(item.name) }}
			</div>
		</div>
		<div class="box">
			<div class="flex flex-b top">
				<div class="t flex flex-c">
					<img src="../../assets/v5/loant1.png" style="width: 0.22rem;height: 0.22rem;" alt="" />
					按天算利息</div>
				<div class="t flex flex-c">
					<img src="../../assets/v5/loant2.png" style="width: 0.22rem;height: 0.22rem;" alt="" />
					隨時可還款</div>
				<div class="t flex flex-c">
					<img src="../../assets/v5/loant3.png" style="width: 0.22rem;height: 0.22rem;" alt="" />
					最快秒到帳</div>
			</div>
			<div class="box-head">
				<div class="box-h-l flex-column-item">
					<div class="box-h-v">{{ userInfo.xyf }}</div>
					<div class="box-h-n">信用分</div>
				</div>
			</div>
			<div class="box-row flex flex-b">
				<div class="box-col flex-column-item">
					<div class="box-r-n">可貸金額</div>
					<div class="box-r-v">{{ $formatMoney(userInfo.zarkjed, 0) }}</div>
				</div>
				<div class="box-col flex-column-item">
					<div class="box-r-n">已貸金額</div>
					<div class="box-r-v">{{ $formatMoney(userInfo.zaryjed, 0) }}</div>
				</div>
			</div>
			<div class="box-h-r flex flex-c" @click="$toPage('/home/<USER>')">
				<span>借貸記錄</span>
			</div>
			<div class="inputs" v-if='navIdx==0'>
				<div class="inputs-i">
					<div>借貸金額</div>
					<input v-model="getNum" @input='inputChange' placeholder="請輸入借貸金額" />
				</div>
				<div class="inputs-butt" @click="getK">確認</div>
			</div>
			<div class="inputs"  v-if='navIdx==1'>
				<div class="inputs-i">
					<div>還款金額</div>
					<input v-model="postNum" @input='inputChange1' placeholder="請輸入還款金額" />
				</div>
				<div class="inputs-butt inputs-butt2" @click="postK">還款</div>
			</div>
		</div>
		<loanRe v-if="navIdx==2"></loanRe>
	</div>
</template>

<script>
	import loanRe from './loanRe.vue'
	export default {
		data() {
			return {
				navList: [{
						name: '借貸',
						type: 0
					},
					{
						name: '還款',
						type: 1
					},
					// {
					// 	name:'借貸記錄',
					// 	type:2
					// }
				],
				navIdx: 0,
				userInfo: {},
				getNum: null,
				postNum: null
			};
		},
		components: {
			loanRe
		},
		created() {
			this.getUserInfo();
		},
		methods: {
			inputChange(e) {
				this.getNum = e.target.value
			},
			inputChange1(e) {
				this.postNum = e.target.value
			},
			changeNav(index) {
				this.navIdx = index;
				this.getNum = null;
				this.postNum = null;
			},
			getUserInfo() {
				this.$server.post("/user/getUserinfo", {}).then((res) => {
					if (res.status == 1) {
						this.userInfo = res.data;
					}
				});
			},
			getK() {
				if (!this.getNum) {
					this.$toast('請輸入借貸金額');
					return;
				}
				this.$server
					.post("/user/to_jq", {
						type: 'tzar',
						money: this.getNum
					})
					.then((res) => {
						if (res.status == 1) {
							this.$toast('借款成功');
						} else {
							this.$toast(this.$t(res.msg));
						}
					});
			},
			async getConfig() {
				const res = await this.$server.post("/hanguo/config");
				let val = {};
				res.data.forEach((vo) => {
					val[vo.name] = vo.value;
				});
				this.$openUrl(val.kefu); //重新获取
			},
			postK() {
				if (!this.postNum) {
					this.$toast('請輸入還款金額');
					return;
				}
				this.$server
					.post("/user/to_hq", {
						type: 'zar',
						money: this.postNum
					})
					.then((res) => {
						if (res.status == 1) {
							this.$toast('還款成功');
						} else {
							this.$toast(this.$t(res.msg));
						}
					});
			}
		}
	};
</script>

<style scoped lang="less">
	.page {
		min-height: 100vh;
		padding-top: 0.5rem;
	}
	.nav-box {
		display: flex;
		justify-content: center;
		align-items: center;
		background: #FFFFFF;
		padding: 0.02rem;
		border-radius: 0.04rem 0.04rem 0.04rem 0.04rem;
		margin: 0 0.6rem;
		.nav-item {
			flex: 1;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 0.14rem;
			color: #64726A;
			line-height: 0.36rem;
			text-align: center;
			position: relative;
		}
	
		.active {
			height: 0.36rem;
			background: linear-gradient( 90deg, #1B167A 0%, #4383C7 100%);
			border-radius: 0.04rem 0.04rem 0.04rem 0.04rem;	
			color: #FFFFFF;
			line-height: 0.36rem;
			position: relative;
			// &::after {
			// 	position: absolute;
			// 	content: '';
			// 	bottom: 0;
			// 	left: 50%;
			// 	transform: translateX(-50%);
			// 	width: 50%;
			// 	height: 0.02rem;
			// 	background-color: #E5C79F;
			// }
		}
	}
	.box {
		margin: 0.12rem;
		background: #FFFFFF;
		box-shadow: 0rem 0.02rem 0.02rem 0rem rgba(125,187,179,0.12), 0rem -0.02rem 0.02rem 0rem rgba(126,187,180,0.12);
		border-radius: 0.04rem 0.04rem 0.04rem 0.04rem;
		padding: 0.12rem;
		padding-bottom: 0.2rem;
		.top {
			.t {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 0.12rem;
				color: #6E6E6E;
				&:last-child{
					border-right: none;
				}
			}
		}
		.box-head {
			width: 100%;
			margin-top: 0.3rem;
			.box-h-l {
				.box-h-n {
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 0.12rem;
					color: #878787;
					margin-top: 0.1rem;
				}

				.box-h-v {
					font-family: DIN Next LT Pro, DIN Next LT Pro;
					font-weight: 500;
					font-size: 0.28rem;
					color: #000000;
				}
			}
		}

		.box-row {
			margin-top: 0.2rem;
			.box-col {
				flex: 1;
				.box-r-n {
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 0.11rem;
					color: #888888;
				}

				.box-r-v {
					padding-top: .1rem;
					font-family: DIN Next LT Pro, DIN Next LT Pro;
					font-weight: 500;
					font-size: 0.2rem;
					color: #2D2D2D;
				}
			}
		}

		.box-h-r {
			margin: 0.12rem 0;
			height: 0.42rem;
			background: linear-gradient( 90deg, #1B167A 0%, #4383C7 100%);
			border-radius: 0.04rem 0.04rem 0.04rem 0.04rem;
			font-family: PingFangSC, PingFang SC;
			font-weight: 600;
			font-size: 0.16rem;
			color: #FFFFFF;
			line-height: 0.42rem;

			image {
				width: .35rem;
				height: .35rem;
			}

			span {
				font-size: .14rem;
				color: #fff;
			}
		}
	}

	.inputs {
		margin: 0.15rem 0;
		.inputs-i {
			width: 100%;
			div {
				font-family: PingFang TC, PingFang TC;
				font-weight: 500;
				font-size: 0.14rem;
				color: #000000;
			}
			input {
				width: 100%;
				margin-top: 0.1rem;
				padding: 0 .12rem;
				height: 0.48rem;
				background: #EFF0F4;
				border-radius: 0.04rem 0.04rem 0.04rem 0.04rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.16rem;
				color: #000;
			}
		}

		.inputs-butt {
			margin-top: .3rem;
			width: 100%;
			height: 0.44rem;
			background: linear-gradient( 90deg, #1B167A 0%, #4383C7 100%);
			border-radius: 0.04rem 0.04rem 0.04rem 0.04rem;
			line-height: .44rem;
			text-align: center;
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			font-size: 0.16rem;
			color: #FFFFFF;
			animation: fadeIn;
			animation-duration: 2s;
		}
		.inputs-butt2{
			background: linear-gradient( 90deg, #F74F28 0%, #FF7757 98%);
		}
	}

	

	::v-deep .uni-input-input {
		font-size: .28rem;
		font-weight: 400;
		color: #333;
	}
</style>