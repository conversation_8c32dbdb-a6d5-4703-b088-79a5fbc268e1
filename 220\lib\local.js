let dict;

let lang = window.localStorage.getItem("lang") || 'jp';
if(window.location.href.indexOf('lang')>-1){
	var arr = window.location.href.split('lang=');
	if(arr[1]=='en'||arr[1]=='tw'||arr[1]=='jp'){
		lang = arr[1]
	}
}
let didnot = [];
export const langList = [{
		name: 'English',
		lang: 'en'
	},
	{
		name: '中文繁體',
		lang: 'tw'
	},
	{
		name: '日本語',
		lang: 'jp'
	}
]

export function setLang(la) {
	lang = la;
	if (la == 'en') {
		dict = require("./lang/en.json")
	} else if (la == 'tw') {
		dict = require("./lang/tw.json")
	} else if (la == 'jp') {
		dict = require("./lang/jp.json")
	} else {
		dict = {}
	}
	window.localStorage.setItem("lang", la);
}


export const getKey = (key, v2) => {
	function containsChinese(str) {
		const regex = /[\u4e00-\u9fff]/;
		return regex.test(str);
	}
	if (!key) {
		if (!v2) {
			// throw new Error("错误的key")
		}
		return '';
	}

	if (key.indexOf(".") > -1) {
		let arr = key.split(".");
		let inc = 0;
		let obj = null;
		while (inc < arr.length) {
			obj = obj == null ? dict[arr[inc]] : obj[arr[inc]];
			if (obj == null) {
				return key;
			}
			inc++;
		}
		key = obj; //中文 
	}
	if (dict[key]) {
		return dict[key]
	}
	didnot = []
	if (didnot.indexOf(key) == -1) {
		didnot.push(key);
	}
	if(containsChinese(key)){
		if(localStorage.getItem('lang') == 'en'){
			key = "Error"
		}
	}
	return key;
}

setLang(lang);

export function getLang() {
	return lang;
}

export function getLangName() {
	for (let i in langList) {
		if (lang == langList[i].lang) {
			return langList[i].name;
		}
	}
	return ''
}

export function initLang(Vue) {
	Vue.prototype.$t = getKey;
}