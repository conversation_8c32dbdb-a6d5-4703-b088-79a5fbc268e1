<template>
	<div class="skinDark">
<!--		<div class="logo text-center">-->
<!--			<img src="../../assets/v2/logo.png" />-->
<!--		</div>-->
		<!-- <div class="logoBox flex flex-c">
			<img class="img1" src="../../assets/skin/start/img1.png" />
		</div> -->
		
		<div class="foot text-center">
			<div class="foot-bottom">
				<div class="foot-tip">{{ initTxt }}...</div>
				<img class="" src="../../assets/v2/jzz.png" style="width: 0.5rem;height: 0.5rem;margin-top: 0.3rem;" />
				<div class="indicatoe-box">
					<div class="indicator-active"></div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
	export default {
		data() {
			return {
				initTxt: '',
				logo: '../../assets/skin/logo.png',
				appName: ''
			};
		},
		mounted() {
			this.setInt();
			this.getConfig();
		},
		methods: {
			getConfig() {
				this.$server.post("/common/config", {type:'all'}).then(res => {
					if (parseInt(res.status) === 200) {
						let list = res.data.data;
						let listLength = list.length;
						let a;
						for (a = 0; a < listLength; a++) {
							if (list[a].name === 'logo') {

							}
						}
					}
				});
			},
			setInt() {
				let _this = this;
				let arr = [_this.$t('start').txt1, _this.$t('start').txt2, _this.$t('start').txt3, _this.$t('start').txt4,
					_this.$t('start').txt5, _this.$t('start').txt6
				];
				let a = 0;
				_this.initTxt = arr[0];
				let int = setInterval(function() {
					a += 1;
					_this.initTxt = arr[a];
					if (a === 5) {
						clearInterval(int);
						setTimeout(() => {
							_this.startTime();
						}, 2500);
					}
				}, 500);
			},
			startTime() {
				let token = window.localStorage.getItem("tokend") || false;
				if (token) {
					this.$router.push({
						path: "/home/<USER>",
					});
				} else {
					this.$router.push({
						path: "/login/logon2",
					});
				}
			}
		}
	};
</script>

<style scoped lang="less">
	@keyframes logo1 {
		0% {
			transform: scale(1);
		}

		50% {
			transform: scale(1.5);
		}

		100% {
			transform: scale(1);
		}
	}

	@keyframes logo {
		0% {
			top: 0rpx;
		}

		50% {
			top: -50rpx;
		}

		80% {
			top: -20rpx;
		}

		100% {
			top: 0rpx;
		}
	}

	@keyframes circle {
		0% {
			transform: rotate(0deg);
		}

		100% {
			transform: rotate(360deg);
		}
	}

	@keyframes title {
		0% {
			opacity: 1;
		}

		50% {
			opacity: 0;
		}

		100% {
			opacity: 1;
		}
	}

	@keyframes more {
		0% {
			width: 0%;
		}

		100% {
			width: 100%;
		}
	}

	.skinDark {
		// background: #fff;
		-webkit-transition-duration: 0.5s;
		-moz-transition-duration: 0.5s;
		-o-transition-duration: 0.5s;
		min-height: 100vh;
		background: url('../../assets/start12.png?t=1') no-repeat center;
    background-size: cover;
		position: relative;

		.logo {
			padding-top: 1.2rem; //240rpx;

			img {
				width: 1rem;
				height: 1rem;
				display: block;
				margin: 0 auto;
				animation: logo1 2s linear infinite;
			}
		}

		.logoBox {
			width: 2.23rem;
			height: 2.15rem;
			margin: .67rem auto .25rem auto;
			position: relative;

			img {
				width: 1.62rem;
				height: 1.63rem;
				display: block;
			}

			.img1 {
				animation: title 2s linear infinite;
			}
		}

		.foot {
			width: 100%;
			//padding-bottom: .4rem;
			position: absolute;
			bottom: 0;
			font-size: .12rem;
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			color: #212121;

			.foot-bottom {
				margin: .25rem 0;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				font-size: .13rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				color: #333333;

				img {
					opacity: 0.5;
					width: .68rem;
					height: .68rem;
					display: block;
					margin: 0 auto;
					animation: circle 4s linear infinite;
				}

				.foot-tip {
					font-weight: 400;
					font-size: 0.13rem;
					color: #333;
					margin-top: .1rem;
				}

				.indicatoe-box {
					width: calc(100% - .4rem);
					background: #F5F5F5;
					height: .08rem;
					border-radius: .08rem;
					margin-top: .26rem;
					position: relative;

					.indicator-active {
						height: .08rem;
						left: 0;
						top: 0;
						background: #5ED5A8;
						border-radius: .1rem;
						animation: more 4s linear;
					}
				}
			}
		}
	}
</style>