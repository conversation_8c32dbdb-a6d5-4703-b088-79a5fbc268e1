@charset "utf-8";
html,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
font,
img,
input,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td {
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0;
  vertical-align: baseline;
  background: transparent;
}

// @font-face {
// font-family: 'PingFang-TC-Medium';
// src: url('./PingFang-TC-Medium.otf');
// }

body {
  min-width: 100vw;
  min-height: 100vh;
  margin: 0;
  padding: 0;
  color: #000000;
}

* {
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0;
  box-sizing: border-box;
  font-size: 0.14rem;
  font-weight: 500;
  // font-family: PingFang TC, PingFang TC;
  font-family: PingFang SC, PingFang SC;
}

.van-dialog__header {
  color: #000;
}

// 这个会影响下拉刷新的组件的显示
.van-pull-refresh {
  overflow: unset !important;
}

.flex {
  display: flex;
  align-items: center;
  &.flex-b {
    justify-content: space-between;
  }
  &.flex-c {
    justify-content: center;
  }
  &.flex-e {
    justify-content: flex-end;
  }
}

.flex-1 {
  flex: 1;
}
.flex-2 {
  flex: 2;
}
.flex-3 {
  flex: 3;
}

.t-center,
.t-c {
  text-align: center;
}
.t-right,
.t-r {
  text-align: right;
}
.no-data {
  text-align: center;
  margin: 1rem 0;
}

.top-pad {
  padding-top: 0.6rem;
}

.b-btn {
  height: 0.5rem;
  text-align: center;
  background: linear-gradient(90deg, #98EF86, #C7F377);
  border-radius: 0.25rem;
  line-height: 0.5rem;
  text-align: center;
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 0.16rem;
  color: #000000;
  margin-top: 0.3rem;
}

.ellipsis {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.icon {
	&.menu {
	  width: 0.32rem;
	  height:0.32rem;
	  background: url("../v5/menu.png") no-repeat center/100%;
	}
	&.arrowl {
	  width: 0.14rem;
	  height:0.13rem;
	  background: url("../v4/arrowl.png") no-repeat center/100%;
	}
  &.bg {
    width: 100%;
    height:1.65rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.zy1 {
    width: 0.14rem;
    height: 0.14rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.by {
    width: 0.14rem;
    height: 0.14rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.jl {
    width: 0.22rem;
    height: 0.22rem;
    background: url('../v5/jl.png') no-repeat center/100%;
  }
  &.ss {
    width: 0.25rem;
    height: 0.25rem;
    background: url('../v5/ss.png') no-repeat center/100%;
  }
  &.addzx {
    width: 0.18rem;
    height: 0.18rem;
    background: url("../v6/addzx.png") no-repeat center/100%;
  }
  &.cutzx {
    width: 0.18rem;
    height: 0.18rem;
    background: url("../v6/cutzx.png") no-repeat center/100%;
  }
  &.cz {
    width: 0.2rem;
    height: 0.2rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.tx {
    width: 0.3rem !important;
    height: 0.3rem !important;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.zj {
    width: 0.2rem;
    height: 0.2rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.sx {
    width: 0.18rem;
    height: 0.18rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.bageye {
    width: 0.16rem;
    height: 0.16rem;
    background: url("../v5/bageye.png") no-repeat center/100%;
  }
  &.bagby {
    width: 0.2rem;
    height: 0.2rem;
    background: url("../v5/bagby.png") no-repeat center/100%;
  }
  &.lines {
    width: 0.91rem;
    height: 0.61rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.jzxl {
    width: 0.18rem;
    height: 0.18rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.zxl {
    width: 0.18rem;
    height: 0.18rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.sczx {
    width: 0.2rem;
    height: 0.2rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.wxz {
    width: 0.16rem;
    height: 0.16rem;
    background: url("../v6/wgx.png") no-repeat center/100%;
  }
  &.xz {
    width: 0.16rem;
    height: 0.16rem;
    background: url("../v6/ygx.png") no-repeat center/100%;
  }
  &.gb {
    width: 0.3rem;
    height:0.3rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.yxz {
    width: 0.14rem;
    height: 0.14rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.zd {
    width: 0.16rem;
    height: 0.16rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.jzx {
    width: 0.14rem;
    height: 0.14rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.bjzx {
    width: 0.14rem;
    height: 0.14rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.zq {
    width: 0.08rem;
    height: 0.1rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.dq {
    width: 0.08rem;
    height: 0.1rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.dq {
    width: 0.08rem;
    height: 0.1rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.gm {
    width: 50%;
    height: 0.25rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }

  &.zy {
    width: 0.24rem;
    height: 0.22rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.rjt {
    width: 0.12rem;
    height: 0.12rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.close {
    width: 0.3rem;
    height: 0.3rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.m1 {
    width: 0.34rem;
    height: 0.34rem;
    background: url("../v4/m1.png") no-repeat center/100%;
  }
  &.m2 {
   width: 0.34rem;
   height: 0.34rem;
    background: url("../v4/m2.png") no-repeat center/100%;
  }
  &.tongzhi{
    width: 0.26rem;
    height: 0.26rem;
    background: url("../v2/tongzhi.png") no-repeat center/100%;
  }
  &.m3 {
    width: 0.34rem;
    height: 0.34rem;
    background: url("../v4/m3.png") no-repeat center/100%;
  }
  &.m4 {
    width: 0.34rem;
    height: 0.34rem;
    background: url("../v4/m4.png") no-repeat center/100%;
  }
  &.m5 {
    width: 0.34rem;
    height: 0.34rem;
    background: url("../v4/m5.png") no-repeat center/100%;
  }
  &.m6 {
    width: 0.26rem;
    height: 0.26rem;
    background: url("../v2/gywm.png") no-repeat center/100%;
  }
  &.m7 {
    width: 0.26rem;
    height: 0.26rem;
    background: url("../v2/lxkf.png") no-repeat center/100%;
  }
  &.ring {
    width: 0.17rem;
    height: 0.17rem;
    background: url(../v6/ring.png) no-repeat center/100%;
  }
  &.jtr {
    width: 0.14rem;
    height: 0.14rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.tz {
    width: 0.24rem;
    height: 0.24rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.tz1 {
    width: 0.24rem;
    height: 0.24rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.kf1 {
    width: 0.26rem;
    height: 0.26rem;
    background: url("../v5/kf1.png") no-repeat center/100%;
  }
  &.zk {
    width: 0.2rem;
    height: 0.2rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.mby {
    width: 0.18rem;
    height: 0.15rem;
    background: url("../v6/by.png") no-repeat center/100%;
  }
  &.mzy {
    width: 0.18rem;
    height: 0.15rem;
    background: url("../v6/zy.png") no-repeat center/100%;
  }
  &.xzl {
    width: 0.22rem;
    height: 0.22rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.h1 {
    width: .34rem;
    height: .34rem;
    background: url("../v6/h1.png") no-repeat center/100%;
  }
  &.h2 {
    width: .34rem;
    height: .34rem;
    background: url("../v6/h2.png") no-repeat center/100%;
  }
  &.h3 {
    width: .34rem;
    height: .34rem;
    background: url("../v6/h3.png") no-repeat center/100%;
  }
  &.h4 {
    width: .34rem;
    height: .34rem;
    background: url("../v6/h4.png") no-repeat center/100%;
  }
  &.h5 {
    width: .34rem;
    height: .34rem;
    background: url("../v6/h5.png") no-repeat center/100%;
  }
  &.h6 {
    width: .34rem;
    height: .34rem;
    background: url("../v6/h6.png") no-repeat center/100%;
  }
  &.h61 {
    width: 0.26rem;
    height: 0.26rem;
    background: url("../v2/zczl.png") no-repeat center/100%;
  }
  &.h7 {
   width: .34rem;
   height: .34rem;
    background: url("../v6/h7.png") no-repeat center/100%;
  }
  &.h8 {
   width: .34rem;
   height: .34rem;
    background: url("../v6/h8.png") no-repeat center/100%;
  }
  &.h9 {
    width: 0.3rem;
    height: 0.3rem;
    background: url("../v5/h9.png") no-repeat center/100%;
  }
  &.h10 {
    width: 0.3rem;
    height: 0.3rem;
    background: url("../v5/h10.png") no-repeat center/100%;
  }
  &.h11 {
    width: 0.26rem;
    height: 0.26rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.h12 {
    width: 0.26rem;
    height: 0.26rem;
    background: url("../v2/zjls.png") no-repeat center/100%;
  }
  &.h13 {
    width: 0.26rem;
    height: 0.26rem;
    background: url("../v2/sygn.png") no-repeat center/100%;
  }
  &.h14 {
    width: 0.46rem;
    height: 0.46rem;
    background: url("../v5/h14.png") no-repeat center/100%;
  }
  &.h15 {
    width: 0.46rem;
    height: 0.46rem;
    background: url("../v5/h15.png") no-repeat center/100%;
  }
  &.h16 {
    width: 0.46rem;
    height: 0.46rem;
    background: url("../v5/h16.png") no-repeat center/100%;
  }
  &.h17 {
    width: 0.46rem;
    height: 0.46rem;
    background: url("../v5/h17.png") no-repeat center/100%;
  }
  &.ico1{
	  width: 0.25rem;
	  height: 0.25rem;
	  background: url("../v6/ico1.png") no-repeat center/100%;
  }
  &.ico2{
  	  width: 0.25rem;
  	  height: 0.25rem;
  	  background: url("../v6/ico2.png") no-repeat center/100%;
  }
  &.ico3{
  	  width: 0.25rem;
  	  height: 0.25rem;
  	  background: url("../v6/ico3.png") no-repeat center/100%;
  }
  &.ico4{
  	  width: 0.25rem;
  	  height: 0.25rem;
  	  background: url("../v6/ico4.png") no-repeat center/100%;
  }
  &.ico5{
  	  width: 0.22rem;
  	  height: 0.22rem;
  	  background: url("../v6/ico5.png") no-repeat center/100%;
  }
  &.ico6{
  	  width: 0.22rem;
  	  height: 0.22rem;
  	  background: url("../v6/ico6.png") no-repeat center/100%;
  }
  &.ico7{
  	  width: 0.22rem;
  	  height: 0.22rem;
  	  background: url("../v6/ico7.png") no-repeat center/100%;
  }
  &.ico8{
  	  width: 0.22rem;
  	  height: 0.22rem;
  	  background: url("../v6/ico8.png") no-repeat center/100%;
  }
  &.ico9{
  	  width: 0.22rem;
  	  height: 0.22rem;
  	  background: url("../v6/ico9.png") no-repeat center/100%;
  }
  &.ico10{
  	  width: 0.22rem;
  	  height: 0.22rem;
  	  background: url("../v6/ico10.png") no-repeat center/100%;
  }
  &.lang{
  	  width: 0.22rem;
  	  height: 0.22rem;
  	  background: url("../v6/lang.png") no-repeat center/100%;
  }
  &.zxadd {
    width: 0.16rem;
    height:0.16rem;
	margin-right: 0.1rem;
    background: url("../v5/zxadd.png") no-repeat center/100%;
  }

  &.bjbtn {
    width: 0.14rem;
    height: 0.14rem;
    background: url("../v5/set.png") no-repeat center/100%;
  }
  &.jyjl {
    width: 0.25rem;
    height: 0.25rem;
    background: url("../v5/jyjl.png") no-repeat center/100%;
  }
  &.zf {
    width: 0.16rem;
    height: 0.16rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.df {
    width: 0.16rem;
    height: 0.16rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }

  &.back {
    width: 0.16rem;
    height: 0.16rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.jt1 {
    width: 0.11rem;
    height: 0.07rem;
    background: url("../v5/jt1.png") no-repeat center/100%;
  }
  &.dj {
    width: 0.12rem;
    height: 0.12rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.jzz {
    width: 0.58rem;
    height: 0.58rem;
    background: url("../home/<USER>") no-repeat center/100%;
    margin: 0 auto;
  }
  &.user {
    width: 0.63rem;
    height: 0.63rem;
    background: url("../v5/user.png") no-repeat center/100%;
  }
  &.set {
    width: 0.26rem;
    height: 0.26rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }

  &.jt2 {
    width: 0.22rem;
    height: 0.22rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }

  &.checked {
    width: 0.18rem;
    height: 0.18rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.nocheck {
    width: 0.18rem;
    height: 0.18rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.sf1 {
    width: 0.36rem;
    height: 0.36rem;
    background: url("../v5/sf1.png") no-repeat center/100%;
  }
  &.sf2 {
    width: 1rem;
    height: 1rem;
    background: url("../v5/sf2.png") no-repeat center/100%;
  }
  &.add {
    width: 0.16rem;
    height: 0.16rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.jj {
    width: 0.18rem;
    height: 0.18rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.sou {
    width: 0.16rem;
    height: 0.16rem;
    background: url("../v6/sou.png") no-repeat center/100%;
    margin-right: 0.05rem;
  }
	&.hq {
	  width: 0.25rem;
	  height: 0.25rem;
	  background: url("../v5/hq.png") no-repeat center/100%;
	}
  &.copy {
    width: 0.14rem;
    height: 0.14rem;
    background: url("../v5/copy.png") no-repeat center/100%;
  }
  &.down {
    width: 0.1rem;
    height: 0.1rem;
    background: url("../v5/down.png") no-repeat center/100%;
  }
  &.up {
    width: 0.1rem;
    height: 0.1rem;
    background: url("../v5/up.png") no-repeat center/100%;
  }
  &.bk{
    width: 0.16rem;
    height: 0.16rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.sz{
    width: 0.16rem;
    height: 0.16rem;
    background: url("../v3/sz.png") no-repeat center/100%;
  }
  &.jt {
    width: 0.1rem;
    height: 0.1rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.xl {
    width: 0.14rem;
    height: 0.14rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.up1 {
    width: 0.14rem;
    height: 0.08rem;
    background: url("../v5/up1.png") no-repeat center/100%;
  }
  &.down1 {
    width: 0.14rem;
     height: 0.08rem;
	 transform: rotate(180deg);
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.sou2 {
    width: 0.2rem;
    height: 0.2rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.tm {
    width: 0.14rem;
    height: 0.14rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.s1 {
    width: 0.1rem;
    height: 0.1rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.x1 {
    width: 0.1rem;
    height: 0.1rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }

  &.ysc {
    width: 0.16rem;
    height: 0.16rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.gd0 {
    width: 1.26rem;
height: 1.6rem;
    background: url("../mjbg.png") no-repeat center/100%;
  }

  &.gd1 {
    width: 1.26rem;
height: 1.6rem;
    background: url("../mjbg.png") no-repeat center/100%;
  }
  &.gd2 {
    width: 1.26rem;
    height: 0.8rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.dd1 {
    width: 0.12rem;
    height: 0.12rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.zz1 {
    width: 0.12rem;
    height: 0.12rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.rns {
    width: 0.18rem;
    height: 0.18rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.kset {
    width: 0.18rem;
    height: 0.18rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }

  &.sou2 {
    width: 0.18rem;
    height: 0.18rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }

  &.usd {
    width: 0.34rem;
    height: 0.34rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.qh {
    width: 0.39rem;
    height: 0.39rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.thb {
    width: 0.34rem;
    height: 0.34rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }
  &.del {
    width: 0.12rem;
    height: 0.12rem;
    background: url("../v6/del.png") no-repeat center/100%;
  }

  &.back1 {
    width: 0.1rem;
    height: 0.18rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }

  &.sou1 {
    width: 0.16rem;
    height: 0.17rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }

  &.rz {
    width: 0.12rem;
    height: 0.14rem;
    background: url("../home/<USER>") no-repeat center/100%;
  }

  &.logo {
    width: 1.35rem;
    height: 0.35rem;
    // background: url("../home/<USER>") no-repeat center/100%;
    img {
      width: 100%;
      height: 100%;
    }
  }

  &.a {
    width: 0.25rem;
    height: 0.25rem;
    background: url("../v6/a.png") no-repeat center/100%;
  }
  &.ac {
    width: 0.25rem;
    height: 0.25rem;
    background: url("../v6/ac.png") no-repeat center/100%;
  }
  &.a1 {
    width: 0.25rem;
    height: 0.25rem;
    background: url("../v6/a1.png") no-repeat center/100%;
  }
  &.ac1 {
	width: 0.25rem;
	height: 0.25rem;
    background: url("../v6/ac1.png") no-repeat center/100%;
  }
  &.a2 {
    width: 0.25rem;
    height: 0.25rem;
    background: url("../v6/a2.png") no-repeat center/100%;
  }
  &.ac2 {
    width: 0.25rem;
    height: 0.25rem;
    background: url("../v6/ac2.png") no-repeat center/100%;
  }
  &.a3 {
    width: 0.25rem;
    height: 0.25rem;
    background: url("../v6/a3.png") no-repeat center/100%;
  }
  &.ac3 {
    width: 0.25rem;
    height: 0.25rem;
    background: url("../v6/ac3.png") no-repeat center/100%;
  }
  &.a4 {
    width: 0.25rem;
    height: 0.25rem;
    background: url("../v6/a4.png") no-repeat center/100%;
  }
  &.ac4 {
    width: 0.25rem;
    height: 0.25rem;
    background: url("../v6/ac4.png") no-repeat center/100%;
  }

  &.success {
    width: 0.97rem;
    height: 0.97rem;
    background: url("../login/sc.png") no-repeat center/100%;
  }
  &.up02 {
  	width: 0.09rem;
  	height: 0.12rem;
  	background: url('../v5/up02.png') no-repeat center/100%;
  }
  &.down02 {
  	width: 0.09rem;
  	height: 0.12rem;
  	background: url('../v5/up02.png') no-repeat center/100%;
	transform: rotate(180deg);
  }
  &.ddjl {
  	width: 0.17rem;
  	height: 0.2rem;
  	background: url('../v4/ddjl.png') no-repeat center/100%;
  }
  &.rzwc {
  	width: 0.99rem;
  	height: 1.96rem;
  	background: url('../v4/rzwc.png') no-repeat center/100%;
  }
  &.gpIcon{
	  width: 0.19rem;
	  height: 0.19rem;
	  background: url('../v5/gpIcon.png') no-repeat center/100%;
  }
  &.zxIcon{
  	  width: 0.19rem;
  	  height: 0.19rem;
  	  background: url('../v5/zxIcon.png') no-repeat center/100%;
  }
  &.redLine{
  	  width: 0.99rem;
  	  height: 0.67rem;
  	  background: url('../v6/redLine.png') no-repeat center/100%;
  }
  &.greenLine{
  	  width: 0.99rem;
  	  height: 0.67rem;
  	  background: url('../v6/greenLine.png') no-repeat center/100%;
  }
}
