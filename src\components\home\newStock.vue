<template>
	<!-- 新股申购 -->
	<div class="page">
		<div class="topFixed flex flex-c">
			<div class="title">新股申購</div>
		</div>
		<!-- <top-back title="新股申購"></top-back> -->
		<!-- <div class="icon jl" @click="goUrl('/home/<USER>')"></div> -->
		<!-- <div class="navs">
			<div class="nav-box flex flex-b">
				<div class="nav-item" v-for="(item, idx) in navList" :key="idx"
					:class="{ active: currmentIndex === item.type }" @click="changeNav(item.type)">
					{{ item.name }}
					<span></span>
				</div>
			</div>
		</div> -->
		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3" :loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<van-skeleton title :row="26" :loading="loading">
				<div class="cot" v-if="allList.length">
					<div class="item" v-for="(item, i) in allList" :key="i" @click="toDetail(item)">
						<div class="item-top flex flex-b">
							<div>
								<div class="flex">
									<div class="name">{{ item.name || "-" }}</div>
									<div class="code">{{ item.symbol || "-" }}</div>
								</div>
								<div class="time">截止日:{{ $formatDate("MM-DD", item.end * 1000) }}</div>
							</div>
							<div class="btn flex flex-c" v-if="item.isKsg==0">去申購</div>
							<div class="btn flex flex-c" v-if="item.isKsg==1">詳情</div>
							<div class="btn3 flex flex-c" v-if="item.isKsg==2">已結束</div>
							<!-- <div class="item-list" v-if="false">
								<div class="t3" :class="item.price - item.bprice < 0 ? 'green' : 'red'">
									{{ item.price - item.bprice > 0 ? "+" : ""}}{{((item.price - item.bprice) / item.bprice) * 100 == -100? "-": ( ((item.price - item.bprice) / item.bprice) * 100).toFixed(2) }}%
								</div>
								<div class="t4 t-r">溢價差</div>
							</div> -->
						</div>
						<!-- <div class="flex flex-b item-bot">
							<div class="item-list flex-column-item">
								<div class="t2 ">申購日</div>
								<div class="t3 flex flex-c">
									{{ $formatDate("MM-DD", item.start * 1000) }}
								</div>
							</div>
							<div class="line"></div>
							<div class="item-list flex-column-item">
								<div class="t2 ">截止日</div>
								<div class="t3 flex flex-c">{{ $formatDate("MM-DD", item.end * 1000) }}</div>
							</div>
							<div class="line"></div>
							<div class="item-list flex-column-item">
								<div class="t2">撥券日</div>
								<div class="t3 flex flex-c">{{ item.fq_date.substring(5,10) }}</div>
							</div>
						</div> -->
						<!-- <div class="jzrBox">
							<van-circle v-model="currentRate" :rate="30" :speed="100"  size="100px" layer-color="#ececec" stroke-width="80" color="#2a3e97" />
							<div class="jzr flex-column-item">
								<div class="t2 ">截止日</div>
								<div class="t3 flex flex-c">{{ $formatDate("MM-DD", item.end * 1000) }}</div>
							</div>
						</div> -->
						<div class="flex flex-b flex-wrap item-middle">
							<div class="item-list">
								<div class="t3">{{ $formatMoney(item.bprice) }}</div>
								<div class="t2">承銷價</div>
							</div>
							<div class="item-list">
								<div class="t3">{{ $formatMoney(item.num, 0) }}</div>
								<div class="t2">總申購</div>
							</div>
							<div class="item-list">
								<div class="t3" :class="item.price - item.bprice < 0 ? 'green' : 'red'">{{ item.price - item.bprice > 0 ? "+" : ""}}{{((item.price - item.bprice) / item.bprice) * 100 == -100? "-": ( ((item.price - item.bprice) / item.bprice) * 100).toFixed(2) }}%</div>
								<div class="t2">溢價差</div>
							</div>
							<div class="item-list">
								<div class="t3" :class="item.price - item.bprice < 0 ? 'green' : 'red'">{{ $formatMoney(item.price) }}
								<div class="t2">市價</div>
								</div>
							</div>
							<div class="item-list">
								<div class="t3">{{ $formatMoney(item.price - item.bprice) }}</div>
								<div class="t2">差價</div>
							</div>
							<!-- <div class="item-list flex flex-b">
								<div class="t2 ">申購期間</div>
								<div class="t3 ">{{ item.subdate }}</div>
							</div> -->
						</div>
					</div>
				</div>
				<no-data v-if="isShow"></no-data>
				<!-- <div v-if="currmentIndex==1">
					<newShareOrder></newShareOrder>
				</div> -->
			</van-skeleton>
		</van-pull-refresh>
		<tab-bar :current="5"></tab-bar>
		<!--点击按钮加载效果组件 -->
		<loading ref="loading" />
	</div>
</template>

<script>
	import newShareOrder from "./newShareOrder.vue";
	export default {
		name: "newStock",
		components: {
			newShareOrder
		},
		data() {
			return {
				loading: true,
				isShow: false,
				isLoading: false,
				currmentIndex: 0,
				// navList: [{
				// 		name: this.$t("新股申購"),
				// 		type: 0
				// 	},
				// 	{
				// 		name: this.$t("申購記錄"),
				// 		type: 1
				// 	},
				// ],
				navList: [{
						name: "待申購",
						type: 0,
					},
					{
						name: "申購中",
						type: 1,
					},
					{
						name: "申購結束",
						type: 2,
					},
				],
				allList: [],
				xinList: [],
				userInfo: {},
				quantity: "",
				currentRate: 10,
			};
		},
		mounted() {
			this.getList();
		},
		methods: {
			goUrl(item) {
				if (item.url == 'kefu') {
					this.getConfig()
				} else if (item.url == 'kefu1') {
					this.getConfig1()
				} else {
					this.$router.push({
						path: item
					})
				}
			},
			// 下拉刷新
			onRefresh() {
				this.isShow = false;
				this.getList();
			},
			changeNav(type) {
				this.currmentIndex = type;
				this.xinList = this.allList.filter((item) => item.isKsg == type);
			},
			// 获取列表
			getList() {
				this.$server.post("/trade/placinglist", {
						type: "twd",
						buy_type: 0,
					})
					.then((res) => {
						this.loading = false;
						this.isLoading = false;
						let now = new Date().getTime();
						let arr = [];
						res.data.forEach((item) => {
							// item.start = new Date().getTime() / 1000 + 50000;
							// item.end = new Date().getTime() / 1000 + 60000;
							// 可申购
							if (item.start * 1000 <= now && now <= item.end * 1000) {
								item.time = Math.floor(
									(item.end * 1000 - now) / 1000 / 60 / 60 / 24
								);
								item.isKsg = 1; //是否可申购
							} else if (now < item.start * 1000) {
								item.time = Math.floor(
									(item.start * 1000 - now) / 1000 / 60 / 60 / 24
								);
								// 待申购
								item.isKsg = 0;
							} else if (item.end * 1000 < now) {
								// 结束
								item.isKsg = 2;
							}
							arr.push(item);
						});
						this.allList = [...new Set(arr)];
						this.allList =this.allList.sort((a,b)=>{
							return b.end-a.end
						})
						// console.log(this.allList,9999)
						this.changeNav(1);
					});
			},
			toDetail(item) {
				this.$storage.save("itemTemp", item);
				this.$toPage(`/home/<USER>
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.5rem 0rem 0.6rem;
		min-height: 100vh;
		.topFixed{
			width: 100%;
			position: fixed;
			top: 0;
			left: 0;
			z-index: 999;
			padding: 0.12rem;
			.title{
				font-family: PingFang SC;
				font-weight: 500;
				font-size: 0.16rem;
				color: #FFFFFF;
			}
		}
	}
	.jl {
		position: fixed;
		z-index: 999;
		top: 0.14rem;
		z-index: 999;
		right: 0.12rem;
	}
	.navs {
		padding: 0 0.12rem;
		position: fixed;
		top: 0.5rem;
		left: 0;
		z-index: 999;
		width: 100%;
		.nav-box {
			height: 0.4rem;
			background: #FFFFFF;
			border-radius: 0.04rem 0.04rem 0.04rem 0.04rem;
			padding: 0.02rem;
			.nav-item {
				flex: 1;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 0.14rem;
				color: #6F6F6F;
				line-height: 0.36rem;
				text-align: center;
				position: relative;
		
				&.active {
					height: 0.36rem;
					background: linear-gradient(90deg, #1B167A 0%, #4383C7 100%);
					border-radius: 0.04rem 0.04rem 0.04rem 0.04rem;
					color: #fff;
					position: relative;
					// &::after{
					// 	position: absolute;
					// 	content: '';
					// 	bottom: 0;
					// 	left: 50%;
					// 	transform: translateX(-50%);
					// 	width: 50%;
					// 	height: 0.02rem;
					// 	background-color: #E5C79F;
					// }
				}
			}
		}
	}
	.cot {
		padding: 0.12rem;
		.item {
			background: #232429;
			border-radius: 0.13rem;
			margin-bottom: 0.15rem;
			padding: 0.12rem;
			.item-top {
				padding-bottom: 0.12rem;
				.name {
					font-family: PingFang SC;
					font-weight: 500;
					font-size: 0.15rem;
					color: #FFFFFF;
				}
				.code {
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 0.11rem;
					color: #999999;
					margin-left: 0.05rem;
				}
				.time{
					margin-top: 0.05rem;
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 0.11rem;
					color: #999999;
				}
				.t3 {
					font-size: 0.16rem;
				}

				.t4 {
					font-size: 0.12rem;
					color: #fff;
					margin-top: 0.05rem;
				}
			}
			.item-bot {
				margin-right: 0.1rem;
				.line {
					margin-top: 0.3rem;
					width: 1.2rem;
					height: 0.1rem;
					background: #0178D3;
				}

				.t3 {
					margin-top: 0.1rem;
					width: 0.64rem;
					height: 0.64rem;
					background: #FFFFFF;
					border: 0.03rem solid #0178D3;
					border-radius: 50%;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.14rem;
					color: #002F7C;
				}

				.t2 {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.14rem;
					color: #999999;
				}
			}
			.jzrBox{
				position: relative;
				margin-right: 0.1rem;
				.jzr{
					position: absolute;
					top: 50%;
					left: 50%;
					transform: translate(-50%,-50%);
					.t2{
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 0.12rem;
						color: #393939;
					}
					.t3{
						margin-top: 0.05rem;
						font-family: PingFang SC, PingFang SC;
						font-weight: 600;
						font-size: 0.16rem;
						color: #000000;
					}
				}
			}
			.item-middle {
				padding: 0.12rem;
				background: #434446;
				border-radius: 0.09rem;
				.item-list {
					text-align: center;
					line-height: 0.2rem;
					.t2 {
						font-family: PingFang SC;
						font-weight: 400;
						font-size: 0.11rem;
						color: #999999;
					}

					.t3 {
						font-family: PingFang SC;
						font-weight: 500;
						font-size: 0.14rem;
						color: #FFFFFF;
					}

					.green {
						color: #39B44C;
					}

					.red {
						color: #ba3b3a;
					}
				}
			}
			.btn {
				height: 0.27rem;
				background: #8EFE99;
				border-radius: 0.13rem;
				padding: 0 0.1rem;
				font-family: PingFang SC;
				font-weight: 500;
				font-size: 0.13rem;
				color: #000000;
				line-height: 0.27rem;
			}
			.btn3 {
				height: 0.27rem;
				background: #599161;
				border-radius: 0.13rem;
				padding: 0 0.1rem;
				font-family: PingFang SC;
				font-weight: 500;
				font-size: 0.13rem;
				color: #000000;
				line-height: 0.27rem;
			}
		}
	}

	.list {
		.item {
			padding: 40px 20px;
			border-bottom: 2px solid #cccccc;

			.mb20 {
				margin-bottom: 20px;
			}

			.name {
				font-size: 36px;
				font-weight: bold;
				color: #333333;
			}

			.code {
				padding: 10px 20px;
				background: #f0f3fa;
				border-radius: 10px;
				font-size: 24px;
				font-family: Roboto;
				font-weight: 400;
				color: #333333;
				text-align: center;
				margin-top: 10px;
			}

			.time {
				padding: 20px;
				background: #f7e8e8;
				border-radius: 20px;
				font-size: 32px;
				font-weight: bold;
				color: #ff3636;
			}
		}

		.per {
			width: 180px;
			height: 180px;
			border: 10px solid #d4e0eb;
			border-radius: 50%;
			overflow: hidden;
			padding: 40px 0 0;
			text-align: center;
			position: relative;
			margin-right: 20px;

			.t {
				// font-size: 44px;
				font-size: 36px;
				font-family: Roboto;
				font-weight: bold;
				color: #ff3636;
				line-height: 48px;
			}

			.t1 {
				width: 100%;
				color: #333333;
				background-color: #d4e0eb;
				position: absolute;
				padding: 5px 0;
				bottom: 0px;
				font-size: 24px;
			}
		}

		.b-btn {
			margin: 0;
			width: 45%;
			height: 60px;
			line-height: 60px;

			&.bt {
				background-color: #ccc;
			}
		}
	}
</style>