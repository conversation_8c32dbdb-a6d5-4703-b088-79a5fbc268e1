module.exports = {
  // 通用组件
  common: {
    confirm: 'Confirm',
    cancel: 'Cancel',
    submit: 'Submit',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    complete: 'Complete',
    search: 'Search',
    loading: 'Loading...',
    noData: 'No Data',
    success: 'Success',
    failed: 'Failed',
    processing: 'Processing',
    waiting: 'Waiting',
    today: 'Today',
    yesterday: 'Yesterday',
    time: 'Time'
  },

  // 菜单导航
  menu: {
    home: 'Home',
    market: 'Market',
    trade: 'Trade',
    favorite: 'Favorite',
    mine: 'Mine'
  },

  // 交易页面
  trade: {
    title: 'Trade',
    profitFunds: 'Profit Funds',
    availableFunds: 'Available Funds',
    positionValue: 'Position Value',
    totalProfitLoss: 'Total P&L',
    returnRate: 'Return Rate',
    totalShares: 'Total Shares',
    buyPrice: 'Buy Price',
    marketValue: 'Market Value',
    cost: 'Cost',
    totalFees: 'Total Fees',
    details: 'Details',
    holding: 'Holding',
    pending: 'Pending',
    sell: 'Sell',
    closePosition: 'Close Position'
  },

  // 持仓详情页面
  positionDetail: {
    title: 'Position Details',
    profitLoss: 'Profit & Loss',
    returnRate: 'Return Rate',
    totalShares: 'Total Shares',
    buyPrice: 'Buy Price',
    marketValue: 'Market Value',
    cost: 'Cost',
    tradeType: 'Trade Type',
    buyLong: 'Buy Long',
    buyShort: 'Buy Short',
    orderNumber: 'Order Number',
    buyShares: 'Buy Shares',
    buyTime: 'Buy Time',
    sellTime: 'Sell Time',
    sellPrice: 'Sell Price',
    type: 'Type',
    marketPrice: 'Market Price',
    pairTrade: 'Pair Trade',
    buyFee: 'Buy Fee',
    sellFee: 'Sell Fee'
  },

  // 个人中心
  mine: {
    totalAssets: 'Total Assets',
    availableFunds: 'Available Funds',
    profitableFunds: 'Profitable Funds',
    freezeFunds: 'Freeze Funds',
    recharge: 'Recharge',
    withdraw: 'Withdraw',
    bankCard: 'Bank Card',
    realNameAuth: 'Real Name Authentication',
    fundRecords: 'Fund Records',
    privacyPolicy: 'Privacy Policy',
    changeLoginPassword: 'Change Login Password',
    changeFundPassword: 'Change Fund Password',
    languageSwitch: 'Language Switch',
    service: 'Service',
    logout: 'Logout'
  },

  // 存股借券
  stockLending: {
    title: 'Stock Lending',
    lendingList: 'Lending List',
    lendingPositions: 'Lending Positions',
    placeOrder: 'Place Order',
    referencePrice: 'Reference Price',
    referenceRate: 'Reference Rate',
    lendingDays: 'Lending Days',
    noThreshold: 'No Threshold',
    tenThousand: 'Ten Thousand',
    minLendingAmount: 'Minimum Lending Amount',
    requiredShares: 'Required Shares',
    closedType: 'Closed Type',
    type: 'Type',
    ended: 'Ended',
    lending: 'Lending',
    lendingIncome: 'Lending Income',
    lendingRate: 'Lending Rate',
    lendingShares: 'Lending Shares',
    lendingMarketValue: 'Lending Market Value',
    lendingTime: 'Lending Time',
    lend: 'Lend',
    buyShares: 'Buy Shares',
    enterBuyShares: 'Enter Number of Shares to Buy'
  },

  // 新股申购
  newStock: {
    title: 'New Stock Subscription',
    pending: 'Pending Subscription',
    subscribing: 'Subscribing',
    ended: 'Subscription Ended',
    enterQuantity: 'Enter Subscription Quantity'
  },

  // 大宗交易
  blockTrade: {
    title: 'Block Trading',
    stockList: 'Stock List',
    purchaseRecords: 'Purchase Records',
    enterBuyShares: 'Enter Number of Shares to Buy'
  },

  // 下拉刷新
  refresh: {
    pullToRefresh: 'Pull down to refresh...',
    releaseToRefresh: 'Release to refresh...',
    loading: 'Loading...'
  },

  // 服务端返回的常见消息（保持中文键名以便服务端消息翻译）
  '查询成功': 'Query successful',
  '参数错误': 'Parameter error',
  '操作成功': 'Operation successful',
  '操作失败': 'Operation failed',
  '余额不足': 'Insufficient balance',
  '请输入购买张数': 'Please enter number of shares to buy'
}
