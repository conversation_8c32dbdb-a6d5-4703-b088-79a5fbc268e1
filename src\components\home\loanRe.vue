<template>
	<div class="pages">
		<top-back title="借貸記錄"></top-back>
		<div class="card">
			<div class="card-item" v-for="(item, index) in reList" :key="index">
				<div class="card-row flex flex-b">
					<div class="card-value">{{ $formatMoney(item.money, 0) }}</div>
					<div class="card-status">{{ $t(stateList[item.status]) || '-' }}</div>
				</div>
				<div class="card-row2 flex flex-b">
					<div class="card-name">{{ item.order_number }}</div>
					<div class="card-time">{{ $formatDate('YYYY/MM/DD hh:mm:ss', new Date(item.create_time *1000).getTime()) }}</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
	export default {
		data() {
			return {
				reList: [
					// {
					// 	order_number:13332222,
					// 	money:200,
					// 	create_time:33323,
					// }
				],
				stateList: ['審核中', '已通過', '已拒絕'],
			};
		},
		components: {},
		created() {
			this.getReList();
		},
		methods: {
			getReList() {
				this.$server.post("/user/loanlist", {
					type: 'twd'
				}).then((res) => {
					if (res.status == 1) {
						this.reList = res.data;
					}
				});
			}
		}
	};
</script>

<style scoped lang="less">
	.pages {
		min-height: 100vh;
		padding-top: 0.6rem;
	}
	.box {
		margin: .2rem;
		height: 2rem;
		background: #FFFFFF;
		border: 1px solid #FFFFFF;
		box-shadow: 0 .01rem .09rem 0 rgba(192, 108, 108, 0.14);
		border-radius: .1rem;
		padding: 0 .2rem;

		.box-head {
			width: 100%;
			height: 1.2rem;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.box-h-l {
				display: flex;
				align-items: center;

				.box-h-n {
					font-size: .15rem;
					color: #333;
				}

				.box-h-v {
					font-size: .18rem;
					color: #FD4331;
				}
			}

			.box-h-r {
				padding: 0 .1rem;
				min-width: .9rem;
				height: .3rem;
				display: flex;
				justify-content: center;
				align-items: center;
				background: #ffaa00;
				border-radius: .5rem;

				image {
					width: .35rem;
					height: .35rem;
				}

				span {
					font-size: .14rem;
					color: #fff;
				}
			}
		}

		.box-row {
			width: 100%;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.box-col {
				width: 50%;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;

				.box-r-n {
					font-size: .14rem;
					color: #666666;
				}

				.box-r-v {
					padding-top: .05rem;
					font-size: .16rem;
					color: #333;
				}
			}
		}
	}

	::v-deep .uni-input-input {
		font-size: 28rpx;
		font-weight: 400;
		color: #333;
	}

	.card {
		padding: .1rem;
		.card-item {
			margin-bottom: .1rem;
			background: #FFFFFF;
			border-radius: 0.04rem 0.04rem 0.04rem 0.04rem;
			padding: 0.12rem;
			.card-row {
				width: 100%;
				display: flex;
				align-items: center;
				.card-value {
					font-family: PingFang SC, PingFang SC;
					font-weight: 600;
					font-size: 0.2rem;
					color: #020202;
				}
				.card-status {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 0.14rem;
					color: #F19234;
				}
			}
			.card-row2 {
				width: 100%;
				display: flex;
				align-items: center;
				padding: 0.12rem 0 0;
				.card-name {
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 0.14rem;
					color: #767676;
				}
			
				.card-time {
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 0.14rem;
					color: #767676;
				}
			}
		}
	}
</style>