<template>
  <!-- 个人 -->
  <div class="page ">
    <van-pull-refresh
      v-model="isLoading"
      @refresh="onRefresh"
      :loosing-text="$t('new').t3"
      :loading-text="$t('new').t4"
      :pulling-text="$t('new').t5"
    >
      <div class="top">
        <!-- <div class="flex flex-b pad10">
          <div class="flex">
            <div
              class="icon tz animate__animated animate__fadeIn"
              @click="$toPage('/information/userInfo')"
            ></div>
            <div
              class="icon set animate__animated animate__fadeIn"
              @click="$toPage('/information/setting')"
            ></div>
          </div>
        </div> -->

        <div class="flex flex-b user-info">
          <div class="flex">
            <div class="icon user animate__animated animate__fadeIn"></div>
            <div>
              <div class="name">{{ userInfo.realname }}</div>
              <div class="account">{{ userInfo.account }}</div>
            </div>
          </div>

          <div
            class="icon close animate__animated animate__fadeIn"
            @click="goBack()"
          ></div>

          <!-- <div class="rz-btn flex" @click="goAuthInfo()">
            <div class="bt">
              {{ userInfo.is_true == 1 ? $t("new").a17 : $t("new").a8 }}
            </div>
            <div class="icon close animate__animated animate__fadeIn"></div>
          </div> -->
        </div>
      </div>

      <div class="cot">
        <div class="money" v-if="false">
          <div class="flex flex-b tops ">
            <div class="flex-1">
              <div class="t flex" @click="show = !show">
                {{ $t("mine").txt1 }}
                <div
                  class="icon animate__animated animate__fadeIn"
                  :class="show ? 'zy' : 'by'"
                ></div>
              </div>
              <div class="num">
                {{ show ? $formatMoney(totalAssets) || 0 : "****" }}
              </div>
            </div>

            <!-- 图 -->
            <div class="animate__animated animate__fadeIn">
              <div class="" id="main"></div>
            </div>
          </div>

          <div class="nums">
            <div class="item flex flex-b">
              <div class="t2">
                <span class="c1 animate__animated animate__fadeIn"> </span
                >{{ $t("mine").txt2 }}
              </div>
              <div class="t1">
                {{ show ? $formatMoney(userInfo.krw) || 0 : "****" }}
              </div>
            </div>
            <div class="item flex flex-b">
              <div class="t2">
                <span class="c2 animate__animated animate__fadeIn"> </span
                >{{ $t("mine").txt3 }}
              </div>
              <div class="t1">
                {{ show ? $formatMoney(totalProfit) || 0 : "****" }}
              </div>
            </div>
            <div class="item flex flex-b">
              <div class="t2">
                <span class="c3 animate__animated animate__fadeIn"> </span
                >{{ $t("mine").txt4 }}
              </div>
              <div class="t1">
                {{ show ? $formatMoney(freezeAssets) || 0 : "****" }}
              </div>
            </div>
            <!-- <div class="item">
              <div class="t1">{{ $formatMoney(userInfo.dollar) }}</div>
              <div class="t2">{{ $t("new").a36 }} (USD)</div>
            </div> -->
          </div>
        </div>

        <div class="btns flex flex-b">
          <!--  @click="$toPage('/information/recharge')" -->
          <div
            class="btn animate__animated animate__fadeIn"
            @click="showCzTips"
          >
            {{ $t("new").b }}
          </div>
          <div
            class="btn btn1 animate__animated animate__fadeIn"
            @click="$toPage('/information/cashOut')"
          >
            {{ $t("new").a23 }}
          </div>
        </div>

        <div class="func">
          <div class="tab">
            <div
              class="tab-item flex flex-b"
              v-for="(item, i) in tabList"
              :key="i"
              @click="goUrl(item.url)"
            >
              <div class="flex ">
                <div
                  class="icon animate__animated animate__fadeIn"
                  :class="item.icon"
                ></div>
                <div class="t">{{ item.name }}</div>
              </div>
              <div class="icon jt1 animate__animated animate__fadeIn"></div>
            </div>
          </div>
        </div>
      </div>
    </van-pull-refresh>
    <loading ref="loading" />

    <tab-bar :current="4"></tab-bar>
  </div>
</template>

<script>
import * as echarts from "echarts";
export default {
  name: "payBag",
  props: {},
  data() {
    return {
      chartData: [],
      show: false,
      loading: true,
      isLoading: false,
      kfUrl: "",
      userInfo: {},
      currentIndex: 0,
      moneyType: ["baht", "hkd", "dollar"],
      tabList: [
        {
          name: this.$t("mine").menu3,
          icon: "m1",
          url: "/information/bankList",
        },
        {
          name: this.$t("mine").menu5,
          icon: "m2",
          url: "/information/fundRecord",
        },
        {
          name: this.$t("mine").menu4,
          icon: "m3",
          // url: "/information/authInfo",
          url: "authInfo",
        },
        {
          name: this.$t("setting").txt3,
          icon: "m4",
          url: "/information/loginPass",
        },
        {
          name: this.$t("setting").txt4,
          icon: "m5",
          url: "/information/fundPass",
        },
        {
          name: this.$t("mine").menu10,
          icon: "m6",
          url: "kefu",
        },
        {
          name: this.$t("mine").menu9,
          icon: "m7",
          url: "/information/changeLang",
        },
        {
          name: this.$t("setting").btn,
          icon: "m8",
          url: "exit",
        },
        // {
        //   name: this.$t("exchange").title,
        //   icon: "m4",
        //   url: "/information/exChange",
        // },
      ],
      totalProfit: 0,
      totalAssets: 0,
      freezeAssets: 0,
      myChart: null,
    };
  },
  computed: {},
  mounted() {
    // this.getConfig();
    this.getTotalProfit();
    this.getTotalAssets();
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    goAuthInfo() {
      if (this.userInfo.is_true == 1) {
        this.$toast(this.$t("new").tt);
      } else {
        this.$toPage("/information/authInfo");
      }
    },
    goUrl(url) {
      if (url == "kefu") {
        this.goKefu();
      } else if (url == "authInfo") {
        if (this.userInfo.is_true == 1) {
          this.$toast(this.$t("new").tt);
        } else {
          this.$toPage("/information/authInfo");
        }
      } else {
        this.$toPage(url);
      }
    },
    showCzTips() {
      this.$toast(this.$t("czts"));
    },
    // 下拉刷新
    onRefresh() {
      // this.getConfig();
      this.getTotalProfit();
      this.getTotalAssets();
    },
    goKefu() {
      this.getConfig();
    },
    //盈利資金
    async getTotalProfit() {
      const res = await this.$server.post("/transaction/userstocklist", {
        is_type: 0,
      });
      let fdyk = 0;
      if (res.status == 1) {
        fdyk = Number(res.data.fdyk);
      }
      this.totalProfit = fdyk;
    },
    // 获取总资产
    async getTotalAssets() {
      this.$refs.loading.open(); //开启加载

      const res = await this.$server.post("/user/getUserinfo", {});
      if (res.status == 1) {
        this.userInfo = res.data;
      }
      let krw = Number(res.data.krw || 0); //用户可用余额
      // 获取跟单盈亏
      const res1 = await this.$server.post("/transaction/userproductlist", {});
      let followingFreeze = 0; //量化跟单的认缴冻结
      if (res1.status == 1 && res1.data && Array.isArray(res1.data)) {
        let arr = [];
        res1.data.forEach((item) => {
          if (item.status == 0) {
            arr.push(Number(item.money)); //跟单冻结的资金
          }
        });
        let total = arr.reduce((a, b) => a + b, 0);
        followingFreeze = total;
      }

      // 申購列表的投資
      const res2 = await this.$server.post("/transaction/usernewstocklist", {});
      let subscriptionProfit = 0; //新股申请的认缴冻结
      if (res2.status == 1 && res2.data && Array.isArray(res2.data)) {
        let arr = [];
        let arr1 = [];
        res2.data.forEach((item) => {
          if (item.status == 1) {
            arr.push(Number(item.rjmoney == "-" ? 0 : item.rjmoney)); //認繳的资金
          }
          if (item.status == 0) {
            arr1.push(Number(item.rjmoney == "-" ? 0 : item.rjmoney)); //计算申购认缴等于0的
          }
        });
        let total = arr.reduce((a, b) => a + b, 0);
        let total1 = arr1.reduce((a, b) => a + b, 0);

        subscriptionProfit = total + total1;
      }

      // 日内交易的申请冻结 接口报错
      const res3 = await this.$server.post("/transaction/urnjylist", {
        type: 1,
      });
      let dayDelFreeze = 0;
      if (res3.status == 1 && res3.data && Array.isArray(res3.data)) {
        let arr = [];
        res3.data.forEach((item) => {
          if (item.status == 0) {
            arr.push(Number(item.credit));
          }
        });
        let total = arr.reduce((a, b) => a + b, 0);
        dayDelFreeze = total;
      }

      // 持仓中的申请冻结
      const res4 = await this.$server.post("/transaction/userstocklist", {
        is_type: 0,
      });
      let positionFreeze = 0;
      delete res4.data.ccsz;
      delete res4.data.fdyk;
      let dataArr = Object.values(res4.data);
      if (dataArr.length) {
        let arr = [];
        dataArr.forEach((item) => {
          if (item.status == 0) {
            // arr.push(Number(item.credit)); //認繳的资金
            arr.push(
              Number(item.buy_price) * Number(item.stock_num) + item.yingkui
            ); //認繳的资金  买入本金+盈利
          }
        });

        let total = arr.reduce((a, b) => a + b, 0);
        positionFreeze = total;
      }

      // 大宗交易申请冻结
      const res5 = await this.$server.post("/transaction/ustockslist", {
        type: 0,
      });
      let bigDealFreeze = 0;
      if (res5.status == 1 && res5.data && Array.isArray(res5.data)) {
        let arr = [];
        res5.data.forEach((item) => {
          if (item.status == 0) {
            arr.push(Number(item.credit)); //認繳的资金
          }
        });
        let total = arr.reduce((a, b) => a + b, 0);
        bigDealFreeze = total;
      }

      // 日内交易持仓
      const res6 = await this.$server.post("/transaction/userstocklist", {
        buy_type: 1,
      });
      let dayDealFreeze = 0;
      delete res6.data.ccsz;
      delete res6.data.fdyk;
      let dataArr1 = res6.data;
      if (dataArr1.length) {
        let arr = [];
        dataArr1.forEach((item) => {
          if (item.status == 0) {
            arr.push(Number(item.credit)); //認繳的资金
          }
        });
        let total = arr.reduce((a, b) => a + b, 0);
        dayDealFreeze = total;
      }

      // 冻结资产
      this.freezeAssets =
        subscriptionProfit +
        followingFreeze +
        dayDelFreeze +
        bigDealFreeze +
        positionFreeze +
        dayDealFreeze;
      // 总资产
      this.totalAssets = krw + this.freezeAssets;
      this.$refs.loading.close();

      this.chartData = [
        // {
        //   value: this.totalAssets || 0,
        //   name: "",
        // },
        {
          value: krw,
          name: "",
        },
        {
          value: this.totalProfit || 0,
          name: "",
        },
        {
          value: this.freezeAssets || 0,
          name: "",
        },
      ];
      console.log("this.chartData", this.chartData);
      this.getEcharts();
    },

    async getConfig() {
      this.$refs.loading.open();
      const res = await this.$server.post("/common/config", { type: 'twd' });
      let val = {};
      res.data.forEach((vo) => {
        val[vo.name] = vo.value;
      });
      // this.kfUrl = val.kefu;
      this.$refs.loading.close();
      this.$openUrl(val.kefu); //重新获取
    },
    getEcharts() {
      let that = this;
      if (that.myChart !== null) {
        echarts.dispose(that.myChart);
      }

      let chartDom = document.getElementById("main");
      that.myChart = echarts.init(chartDom);
      let option;
      option = {
        // color: ["#C5585E", "#6970AF", "#F9E5E6", "#ECFAFA"], // 顺时针
        color: ["#6970AF", "#F9E5E6", "#ECFAFA"], // 顺时针
        tooltip: {
          trigger: "item",
        },
        // 頂部圖例
        legend: {
          top: "5%",
          left: "center",
          show: false,
        },
        series: [
          {
            name: "",
            type: "pie",
            // radius: ['40%', '70%'], //圆环
            radius: "100%",
            center: ["50%", "50%"],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              label: {
                show: true,
                fontSize: "40",
                fontWeight: "bold",
              },
            },
            labelLine: {
              show: false,
            },
            data: this.chartData,
          },
        ],
      };

      option && that.myChart.setOption(option);
    },
  },
};
</script>

<style scoped lang="less">
.page {
  padding: 0 0 0.5rem;
}
#main {
  width: 0.66rem;
  height: 0.66rem;
  border-radius: 50%;
}

.cot {
  .money {
    padding: 0rem 0.1rem;
    // box-shadow: 0rem 0rem 0.14rem 0rem #eeeeee;
    .tops {
      // background: linear-gradient(90deg, #469d6f, #184856);
      margin-bottom: 0.1rem;
      .t {
        font-weight: 600;
        font-size: 0.12rem;
        .icon {
          margin-left: 0.1rem;
        }
      }
      .num {
        font-weight: 600;
        font-size: 0.24rem;
        color: #c5585e;
        margin: 5px 0;
      }
      .line {
        background: rgba(255, 255, 255, 0.5);
        border-radius: 0.03rem;
        height: 0.07rem;
        position: relative;
        &::after {
          content: "";
          width: 80%;
          height: 100%;
          position: absolute;
          top: 0;
          left: 0;
          z-index: 1;
          background: linear-gradient(45deg, #88d68b, #ffffff);
          border-radius: 0.03rem;
        }
      }
      .zc {
        margin-left: 0.3rem;
      }
    }
    .nums {
      .item {
        padding: 0 0 0.1rem;
        text-align: center;
        .t1 {
          font-weight: 600;
        }
        .t2 {
          span {
            display: inline-block;
            width: 0.12rem;
            height: 0.12rem;
            border-radius: 50%;
            margin-right: 0.1rem;
          }
          .c1 {
            background: #6970af;
          }
          .c2 {
            background: #f9e5e6;
          }
          .c3 {
            background: #ecfafa;
          }
        }
      }
    }
  }

  .btns {
    margin: 0.1rem 0;
    padding: 0 0.15rem;
    .btn {
      width: 30%;
      background: #6970af;
      border-radius: 0.04rem;
      font-weight: 500;
      color: #ffffff;
      text-align: center;
      padding: 0.05rem 0;
      &.btn1 {
        background: #fff;
        border: 0.01rem solid #6970af;
        color: #6970af;
      }
    }
  }
  .ts {
    font-weight: bold;
    font-size: 0.16rem;
    color: #333333;
    padding: 0.2rem 0;
  }

  .func {
    padding: 0.1rem 0;
    border-top: 0.01rem solid #f6f6f6;

    .tab {
      .tab-item {
        border-bottom: 0.01rem solid #f6f6f6;
        padding: 0 0.1rem 0.1rem;
        margin-bottom: 0.1rem;
        &:last-child {
          margin-bottom: 0;
        }
        .t {
          font-weight: 500;
          font-size: 0.16rem;
          color: #333333;
          margin-left: 0.1rem;
        }
      }
    }
  }
}

.top {
  padding: 0.15rem;
  .set {
    margin-left: 0.05rem;
  }
  .pad10 {
    padding: 0 0.1rem 0.1rem;
  }
  .user-info {
    .user {
      margin: 0 0.1rem 0 0;
    }
    .account {
      font-size: 0.12rem;
      color: #8f8f8f;
    }
    .rz-btn {
      .bt {
        background: #eceefe;
        border-radius: 0.04rem;
        padding: 0.05rem 0.1rem;
        font-weight: 500;
        color: #777779;
        margin-right: 0.05rem;
      }
    }
  }
}
</style>
