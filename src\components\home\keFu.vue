<template>
	<div class="pages">
		<div class="top-fixed">
			<div class="header">
				<div class="tit"></div>
			</div>
		</div>
		<div class="flex-column-item">
			<div class="icon user" style="width: 1.35rem;height: 1.35rem;"></div>
			<div class="defbtn" @click="getConfig">{{ $t('聯繫客服') }}</div>
		</div>
		<!-- <tab-bar :current="4"></tab-bar> -->
    <loading ref="loading" />
	</div>
</template>

<script >
	export default {
		name: "favorite",
		props: {},
		components: {
		},
		data() {
			return {
			};
		},
		computed: {
		},
		created() {

		},
		mounted() {
		},
		methods: {
      async getConfig() {
        this.$refs.loading.open();
        const res = await this.$server.post("/common/config", {
          type: "all"
        });
        let val = {};
        res.data.forEach((vo) => {
          val[vo.name] = vo.value;
        });
        this.$refs.loading.close();
        this.$openUrl(val.kefu); //重新获取
      },
      goUrl(item) {
        if (item.url == 'kefu') {
          this.getConfig()
        } else if (item.url == 'kefu1') {
          this.getConfig1()
        } else {
          this.$toPage(item.url)
        }
      },
			// goUrl(url) {
			// 	this.$toPage(url);
			// },
		},
	};
</script>

<style lang="less" scoped>
	.pages{
		padding: 1.5rem 0.12rem 1rem;
		.defbtn{
			width: 100%;
		}
	}
	.top-fixed {
		width: 100%;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 999;

		.header {
			height: 0.5rem;
			padding: 0.15rem;
			width: 100%;
			background: #3A4254;
			position: relative;

			.tit {
				text-align: center;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 0.15rem;
				color: #FFFFFF;
			}
		}

	}
</style>