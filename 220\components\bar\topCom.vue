<template>
  <div class="flex top flex-b">
    <div class="icon cdIcon" @click="showMine = true"></div>
    <img :src="this.newLogo" v-if="this.newLogo" style="height: .34rem;" alt="" />
    <div class="icon sousuo" @click="clickNext('/home/<USER>')"></div>

    <van-popup v-model="showMine" position="left" :style="{ width: '90%' }">
      <usercenter
        style="height: 100%; padding-bottom: 0.28rem;"
        @handleHidden="hiddenUser()"
      ></usercenter>
    </van-popup>
  </div>
</template>
<script type="text/javascript">
import usercenter from "../user/usercenter.vue";
import { Toast } from "vant";
export default {
  name: "topCom",
  data() {
    return {
      showMine: false,
      newLogo: "",
    };
  },
  components: {
    usercenter,
  },
  methods: {
    hiddenUser(e) {
      this.showMine = e;
    },
    getConfig() {
      this.$server.post("/common/config",{type:'all'}).then((str) => {
        if (parseInt(str.status) === 200) {
          str.data.data.forEach((item) => {
            if (item.name == "logo") {
              this.newLogo = item.value;
            }
          });
        } else {
          Toast({
            message: this.$t(str.data.msg),
            duration: 2000,
          });
        }
      });
    },
  },
  mounted() {
    this.getConfig();
  },
};
</script>
<style type="text/css" lang="less" scoped="scoped">
.top {
  height: 0.47rem;
  padding: 0 0.15rem;
  background-color: #0f161c;
}
</style>
