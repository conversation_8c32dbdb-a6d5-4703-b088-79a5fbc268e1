// 设置rem函数
function setRem() {
  if ((navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i))) {
    const screenWidth = 750
    const scale = screenWidth / 16
    const htmlWidth = document.documentElement.clientWidth || document.body.clientWidth
    const htmlDom = document.getElementsByTagName('html')[0]
    htmlDom.style.fontSize = htmlWidth / scale + 'px'
  } else {
    const screenHeight = 1080
    const scale = screenHeight / 16
    const htmlHeight = document.documentElement.clientHeight || document.body.clientHeight
    const htmlDom = document.getElementsByTagName('html')[0]
    htmlDom.style.fontSize = htmlHeight / scale + 'px'
  }
}

// 初始化
setRem()

// 改变窗口大小重新设置rem
window.onresize = function() {
  setRem()
}
