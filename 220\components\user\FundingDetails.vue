<template>
	<div class="FundingDetails">
		<div class="hesadf">
			<topCom></topCom>
		</div>
		<div class="img" @click="showG=false" v-if="showG">
			<div class="icon close02"></div>
		</div>
		<div class="box">
			<div class="tab">
				<div class="thj flex flex-a">
					<a class="" :class="{ xuan: xnum == 1 }" @click="choose(1)">{{ $t("FundRecord").txt1 }}</a>
					<a class="" :class="{ xuan: xnum == 2 }" @click="choose(2)">{{ $t("FundRecord").txt2 }}</a>
					<a class="" :class="{ xuan: xnum == 3 }" @click="choose(3)">{{ $t("FundRecord").txt3 }}</a>
				</div>
			</div>
			<ul class="czjl02" v-if="lisdata.length>0">
				<li class="czli" :class="{ lizj: xnum == 1 }" v-for="(item, i) in lisdata" :key="i">
					<div class="titName flex flex-b">
						<h4>{{$t(item.name)}}</h4>
						<p class="status" :class="{ wanc: item.status != 0 }">{{ $t(item.stat) }}</p>
					</div>
					<div class="bot">
						<div class="border flex flex-b">
							<div class="tt">{{$t('other').txt14}}</div>
							<h5 :class="{red:xnum == 2,blue:xnum==3}">{{ $formatMoney(item.money) }}円
							</h5>
						</div>
						<div class="border flex flex-b">
							<div class="tt">{{$t('other').txt27}}</div>
							<h6>{{ item.order_number }}</h6>
						</div>
						<div class="border flex flex-b">
							<div class="tt">{{$t('other').txt28}}</div>
							<h6>{{ $formatDate("MM-DD hh:mm",item.create_time*1000) }}</h6>
						</div>
					</div>
				</li>
			</ul>
			<template v-if="!lisdata.length">
				<div class="no-data">
					{{ $t("other").txt23 }}
				</div>
			</template>
		</div>
		
		<bottomnav :on='3'></bottomnav>
	</div>
</template>
<script type="text/javascript">
	import Vue from "vue";
	import qs from "qs";
	import axios from "axios";
	import topCom from '../bar/topCom.vue'
	import bottomnav from "../bar/bottomnav.vue";
	import {
		Toast
	} from "vant";
	Vue.use(Toast);
	import {
		List
	} from "vant";

	Vue.use(List);
	import top from "../bar/toper.vue";
	export default {
		name: "FundingDetails",
		data() {
			return {
				xnum: 1,
				chuan: this.$route.query.num,
				page: 0,
				size: 10,
				lisdata: [],
				loading: false,
				finished: false,
				text: this.$t('mine').list.txt2,
				showG:true,
			};
		},

		components: {
			top,
			topCom,
			bottomnav
		},
		computed: {

		},
		methods: {
			choose(e) {
				this.xnum = e;
				this.lisdata = [];
				switch (e) {
					case 1:
						this.getList()
						break;
					case 2:
						this.getList1();
						break;
					case 3:
						this.getList2();
						break;
					default:
						break;
				}
			},
			onLoad() {
				this.getList()
			},
			fanhui() {
				this.$router.go(-1);
			},

			// 这里包含A股 美股台股的交易记录
			getList() {
				this.$server.post("/user/capitalloglist", {
					type: "jpy"
				}).then((res) => {

					for (var i in res.data.data) {
						var row = res.data.data[i];
						if (row.detailed.indexOf('股票代码') > -1) {
							row.title = this.getName(row.detailed);
							row.code = this.getCode(row.detailed);
						} else if (row.detailed.indexOf('发单人') > -1) {
							var arr = row.detailed.split('产品名称');
							row.title = arr[1] || ''
						} else if (row.detailed.indexOf('發單人') > -1) {
							var arr = row.detailed.split('產品名稱');
							row.title = arr[1] || ''
						}
						if (row.detailed.indexOf('跟單') > -1) {
							row.name = '跟单'
						}
            row.detailed = this.$t(row.detailed)
            row.name = this.$t(row.name)
					}
					this.lisdata = [...this.lisdata, ...res.data.data];
				});
			},
			formatDate(date) {
				var date = new Date(date * 1000);
				var YY = date.getFullYear() + "-";
				var MM =
					(date.getMonth() + 1 < 10 ?
						"0" + (date.getMonth() + 1) :
						date.getMonth() + 1) + "-";
				var DD = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
				var hh =
					(date.getHours() < 10 ? "0" + date.getHours() : date.getHours()) + ":";
				var mm =
					(date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes()) +
					":";
				var ss =
					date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
				return MM + DD + " " + hh + mm + ss; //YY + MM + DD +" "+hh + mm + ss;
			},
			//充值和提现
			getList1() {
				this.$server.post("/user/rechargelist", {
					type: "jpy"
				}).then((res) => {

					this.lisdata = [...this.lisdata, ...res.data.data];
				});
			},
			getList2() {
				this.$server.post("/user/withdrawallist", {
					type: "jpy"
				}).then((res) => {

					this.lisdata = [...this.lisdata, ...res.data.data];

				});
			},
			getName(str) {
				var arr = str.split('股票名称');
				return arr[1] || ''
			},
			getCode(str) {
				var tmp = "";
				var arr = str.split(',');
				for (var i in arr) {
					var row = arr[i];
					if (row.indexOf('股票代码') > -1) {
						tmp = row.replace('股票代码', '');
					}
				}
				return tmp;
			}
		},
		destroyed() {},
		mounted() {
			if (this.chuan) {
				this.xnum = this.chuan;
			}
      this.onLoad()
		},
	};
</script>
<style type="text/css" lang="less" scoped="scoped">
	.FundingDetails {
		background-color: #1b232a;
		min-height: 100vh;
		padding-top: .44rem;
		padding-bottom: 1.5rem;

		.hesadf {
			height: 0.44rem;
			width: 100%;
			position: fixed;
			top: 0;
			left: 0;
			z-index: 999;
		}
		.img{
			position: fixed;
			bottom: 0.6rem;
			width: 100%;height: 0.7rem;
			background: url('../../assets/v2/xwbg.png') no-repeat center/100%;
			.close02{
				margin-left: 3.5rem;
				margin-top: 0.1rem;
			}
		}

		.no-data {
			font-size: 0.12rem;
			color: #999;
			text-align: center;
			margin-top: .8rem;
		}

		.czjl02 {
			max-height: calc(100vh - 1.1rem);
			overflow: scroll;

			.czli {
				position: relative;
				color: #000;

				.titName {
					padding: 0.05rem 0.1rem;
					background-color: rgba(94, 213, 168, 0.2);
					border-bottom: 0.02rem solid #fff;

					h4 {
						font-weight: 600;
						font-size: 0.14rem;
						color: #FFFFFF;
					}

					.status {
						font-size: 0.14rem;
						color: #d73d3d;
						font-weight: 550;

						&.wanc {
							color: #A8EC7A;
						}
					}
				}

				.bot {
					padding: 0 0.12rem;

					.border {
						border-bottom: 0.01rem solid rgba(255, 255, 255, 0.2);
						line-height: 0.4rem;

						.tt {
							font-family: PingFangSC, PingFang SC;
							font-weight: 600;
							font-size: 0.11rem;
							color: #718A94;
						}

						h5 {
							font-family: PingFangSC, PingFang SC;
							font-weight: 600;
							font-size: 0.11rem;
							color: #fff;

							&.red {
								color: #FE0000;
							}

							&.blue {
								color: #3498DA;
							}
						}

						h6 {
							font-family: PingFangSC, PingFang SC;
							font-weight: 600;
							font-size: 0.11rem;
							color: #fff;
						}
					}
				}
			}
		}

		.czjl {
			max-height: calc(100vh - 1.1rem);
			overflow: scroll;

			.czli {
				padding: .12rem;
				border-bottom: 0.01rem solid #F4F4F4;
				position: relative;
				color: #000;

				&.lizj {
					.title {
						margin: 0 auto;
						font-weight: 500;
						color: #fff;
						font-size: .14rem;

						p {
							font-size: 0.16rem;
						}

						.time {
							flex: none;
							color: #fff;
						}

						h3 {
							font-weight: 500;
							font-size: 0.18rem;
						}

						h4 {
							font-weight: 400;
							font-size: .14rem;
							color: #fff;

							span {
								display: block;
								margin-top: .06rem;
							}
						}
					}

					&:last-child {
						margin-bottom: 0;
					}
				}

				h5 {
					font-family: PingFang SC;
					font-weight: bold;
					font-size: .18rem;
					color: #1DAB74;

					&.red {
						color: #E52C2C;
					}
				}

				.riqi {
					margin-top: 0.05rem;
					font-weight: 400;
					font-size: .12rem;
					color: #999;
				}

				.status {
					font-size: 0.14rem;
					color: #d73d3d;
					font-weight: 550;
					margin-top: .08rem;

					&.wanc {
						color: #A8EC7A;
					}
				}

				h6 {
					color: #fff;
					font-weight: 400;
					font-size: 0.14rem;
				}
			}
		}

		.tab {
			background: #0f161c;
			width: 100%;
			padding: 0.1rem 0.12rem;
			
			.thj {
				height: 0.44rem;
				background: #1B232A;
				border-radius: 0.1rem;
				position: relative;
				padding: 0.05rem;
				overflow-x: scroll;
				overflow-y: hidden;
				white-space: nowrap;
				text-align: center;
				a {
					padding: 0.08rem 0.12rem;
					min-width: 33.3%;
					text-align: center;
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.14rem;
					color: #B3B8B8;
					transition: all 0.3s;

					&.xuan {
						background: #5ED5A8;
						border-radius: 0.1rem;
						color: #000000;
					}
				}

				.tiaokl {
					width: 0.2rem;
					height: 0.04rem;
					background: #28B2AD;
					border-radius: 0.02rem;
					position: absolute;
					bottom: 0;
					left: 17%;
					margin-left: -0.1rem;
					transition: all 0.5s;
				}
			}
		}
	}
</style>