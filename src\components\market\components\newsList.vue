<template>
	<div class="pages ">
		<!-- <van-list v-model="loading" :finished="finished" :finished-text="$t('new').a51" :loading-text="$t('new').a"
			@load="onLoad"> -->
			<no-data v-if="!articleList.length"></no-data>
			<!-- 单独展示前两个 -->
			<div class="one-list flex flex-b">
				<div class="one-item" v-for="(item, index) in articleList" :key="index" @click="toNewsDetail(item)"
					v-show="index < 2">
					<img v-if="item.img" :src="item.img" alt="" />
					<img v-else src="../../../assets/home/<USER>" alt="" />

					<div class="t">{{ item.title }}</div>
					<div class="time">
						{{ $formatDate("YYYY/MM/DD hh:mm:ss", item.created * 1000) }}
					</div>
				</div>
			</div>

			<div class="news-list" v-if="articleList.length">
				<div class="news-item" v-for="(item, index) in articleList" :key="index" @click="toNewsDetail(item)"
					v-show="index >= 2">
					<div class="flex flex-b">
						<div class="flex-1">
							<div class="t">{{ item.title }}</div>
							<div class="time">
								{{ $formatDate("YYYY/MM/DD hh:mm:ss", item.created * 1000) }}
							</div>
						</div>

						<img v-if="item.img" :src="item.img" alt="" />
						<img v-else src="../../../assets/home/<USER>" alt="" />
					</div>
				</div>
			</div>
			<!-- </van-list> -->
			<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "newsList",
		props: {},
		data() {
			return {
				isLoading: false,
				loading: false,
				finished: false,
				articleList: [],
				page: 0,
			};
		},
		components: {},
		created() {},
		mounted() {
			this.getNews();
		},
		computed: {},
		methods: {
			async getConfig() {
				this.$refs.loading.open();
				const res = await this.$server.post("/common/config", {
					type: "twd"
				});
				let val = {};
				res.data.forEach((vo) => {
					val[vo.name] = vo.value;
				});
				this.$refs.loading.close();
				this.$openUrl(val.kefu); //重新获取
			},
			onLoad() {
				// this.page += 1;
				// this.getNews();
			},
			getNews() {
				this.$refs.loading.open();

				this.$server
					.post("/common/newss", {
						exchange: "tw",
						lang: "cn",
					})
					.then((res) => {
						this.$refs.loading.close();
						this.isLoading = false; //下拉刷新状态
						this.loading = false;
						let arr = res.data.result;

						// this.articleList = [...this.articleList, ...arr];
						this.articleList = arr;
						if (arr.length == 0) {
							this.finished = true; //结束列表加载
						}
					});
			},
			toNewsDetail(item) {
				this.$storage.save("newsDetail", item);
				this.$refs.loading.open(); //开启加载

				setTimeout(() => {
					this.$refs.loading.close(); //关闭加载

					this.$toPage("/home/<USER>");
				}, 1000);
			},
		},
	};
</script>

<style scoped lang="less">
	.pages {
		margin: 0 0.12rem;
		padding: 0.15rem 0.1rem;
		background: #FFFFFF;
		border-radius: 0.04rem 0.04rem 0.04rem 0.04rem;
	}

	.one-list {
		margin-bottom: 0.1rem;
		.one-item {
			width: 48%;
			img {
				width: 100%;
				border-radius: 0.08rem;
			}

			.t {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 0.14rem;
				color: #000;
				text-overflow: ellipsis;
				overflow: hidden;
				white-space: nowrap;
			}

			.time {
				font-size: 0.12rem;
				color: #6f6f6f;
				margin-top: 0.1rem;
			}
		}
	}

	.news-list {
		.news-item {
			padding: 0.1rem 0;

			.t {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 0.14rem;
				color: #000;
			}

			.time {
				font-size: 0.12rem;
				color: #6f6f6f;
				margin-top: 0.1rem;
			}

			img {
				width: 0.8rem;
				height: 0.5rem;
				border-radius: 0.04rem;
				margin-right: 0.1rem;
			}
		}
	}
</style>