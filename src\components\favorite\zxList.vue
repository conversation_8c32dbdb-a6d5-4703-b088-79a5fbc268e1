<template>
	<!-- 自选 -->
	<div class="padd">
		<div class="top-fixed" v-if="false">
			<div class="header flex flex-b">
				<div class="tit">自選</div>
				<div class="flex">
					<!-- <div class="icon ring2 animate__animated animate__fadeIn" @click="$toPage('/information/userInfo')">
						<span v-if="readNum>0" style="width: .08rem;height: .08rem;border-radius: 50%;background: #ff0000;margin-right: .05rem;display: block;"></span>
					</div> -->
					<!-- <div style="margin-left: 0.16rem;" class="icon set animate__animated animate__fadeIn" @click="$toPage('/information/setting')"></div> -->
					<div class="icon ss" style="margin-right: 0.16rem;" @click="$toPage('/favorite/search')"></div>
					<div class="">
						<div class="flex tt" v-if="!show" @click="changeList">
							<div class="icon bjbtn animate__animated animate__fadeIn"></div>
							<!-- 編輯 -->
						</div>
						<div class="flex tt" v-if="show" @click="cancle">取消</div>
					</div>
				</div>
			</div>
		</div>
		<van-skeleton title :row="26" :loading="loading">
			<div class="zxList">
				<div class="title flex flex-b">
					<div class="tit">我的自選</div>
					<div class="">
						<div class="flex tt" v-if="!show" @click="changeList">
							<div class="icon bjbtn animate__animated animate__fadeIn"></div>
							編輯
						</div>
						<div class="flex tt" v-if="show" @click="cancle">取消</div>
					</div>
				</div>
				<no-data v-if="isShow"></no-data>
				<div class="rm-list" v-if="chooseList.length">
					<!-- <div class="flex flex-b titles">
						<div class="flex-1">名稱</div>
						<div class="flex-1 t-c">價格</div>
						<div class="flex-1 t-r flex flex-e" @click="changeListup">漲跌</div>
						<div class="flex-1 t-r flex flex-e" @click="changeListup">
							漲跌
							<div class="icon" :class="isUp ? 'zq' : 'dq'"></div>
						</div>
					</div> -->
					<div class="rm">
						<div class="rm-item flex flex-b" v-for="(item, index) in chooseList" :key="index" @click=" $toDetail(`/market/stockDetail?symbol=${item.symbol}`, item)">
							<div>
								<div class="toph flex flex-2">
									<div v-if="item.showIcon" class="icon wxz animate__animated animate__fadeIn" :class="{ xz: item.choose }" @click.stop="changeItem(index)"></div>
									<!-- <img :src="item.logo" style="width: 0.3rem;border-radius: 50%;" alt="" /> -->
									<div class="name">{{ item.name || "-" }}</div>
									<div class="code">{{ item.symbol || "-" }}</div>
								</div>
								<div class="price red" :class="{'green': Number(item.gain) < 0, }">{{ $formatMoney(item.price) || 0 }}
								</div>
							</div>
							<!-- <div class="flex flex-b mid">
								<div class="icon redIcon" :class="{ greenIcon: Number(item.gain) < 0}"></div>
								<div class="tt" :class="Number(item.gain) < 0?'down':'up'">大買</div>
							</div> -->
							<div>
								<div class="icon" :class="Number(item.gain) < 0?'greenLine':'redLine'"></div>
								<!-- <div class="icon up animate__animated animate__fadeIn" :class="{ down: Number(item.gain) > 0,}"></div>
								<div class="per red" :class="{green: Number(item.gain) < 0, }">{{ $formatMoney(item.gainValue) || 0 }}</div> -->
								<div class="per red" :class="{green: Number(item.gain) < 0, }">{{ item.gain || 0 }}%
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="btns flex flex-b" v-if="show">
					<div class="btn flex" @click="chooseAll">
						<div class="icon " :class="chooseShow ? 'xz' : 'wxz'"></div>
						全選
					</div>
					<div class="btn btn1" @click="delItem">刪除 ({{ chooseLength }})</div>
				</div>
				<div class="flex flex-c" style="margin: 0.2rem 0.35rem 0;">
					<!-- <div class="flex flex-c edit" v-if="!show" @click="changeList">
						<div class="icon bjbtn animate__animated animate__fadeIn"></div>
						編輯自選
					</div>
					<div class="flex flex-c edit" v-if="show" @click="cancle">取消</div> -->
					<div class="btn-box flex flex-c" @click="$toPage('/favorite/search')">
						<!-- <div class="icon zxadd"></div> -->
						添加自選
					</div>
				</div>
			</div>
			<div style="height: .5rem;"></div>
		</van-skeleton>
		<loading ref="loading" />
		<tab-bar :current="5"></tab-bar>
	</div>
</template>

<script>
	export default {
		name: "favorite",
		props: {},
		data() {
			return {
				readNum: 0,
				isUp: true, //默认涨幅在前
				list: [],
				loading: true,
				loading1: true,
				isShow: false,
				isLoading: false,
				show: false,
				chooseList: [],
				lastSymbol: "",
				chooseShow: false,
				chooseLength: 0,
			};
		},
		computed: {},
		created() {
			this.readData();
			this.getMine();
		},
		mounted() {
			// this.$refs.firstLoading.open();
		},
		methods: {
			readData() {
				this.$server.post("/user/notice", {
					type: "zar"
				}).then((res) => {
					if (res.status == 1) {
						let list = res.data;
						let length = list.length
						let a
						for (a = 0; a < length; a++) {
							let read = localStorage.getItem("readMsg")
							let oldRead = JSON.parse(read)
							let hasValue = oldRead.id.includes(list[a].id.toString())
							if (!hasValue) {
								this.readNum += 1
							}
						}
					}
				});
			},
			onRefresh() {
				this.isShow = false;
				this.getMine();
			},
			changeList() {
				if (!this.chooseList.length) return;
				this.chooseList.forEach((item) => {
					item.showIcon = true;
				});
				this.show = true;
			},
			cancle() {
				this.chooseList.forEach((item) => {
					item.showIcon = false;
				});
				this.show = false;
			},
			changeItem(index) {
				this.chooseList.forEach((item, i) => {
					if (index == i) {
						item.choose = !item.choose;
					}
				});

				// 單獨選擇，更新數量
				let arr = this.chooseList.filter((item) => item.choose);
				this.chooseLength = arr.length;

				if (this.chooseLength == this.chooseList.length) {
					this.chooseShow = true;
				} else {
					this.chooseShow = false;
				}
			},
			chooseAll() {
				// 全選
				this.chooseShow = !this.chooseShow;
				this.chooseList.forEach((item) => {
					item.choose = this.chooseShow;
				});

				// 顯示選中數量
				if (this.chooseShow) {
					this.chooseLength = this.chooseList.length;
				} else {
					this.chooseLength = 0;
				}
			},
			delItem() {
				let arr = this.chooseList.filter((item) => item.choose);

				if (arr.length) {
					this.lastSymbol = arr[arr.length - 1].systexId; //记录已选中最后一项的值
					// console.log("lastSymbol", this.lastSymbol);
					this.$refs.loading.open(); //开启加载
					arr.forEach((item) => {
						this.removeOptional(item);
					});
				}
			},
			changeListup() {
				this.isUp = !this.isUp;
				// 漲跌 排序
				if (this.isUp) {
					this.chooseList = this.chooseList.sort(
						(a, b) => Number(b.gainValue) - Number(a.gainValue)
					);
				} else {
					this.chooseList = this.chooseList.sort(
						(a, b) => Number(a.gainValue) - Number(b.gainValue)
					);
				}
			},
			getMine() {
				this.$server.post("/user/Optionallist", {
					type: "zar"
				}).then((res) => {
					this.isLoading = false; //下拉刷新状态
					// this.$refs.firstLoading.close(); //关闭初始加载的效果
					this.loading = false;

					if (res.status == 1) {
						let arr = res.data;

						if (this.show) {
							// 如果是已经选择删除，但是列表还有数据
							arr.forEach((item) => {
								item.showIcon = true;
								item.choose = false;
							});
						} else {
							arr.forEach((item) => {
								item.showIcon = false;
								item.choose = false;
							});
						}

						this.chooseList = arr;
						this.changeListup(); //重新加载排序，涨在前

						if (!arr.length) {
							this.show = false;
							this.isShow = true;
						}
						// console.log("this.chooseList ", this.chooseList);
					}
				});
			},
			removeOptional(item) {
				this.$server
					.post("/user/removeOptional", {
						symbol: item.symbol,
						type: "zar"
					})
					.then((res) => {
						if (res.status == 1) {
							if (this.lastSymbol == item.systexId) {
								this.$refs.loading.close(); //删除最后一项成功，结束加载中
								this.getMine();
							}
						}
					});
			},
		},
	};
</script>

<style scoped lang="less">
	.padd {
		padding: 0.12rem;
	}

	::v-deep .van-skeleton__row {
		background-color: transparent !important;
	}

	::v-deep .van-skeleton__title {
		background-color: transparent !important;
	}

	.top-fixed {
		width: 100%;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 999;
		height: 0.5rem;
		.header {
			height: 0.5rem;
			padding: 0.15rem;
			width: 100%;
			background: #2F3135;
			.tit {
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 0.18rem;
				color: rgba(255,255,255,0.8);
			}
		}

	}
	.zxList{
		background: #232429;
		border-radius: 0.23rem;
		padding-bottom: 0.15rem;
	}
	.title {
		padding: 0.12rem;
		.tit{
			font-family: PingFang SC;
			font-weight: 500;
			font-size: 0.17rem;
			color: #FFFFFF;
		}
	}
	.tt {
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		font-size: 0.12rem;
		color: #fff;
	}
	.bjbtn {
		margin-right: 0.05rem;
	}
	.rm-list {
		.titles {
			padding: 0.05rem 0.12rem;
			div {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 0.12rem;
				color: #A0A0A0;
			}
			.icon {
				margin-left: 0.05rem;
			}
		}

		.rm-item {
			padding: 0.1rem 0;
			margin: 0 0.12rem;
			.toph {
				.wxz{
					margin-right: 0.05rem;
				}
				.name {
					font-family: PingFang SC;
					font-weight: 500;
					font-size: 0.17rem;
					color: #999999;
				}
				.code {
					margin-left: 0.05rem;
					font-family: PingFang SC;
					font-weight: 500;
					font-size: 0.17rem;
					color: #999999;
				}
			}
			.price {
				margin-top: 0.05rem;
				font-family: PingFang SC;
				font-weight: 500;
				font-size: 0.17rem;
			}
			.down {
				margin-right: 0.05rem;
			}
			.per {
				text-align: center;
				margin-top:-0.25rem;
				font-family: PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
			}
		}
	}
	.edit{
		width: 46%;
		height: 0.27rem;
		line-height: 0.27rem;
		border-radius: 0.14rem;
		border: 0.01rem solid #002F7C;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 0.13rem;
		color: #002F7C;
	}
	.line{
		margin: 0 0.12rem;
		width: 0.01rem;
		height: 0.27rem;
		background: #002F7C;
	}
	.btn-box {
		width: 100%;
		height: 0.46rem;
		background: linear-gradient(90deg, #98EF86, #C7F377);
		border-radius: 0.31rem;
		font-family: PingFang TC, PingFang TC;
		font-weight: 400;
		font-size: 0.16rem;
		color: #000;
		line-height: 0.46rem;
	}

	.index {
		.t {
			font-weight: 500;
			color: #1e1e1e;
			padding: 0 0.1rem 0.1rem;
		}
	}

	.nums {
		text-align: center;
		padding: 0.1rem 0;
		// border-top: 0.01rem solid #f5f5f5;
		border-bottom: 0.01rem solid #f5f5f5;

		.nums-item {
			width: 32%;

			&.center {
				border-left: 0.02rem solid #bbc5c1;
				border-right: 0.02rem solid #bbc5c1;
			}

			.name {
				font-weight: 600;
				font-size: 0.12rem;
				margin-bottom: 0.05rem;
			}

			.icon {
				margin-left: 0.05rem;
			}

			.t {
				font-weight: 500;
				font-size: 0.16rem;
				color: #c5585e;
				margin: 0.1rem 0;

				&.die {
					color: #4f8672;
				}
			}

			.t1 {
				font-size: 0.12rem;
				color: #c5585e;

				&.die {
					color: #4f8672;
				}
			}
		}
	}


	.btns {
		background: #232429;
		padding: 0.12rem;
		width: 100%;
		position: fixed;
		bottom: 0.75rem;
		left: 0;
		z-index: 999;

		.btn {
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			font-size: 0.13rem;
			color: #fff;

			.icon {
				margin-right: 0.05rem;
			}

			&.btn1 {
				font-family: PingFang TC, PingFang TC;
				font-weight: 400;
				font-size: 0.14rem;
				color: #EE1515;
			}
		}
	}
</style>