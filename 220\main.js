
//注意，因为我们index.js中把i18n绑定到了window，所以要在第一个引入


import Vue from 'vue'
import App from './App.vue'
import router from './router'
import VueResource from 'vue-resource'
import store from './store'
import './plugin/vant'
import { initLang } from './lib/local.js'
import server from './lib/common.js'
//import "./assets/js/jquery.min.js";
import axios from 'axios'

Vue.use(VueResource)
Vue.prototype.$server = server // 全局调用接口

Vue.config.productionTip = false
import tool from "./lib/tool";
import QRCode from 'qrcodejs2';

Vue.prototype.qrCode = QRCode;


Vue.use(tool);
import EventBus from "./EventBus.js"

Vue.prototype.$bus = EventBus;
import { Toast, Dialog,Switch,Popup,PullRefresh,List,Skeleton } from 'vant';

Vue.use(Dialog);
Vue.use(Toast);
Vue.use(Switch);
Vue.use(Popup).use(PullRefresh).use(List).use(Skeleton);
initLang(Vue);


import Mui from 'vue-awesome-mui';
Vue.config.productionTip = false
Vue.use(Mui);


import VueClipboard from 'vue-clipboard2'
Vue.use(VueClipboard)


// 格式化 money 显示千分位分隔符
Vue.prototype.$formatMoney = (value,wei=2) => {
	var b = false;
	if (value == null || value == '' || isNaN(value)) return '';

	value = Number(value).toFixed(wei) //保留两位小数
	value = value.toString();
	if (value.indexOf('-') != -1) {
		b = true;
		value = value.substring(1, value.length);
	}
	if (/^\-?[0-9]+(.[0-9]+)?$/.test(value)) {
		value
			.replace(/[^\d\.]/g, '')
			.replace(/(\.\d{2}).+$/, '$1')
			.replace(/^0+([1-9])/, '$1')
			.replace(/^0+$/, '0');
		let txt = value.split('.');
		while (/\d{4}(,|$)/.test(txt[0])) {
			txt[0] = txt[0].replace(/(\d)(\d{3}(,|$))/, '$1,$2');
		}
		value = txt[0] + (txt.length > 1 ? '.' + txt[1] : '');
	}
	if (b) {
		value = '-' + value;
	}
	return value;
}



Vue.prototype.$formText = (text) => {
	let str = text

	function extractNumber(input) {
		// 使用正则表达式匹配并捕获数字部分
		var regex = /\d+/;
		// 使用正则表达式的 exec 方法获取匹配结果
		var match = input.match(regex);
		// 如果有匹配项，则返回捕获的数字；否则返回 false
		return match ? match[0] : false;
	}

	function replaceNumberWithPlaceholder(input) {
		// 使用正则表达式匹配数字部分
		var regex = /\d+/;
		// 使用 replace 方法将数字替换成占位符
		var replacedString = input.replace(regex, "{{numberPlaceholder}}");
		// 返回替换后的字符串
		return replacedString;
	}

	let num = extractNumber(str)
	let str1 = Vue.prototype.$t(replaceNumberWithPlaceholder(str))
	return str1.replace("{{numberPlaceholder}}", Vue.prototype.$formatMoney(num, 0))
}

Vue.prototype.$formatDate = (fmt, date) => {
	let ret;
	if(typeof date=='string'&&date.indexOf('-')){
		date = date.replaceAll('-','/');
	}
	date = new Date(date);
	//console.log(date.toDateString().split(' '))
	const opt = {
		'Y+': date.getFullYear().toString(), // 年
		'M+': (date.getMonth() + 1).toString(), // 月
		'D+': date.getDate().toString(), // 日
		'h+': date.getHours().toString(), // 时
		'm+': date.getMinutes().toString(), // 分
		's+': date.getSeconds().toString(), // 秒
		'E+': date.toDateString().split(" ")[1]//
		// 有其他格式化字符需求可以继续添加，必须转化成字符串
	};
	for (let k in opt) {
		ret = new RegExp('(' + k + ')').exec(fmt);
		if (ret) {
			fmt = fmt.replace(ret[1], ret[1].length === 1 ? opt[k] : opt[k].padStart(ret[1].length, '0'));
		}
	}
	return fmt;
}

let openInBrowser = (url) => {
	window.location.href = url;
}

Vue.prototype.openInBrowser = openInBrowser;

Vue.prototype.clickNext = function(link){
	if(!link){
		return;
	}
	this.$router.push({
		path: link,
	});
}

Vue.prototype.clickBack = function(value){
	this.$router.go(-1);
}

//收藏
Vue.prototype.$getOp = function(code,type, call){
	this.$server.post("/user/Optionallist", { type: 'jpy' }).then((res) => {
		for (let key in res.data.data) {
			let vo = res.data.data[key];
			if (vo && vo.symbol == code) {
				call(true)
				return;
			}
		}
		call(false)
	});
}




// 替换返回时间字符翻译
Vue.prototype.$formTextTime = (text) => {
	let str = text

	function extractTimeRange(input) {
		// 使用正则表达式匹配并捕获数字部分
		var regex = /(\d{2}:\d{2}:\d{2})-(\d{2}:\d{2}:\d{2})/;
		// 使用正则表达式的 exec 方法获取匹配结果
		var match = input.match(regex);
		// 如果有匹配项，则返回捕获的数字；否则返回 false
		return match ? match[0] : false;
	}

	function replaceTimeWithPlaceholder(input) {
		// 使用正则表达式匹配数字部分
		var regex = /(\d{2}:\d{2}:\d{2})-(\d{2}:\d{2}:\d{2})/;
		// 使用 replace 方法将数字替换成占位符
		var replacedString = input.replace(regex, "{{numberPlaceholder}}");
		// 返回替换后的字符串
		return replacedString;
	}

	let timeStr = extractTimeRange(str)
	let str1 = Vue.prototype.$t(replaceTimeWithPlaceholder(str))
	return str1.replace("{{numberPlaceholder}}", timeStr)
}




Vue.filter('sub9', function (str) {
	return str.length > 9 ? str.substring(0, 10) : str;
})

Vue.filter('subStr', function (str) {
	return str.length > 20 ? str.substring(0, 6) + '...' + str.slice(-6) : str;
})

Vue.filter('yuedu', function (num) {
	return num + ((num % 3) + 1) * (1331 + Math.random() * 331 | 0)
})


Vue.filter('conv', function (str) {


	return parseInt(str * 100);
})

Vue.filter('Convert', function (str) {
	var num = parseInt(str).toString()
	if (num.length >= 7) {
		return (parseFloat(str) / 1000000).toFixed(2) + "juta"
	} else if (num.length >= 4) {
		return (parseFloat(str) / 1000).toFixed(2) + "K"
	} else {
		return parseFloat(str).toFixed(2)
	}
})

Vue.prototype.$currency = "円"

new Vue({
	router,
	store,
	render: h => h(App)
}).$mount('#app')
