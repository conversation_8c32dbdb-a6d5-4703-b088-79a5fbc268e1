<template>
  <!-- 自选 -->
  <div class="page ">
    <top-menu></top-menu>

    <van-pull-refresh
      v-model="isLoading"
      @refresh="onRefresh"
      :loosing-text="$t('new').t3"
      :loading-text="$t('new').t4"
      :pulling-text="$t('new').t5"
    >
      <van-skeleton title :row="26" :loading="loading">
        <div class="title flex flex-b">
          <div>{{ $t("new").a1 }}</div>
        </div>

        <div class="cot">
          <no-data v-if="isShow"></no-data>

          <div class="list" v-if="chooseList.length">
            <div class="flex flex-b titles">
              <div class="flex-2">{{ $t("newt").t57 }}</div>
              <div class="flex-1 t-r">{{ $t("newt").t58 }}</div>
              <div class="flex-1 t-r flex flex-e">
                {{ $t("newt").t59 }}
              </div>
            </div>

            <div
              class="list-item flex flex-b"
              v-for="(item, index) in chooseList"
              :key="index"
              @click="
                $toDetail(`/market/stockDetail?symbol=${item.symbol}`, item)
              "
            >
              <div class="flex-2 flex ">
                <div>
                  <div class="name">{{ item.name || "-" }}</div>
                  <div class="code">{{ item.symbol || "-" }}</div>
                </div>
              </div>

              <div class=" price flex-1">
                {{ $formatMoney(item.price) || 0 }}
              </div>

              <div
                class="flex-1 per t-r flex flex-e"
                :class="{
                  green: Number(item.gain) < 0,
                }"
              >
                <div class="t" :class="item.gainValue > 0 ? 'red' : 'green'">
                  {{ item.gainValue > 0 ? "+" : ""
                  }}{{ $formatMoney(item.gainValue) || 0 }}
                </div>
                <div class="t t1" :class="item.gainValue > 0 ? 'red' : 'green'">
                  {{ item.gain > 0 ? "+" : "" }}{{ item.gain || 0 }}%
                </div>
              </div>
            </div>
          </div>

          <div class="btns flex flex-b" v-if="chooseList.length">
            <div class="btn flex flex-c" @click="$toPage('/favorite/search')">
              <div class="icon jzx"></div>
              {{ $t("new").t36 }}
            </div>
            <div
              class="btn btn1 flex flex-c"
              @click="$toPage('/favorite/editSelf')"
            >
              <div class="icon bjzx"></div>
              {{ $t("编辑自选") }}
            </div>
          </div>

          <!-- 列表空显示 -->
          <div class="btns bt flex flex-b" v-if="!chooseList.length">
            <div class="btn flex flex-c" @click="$toPage('/favorite/search')">
              <div class="icon jzx"></div>
              {{ $t("new").t36 }}
            </div>
            <!-- <div
              class="btn btn1 flex flex-c"
              @click="$toPage('/favorite/editSelf')"
            >
              <div class="icon bjzx"></div>
              {{ $t("编辑自选") }}
            </div> -->
          </div>
        </div>
      </van-skeleton>
    </van-pull-refresh>
    <tab-bar :current="3"></tab-bar>

    <loading ref="loading" />
  </div>
</template>

<script>
export default {
  name: "favorite",
  props: {},
  data() {
    return {
      isUp: false, //默认涨幅在前
      list: [],
      loading: true,
      loading1: true,
      isShow: false,
      isLoading: false,
      show: false,
      chooseList: [],
      lastSymbol: "",
    };
  },
  computed: {},
  created() {
    this.getMine();
  },
  mounted() {
    // this.$refs.firstLoading.open();
  },
  methods: {
    onRefresh() {
      this.isShow = false;
      this.getMine();
    },
    changeListup() {
      this.isUp = !this.isUp;
      let arr = [];
      let arr1 = [];
      this.chooseList.forEach((item) => {
        if (item.gain > 0) {
          arr.push(item);
        } else {
          arr1.push(item);
        }
      });

      if (this.isUp) {
        arr.sort((a, b) => b.gain - a.gain);
        arr1.sort((a, b) => b.gain - a.gain);
        this.chooseList = [...arr, ...arr1]; //涨在前、高在前
      } else {
        arr.sort((a, b) => a.gain - b.gain);
        arr1.sort((a, b) => a.gain - b.gain);
        this.chooseList = [...arr1, ...arr]; //跌在前、低在前
      }
    },
    getMine() {
      this.$server.post("/transaction/Optionallist", { offset: 0 }).then((res) => {
        this.isLoading = false; //下拉刷新状态
        // this.$refs.firstLoading.close(); //关闭初始加载的效果
        this.loading = false;
        if (res.status == 1) {
          if (this.show) {
            // 如果是已经选择删除，但是列表还有数据
            res.data.forEach((item) => {
              item.showIcon = true;
              item.choose = false;
            });
          } else {
            res.data.forEach((item) => {
              item.showIcon = false;
              item.choose = false;
            });
          }

          this.chooseList = res.data;
          this.changeListup(); //重新加载排序，涨在前

          if (!res.data.length) {
            this.show = false;
            this.isShow = true;
          }
          // console.log("this.chooseList ", this.chooseList);
        }
      });
    },
  },
};
</script>

<style scoped lang="less">
::v-deep .search {
  border-radius: 0.04rem;
}
::v-deep .van-skeleton__row{
  background-color:transparent !important;
}
::v-deep .van-skeleton__title{
  background-color:transparent !important;
}
.page {
  padding: 0.5rem 0;
  min-height: 100vh;
}
.btn-box {
  padding: 0.2rem 0.1rem;
  .b-btn {
    margin: 0;
  }
}

.index {
  .t {
    font-weight: 500;
    color: #1e1e1e;
    padding: 0 0.1rem 0.1rem;
  }
}

.red {
  color: #ba3b3a !important;
}
.green {
  color: #39B44C !important;
}

.title {
  padding: 0 0.1rem;
  div {
    font-weight: 600;
    font-size: 0.16rem;
    color: #000000;
  }
}

.cot {
  .list {
    .titles {
      padding: 0.1rem 0.1rem 0;
      div {
        font-size: 0.12rem;
        color: #535353;
      }
    }
    .list-item {
      padding: 0.1rem;
      border-bottom: 0.01rem solid #f4f4f4;
      .wxz {
        margin-right: 0.05rem;
      }
      .name {
        font-size: 0.12rem;
        color: #000000;
      }
      .code {
        font-size: 0.1rem;
        color: #c4c4c4;
      }
      .price {
        font-size: 0.12rem;
        color: #0c0c0c;
        text-align: center;
      }
      .per {
        .t {
          font-size: 0.12rem;
          color: #0c0c0c;
          &.t1 {
            margin-left: 0.1rem;
          }
        }
      }
    }
  }
}
.btns {
  margin: 0.2rem 0.1rem;
  position: relative;
  &.bt {
    .btn {
      width: 100%;
    }
    &::after {
      display: none;
    }
  }
  &::after {
    content: "";
    position: absolute;
    width: 0.02rem;
    height: 50%;
    background-color: #888888;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .btn {
    width: 48%;
    font-weight: 500;
    text-align: center;
    padding: 0.1rem 0;
    font-size: 0.12rem;
    color: #888888;
    .icon {
      margin-right: 0.05rem;
    }
  }
}
</style>
