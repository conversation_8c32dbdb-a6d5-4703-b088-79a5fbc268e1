<template>
<!--  <div class="page flex flex-b">-->
<!--    &lt;!&ndash; <div class="tips">{{ $t("new").t39 }}</div> &ndash;&gt;-->

<!--    <div class="flex-1 ">-->
<!--      <div class="icon jz1 animate__animated animate__fadeIn"></div>-->
<!--    </div>-->

<!--    <div class="btns">-->
<!--      <div-->
<!--        class="bt  animate__animated animate__fadeIn"-->
<!--        @click="$toPage('/login/login')"-->
<!--      >-->
<!--        {{ $t("new").t41 }}-->
<!--      </div>-->
<!--      <div-->
<!--        class="bt bt1 animate__animated animate__fadeIn"-->
<!--        @click="$toPage('/login/register')"-->
<!--      >-->
<!--        {{ $t("new").t40 }}-->
<!--      </div>-->
<!--      &lt;!&ndash; <div class="t" @click="$toPage('kefu', cfg.kefu)">-->
<!--        {{ $t("new").t42 }}-->
<!--      </div> &ndash;&gt;-->
<!--    </div>-->
<!--  </div>-->
  <div>
    <div class="logos"><img :src="$cfg.logo" /></div>
    <div class="titles">
      <div class="titles-i">{{ $t('new')['newt1'] }}</div>
      <div class="titles-i">{{ $t('new')['newt2'] }}</div>
      <div class="titles-i">{{ $t('new')['newt3'] }}</div>
    </div>
    <div class="boxs">
      <div class="boxs-right">
        <div class="boxs-cont flex">
          <span>{{ $t('new')['newt4'] }}</span>
        </div>
        <div class="boxs-cont flex" @click="$toPage('/login/register')">
          <span>{{ $t('new')['newt5'] }}</span>
          <img src="../../assets/home/<USER>" />
        </div>
      </div>
    </div>
    <div class="butts" @click="$toPage('/login/login')">{{ $t('new')['newt6'] }}</div>
  </div>
</template>

<script>
export default {
  name: "start1",
  props: {},
  data() {
    return {
      cfg: {},
    };
  },
  components: {},
  mounted() {
  },
  methods: {
  },
};
</script>

<style scoped lang="less">
.logos {
  padding: 0 .3rem;
  height: 1rem;
  display: flex;
  align-items: center;
  img {
    width: .7rem;
    height: .7rem;
    object-fit: contain;
    animation: bounceIn;
    animation-duration: 10s;
  }
}
.titles {
  padding: 0 .3rem;
  animation: shakeX;
  animation-duration: 2s;
  .titles-i {
    padding-top: .1rem;
    width: 100%;
    font-size: .14rem;
    color: #666666;
  }
}
.boxs {
  margin: .5rem .23rem 0;
  padding: .1rem .15rem;
  background: #6970af;
  box-shadow: 0 .1rem .12rem 0 rgba(0, 0, 0, 0.28);
  border-radius: .1rem;
  border: .01rem solid #ffffff;
  display: flex;
  .boxs-left {
    flex-direction: column;
    img {
      margin: .1rem 0;
      width: .33rem;
      height: .33rem;
    }
  }
  .boxs-right {
    width: 100%;
    animation: shakeX;
    animation-duration: 2s;
    .boxs-cont {
      width: 100%;
      span {
        font-size: .15rem;
        color: #ffffff;
      }
      img {
        margin-left: .08rem;
        width: .17rem;
        height: .12rem;
      }
      &:nth-child(2) {
        margin-top: .2rem;
      }
    }
  }
}
.butts {
  height: .5rem;
  line-height: .5rem;
  text-align: center;
  background: #6970af;
  box-shadow: 0 .05rem .06rem 0 rgba(0, 0, 0, 0.28);
  font-size: .16rem;
  color: #ffffff;

  margin: .5rem .22rem 0;
  border-radius: .09rem;
}
</style>
