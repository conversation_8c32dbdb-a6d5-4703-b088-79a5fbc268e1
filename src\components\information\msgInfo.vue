<template>
	<div class="page ">
		<top-back :title="$t('詳情')"></top-back>

		<div class="list-box">
			<div class="list-item">
				<div>
					<div class="t">{{ item.title }}</div>
					<div class="time">{{ $formatDate('YYYY/MM/DD hh:mm:ss', new Date(item.create_time *
              1000).getTime()) }}</div>
				</div>
				<div class="cot" v-html="item.content"></div>
			</div>
		</div>
	</div>
</template>

<script>
	export default {
		name: "msgInfo",
		data() {
			return {
				item: {
					// title: "通知",
					// create_time: "2024-01-26",
					// content: "contentcontentcontent",
				},
			};
		},
		computed: {},
		components: {},
		created() {
			let id = this.$route.query.id;
			let read = localStorage.getItem("readMsg")
			if (!read) {
				let newRead = {
					id: [id]
				}
				localStorage.setItem("readMsg", JSON.stringify(newRead))
			} else {
				let oldRead = JSON.parse(read)
				let hasValue = oldRead.id.includes(id)
				if (!hasValue) {
					oldRead.id.push(id)
					localStorage.setItem("readMsg", JSON.stringify(oldRead))
				}
			}
			this.initData(id);
		},

		methods: {
			initData(id) {
				this.$server
					.post("/user/noticedetail", {
						id,
						type: "zar",
					})
					.then((res) => {
						if (res.status == 1) {
							this.item = res.data;
						}
					});
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.6rem 0.1rem 0.1rem;
		min-height: 100vh;
	}

	.t {
		font-weight: bold;
	}

	.list-box {
		// background: #ffffff;
		// border-radius: 0.1rem;
		// padding: 0.2rem 0.1rem;
		.t {
			font-weight: bold;
			color: #fff;
		}

		.time {
			font-size: 0.12rem;
			color: #999;
			margin-top: 0.1rem;
		}

		.cot {
			margin-top: 0.1rem;
			color: #fff;
		}
	}
</style>