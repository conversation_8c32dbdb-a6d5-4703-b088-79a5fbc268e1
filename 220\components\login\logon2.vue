<template>
	<div class="logon2">
		<div class="logo text-center" :class="{show:logoShow}">
			<div class="txt flex-column-item">
				<div class="t01">{{$t('logon').txt1}}</div>
				<div class="t02">{{$t('logon').txt2}}</div>
			</div>
		</div>
		<div class="Btn" :class="{show:btnShow}">
			<button class="link1 flex1 flex flex-c" @click="clickNext('/login/register')">{{$t('logon').btn3}}</button>
			<button class="link2 flex1 flex flex-c" @click="clickNext('/login/login')">{{$t('logon').btn4}}</button>
			<button class="link3 flex1 flex flex-c" @click="clickKf()">{{$t('logon').btn5}}</button>
		</div>
		
		<div class="bottom text-center" :class="{show:bottomShow}" v-if="false">
			<div>V2.5.6</div>
		</div>
	</div>
</template>

<script>
	export default {
	    name: "logon2",
	    data() {
	        return {
	            logoShow: false,
	            bottomShow: false,
	            btnShow: false,
	            customer: '',
				logo:'../../assets/skin/logo.png'
	        };
	    },
	    mounted() {
	        let _this = this
	        setTimeout(function () {
	            _this.logoShow = true
	            setTimeout(function () {
	                _this.btnShow = true
					setTimeout(function () {
					    _this.bottomShow = true
					},100)
	            },300)
	        },800)
	    },
	    methods: {
	        clickKf() {
				this.$server.post("/common/config", {type:'all'}).then(res => {
					if (parseInt(res.status) === 200) {
						let list = res.data.data;
						let listLength = list.length;
						let a;
						for (a = 0; a < listLength; a++) {
							if (list[a].name === 'kefu') {
								this.kefu = list[a].value;
								this.openInBrowser(list[a].value)
							}
						}
					}
				});
	        },
	    }
	}
</script>

<style scoped lang="less">
	.logon2 {
	    background: #fff;
	    min-height: 100vh;color: #333333;
		background: #030f1d;
    background-size: cover;
    box-sizing: border-box;
    padding-top: 3.5rem;
		.logo{
			opacity: 0;
			-webkit-transition-duration: 1s;
			-moz-transition-duration: 1s;
			-o-transition-duration: 1s;
			margin-left: -3rem;
			width:100%;
			padding-top:0.7rem;
			.img1{width:1.43rem;height:.45rem;margin-bottom:.5rem;}
			.img2{
				width:2.9rem;
			}
			&.show{
				margin-left:0;
				opacity: 1;
			}
		}
		.txt{
			margin-top: 0.1rem;
			.t01{
				font-weight: 500;
				font-size: 0.24rem;
				color: #FFFFFF;
			}
			.t02{
				margin: 0.1rem;
				width: 2rem;
				font-weight: 400;
				font-size: 0.13rem;
				color: #999999;
				text-align: center;
			}
		}
		.Btn{
			width:100%;
			opacity: 0;
			-webkit-transition-duration: 1s;
			-moz-transition-duration: 1s;
			-o-transition-duration: 1s;
			margin-left: -3rem;
			margin-top:.3rem;
			&.show{
				margin-left:0;
				opacity: 1;
			}
			button{
				height: .44rem;
				width:80%;
				border-radius: .27rem;
				font-size: .14rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 0.14rem;
				margin:0 .37rem;
				&.link1{
					margin-bottom: .15rem;
					color:#000;
					background: #5ED5A8;
					border-radius: 0.1rem;
					border:none;
				}
				&.link2{
					border-radius: 0.1rem;
					border: 0.01rem solid #5ED5A8;
					background:none;
					color: #5ED5A8;
				}
				&.link3{
					border:none;
					font-weight: 400;
					font-size: .13rem;
					color: #fff;
					background:none;
					&:after{
						display:none;
					}
				}
			}
		}
		.bottom{
			position: fixed;bottom:0;left:0;
			width:100%;
			background: #FFFFFF;
			box-shadow: 0 -.01rem .04rem 0 rgba(0,0,0,0.11);
			font-weight: 500;
			font-size: .12rem;padding-top: .3rem;padding-bottom: .1rem;
			color: #212121;
			opacity: 0;
			-webkit-transition-duration: 1s;
			-moz-transition-duration: 1s;
			-o-transition-duration: 1s;
			margin-left: -3rem;
			&.show{
				margin-left:0;
				opacity: 1;
			}
		}
	}
	
</style>