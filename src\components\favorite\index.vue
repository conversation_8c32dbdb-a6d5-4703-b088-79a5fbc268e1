<template>
	<!-- 市场 -->
	<div class="page">
		<div class="top-fixed">
			<div class="searchBox flex" @click="$toPage('/favorite/search')">
				<div class="icon sou" style="margin-right: 0.1rem;"></div>
				<div class="t">搜尋</div>
			</div>
			<!-- <div class="header flex flex-b">
				<div class="nav-box flex">
					<div class="nav-item flex flex-c" v-for="(item, index) in navList" :key="index" :class="{ active: currmentIndex == item.type }" @click="changeNav(item.type)">{{ item.name }}
					</div>
				</div>
				<div class="flex">
					<div class="icon kf1" @click="$toPage('/information/kef')" style="margin-right: 0.1rem;"></div>
					<div class="icon ring animate__animated animate__fadeIn" @click="$toPage('/information/userInfo')">
						<span v-if="readNum>0" style="width: .08rem;height: .08rem;border-radius: 50%;background: #ff0000;margin-right: .05rem;display: block;"></span>
					</div>
				</div>
			</div> -->
			<div class="cyTab flex">
				<div :class="cyTabIndex == index?'cyTabItemd':'cyTabItem'" @click="clickCyTab(index)" v-for="(item,index) in cyTab" :key="index">
					{{item.name}}
				</div>
			</div>
		</div>
		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3" :loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<van-skeleton title :row="6" :loading="loading1" v-if="false">
				<!-- 指數顯示 -->
				<div class="zx-cot">
					<div class="zx-list flex flex-b">
						<div class="zx-item flex-column-item" v-for="(item, i) in indexList" :key="i">
							<div class="name flex flex-1">{{ item.symbolName }}</div>
							<div class="price t-c flex-1" :class="item.change > 0 ? 'red' : 'green'">
								{{ $formatMoney(item.price) }}
							</div>
							<div class="flex flex-e per flex-1" :class="item.change > 0 ? 'red-bg' : 'green-bg'">
								<!-- <div class="icon" :class="item.change > 0 ? 'up' : 'down'"></div> -->
								{{ $formatMoney(item.change) }}({{ item.changePercent }})
							</div>
						</div>
					</div>
				</div>
			</van-skeleton>
			<!-- 自选列表显示 -->
			<zxList v-if="cyTabIndex == 2" />
			<van-skeleton title :row="6" :loading="loading2" v-else>
				<div class="cy">
					<!-- <div class="flex flex-b tt">
						<div class="t">產業報告</div>
						<div class="t1 flex" @click="$toPage('/favorite/categoryList')">更多<div class="icon arrow"></div>
						</div>
					</div> -->
					<!-- <div class="cyTab flex">
						<div :class="cyTabIndex == index?'cyTabItemd':'cyTabItem'" @click="clickCyTab(index)" v-for="(item,index) in cyTab" :key="index">
							{{item.name}}
						</div>
					</div> -->
					<div class="flex title" @click="showCy=!showCy">
						<div>
							<div v-for="(item, i) in categoryList" :key="i" v-if="i==0"></div>{{cyName}}
						</div>
						<div class="icon jt1" style="margin-left: 0.1rem;"></div>
					</div>
					<div class="cy-list flex flex-b flex-wrap" v-if="showCy">
						<div class="cy-item01" v-for="(item, i) in categoryList" :key="i">
							<div class="name" @click="getMarkListStock(item.sectorId)">{{ item.name || "-" }}</div>
						</div>
					</div>
					<div class="cy-list2">
						<div class="cy-item flex flex-b" v-for="(item, i) in cylist" :key="i" @click="$toDetail(`/market/stockDetail?symbol=${item.systexId}`, item)">
							<div>
								<div class="name">{{ item.symbolName || "-" }}</div>
								<div class="price" :class="item.change.sort > 0 ? 'red' : 'green'">{{ $formatMoney(item.price.sort) || "-" }}</div>
							</div>
							<div>
								<div class="icon" :class="item.change.sort>0?'redLine':'greenLine'"></div>
								<!-- <div class="icon" :class="item.change.sort > 0 ? 'up' : 'down'"></div>
								<div>{{ $formatMoney(item.change.sort) }}</div> -->
								<div class="per" :class="item.change.sort > 0 ? 'up' : 'down'">{{item.change.sort > 0 ?'+':'-'}}{{ item.changePercent || "-" }}</div>
							</div>
						</div>
					</div>
				</div>
			</van-skeleton>
			<!-- <div class="cy">
				<div class="cy-list flex flex-b">
					<div class="zhuzi" v-for="(item, i) in categoryList2" :key="i">
						<div class="per flex flex-c" :class="item.change.sort > 0 ? 'red' : 'green'">
							{{ item.changePercent || "-" }}
						</div>
						<div style="height: 1.5rem;display: flex;align-items: flex-end">
							<div :style="[{'background':item.change.sort>0?'#DF3224':'#53B453'},{'height':(Math.abs(item.change.sort)*100/2>100?100:Math.abs(item.change.sort)*100/2)+'%'}]"
								style="width: .28rem;margin: .1rem 0;"></div>
						</div>
						<div class="name" style="height: .4rem;">{{ item.sectorName || "-" }}</div>
					</div>
				</div>
			</div> -->
			<van-skeleton title :row="26" :loading="loading" v-if="false">
				<div class="rm" v-if="currmentIndex == 1">
					<div class="tit">熱門推薦</div>
					<div class="rm-list">
						<div class="titles flex flex-b">
							<div class="flex-2">名稱</div>
							<div class="flex-1">價格</div>
							<div class="flex-2 t-c flex flex-e">
								漲跌
								<!-- <div class="icon" :class="show ? 'zq' : 'dq'" @click="changeList(0)"></div> -->
							</div>
							<div class="flex-2 t-r flex flex-e">
								漲跌幅（%） 
								<!-- <div class="icon" :class="show1 ? 'zq' : 'dq'" @click="changeList(1)"></div> -->
							</div>
						</div>
						<div class="rm-item flex flex-b" v-for="(item, i) in list" :key="i" @click="$toDetail(`/market/stockDetail?symbol=${item.symbol}`, item)">
							<div class="flex-2">
								<div class="name">{{ item.local_name }}</div>
								<div class="code">{{ item.symbol }}</div>
							</div>
							<div class="price flex flex-c flex-1" :class="item.gain > 0 ? 'red-bg' : 'green-bg'">
								{{ $formatMoney(item.price) || "-" }}
							</div>
							<!-- <div class="flex-1 t-c price" :class="item.gain > 0 ? 'red' : 'green'">{{ $formatMoney(item.volume / 1000000) }} M</div> -->
							<!-- <div class="icon animate__animated animate__fadeIn":class="item.gain > 0 ? 'up' : 'down'"></div> -->
							<div class="per flex-2 flex flex-e" :class="item.gain > 0 ? 'red' : 'green'">
								{{ $formatMoney(item.gainValue) || "-" }}
							</div>
							<div class="per flex-2 flex flex-e" :class="item.gain > 0 ? 'red' : 'green'">
								<div class="icon animate__animated animate__fadeIn"
									:class="item.gain> 0 ? 'up' : 'down'"></div>
								{{ item.gain || "-" }}%
							</div>
						</div>
					</div>
				</div>
			</van-skeleton>
			<div class="news" v-if="false">
				<div class="newsList">
					<div class="newItem flex" v-for="(item, i) in newList" :key="i" @click="toNewsDetail(item)">
						<img :src="item.img" alt=""
							style="width: 0.89rem;height: 0.64rem;border-radius: 0.1rem;margin-right: 0.1rem;" />
						<div>
							<div class="time">{{ $formatDate("YYYY-MM-DD", item.created * 1000) }}</div>
							<div class="tit">{{item.title}}</div>
						</div>
					</div>
				</div>
			</div>
		</van-pull-refresh>
		<tab-bar :current="3"></tab-bar>
		<loading ref="loading" />
	</div>
</template>

<script>
	import {nextTick} from "vue";
	import zxList from "./zxList.vue";
	import indexLine from '../components/index-line.vue';
	export default {
		name: "favorite",
		props: {},
		components: {
			zxList,indexLine
		},
		data() {
			return {
				readNum: 0,
				cyTabIndex: 0,
				cyTab: [
					{
						name: '上市',
						type: 'TAI'
					},
					{
						name: '上櫃',
						type: 'TWO'
					},
					{
						name: "自選",
						type: 3
					},
				],
				show: true,
				show1: true,
				navList: [
					// {
					// 	name: "每日資訊",
					// 	type: 0
					// },
					{
						name: "行情",
						type: 1
					},
					// {
					// 	name: "指數",
					// 	type: 2
					// },
					{
						name: "自選",
						type: 3
					},
				],
				currmentIndex: 1,
				indexList: [],
				loading: true,
				loading1: true,
				loading2: true,
				isLoading: false,
				categoryList: [],
				categoryList2: [],
				sort: [{
						name: "跌幅榜",
						id: 1
					},
					{
						name: "漲幅榜",
						id: 0
					},
					{
						name: "成交額",
						id: 2
					},
					// { name: "創高榜", id: 3 },
					// { name: "創低榜", id: 4 },
				],
				sortIndex: 0,
				list: [],
				type: "zhangfb",
				newList: [],
				showCy: false, //显示上市上架
				selIndex: null, //选择得分类名id
				cyName: '', //选择得分类名称
				cylist:[]
			};
		},
		computed: {
			//分类
			cyIndex() {
				return this.cyTab[this.cyTabIndex].type
			},
			filterName() {
				return (value) => {
					value = value.replace("股價", "");
					let indx = value.indexOf("指數");
					return value.slice(indx - 2, indx + 2);
				};
			},
		},
		created() {
			this.readData();
			this.getIndexList();
			// this.getCategory(this.cyIndex);
			this.getList();
			this.getNews()
			this.getCategory(this.cyTab[this.cyTabIndex].type)
			if(this.$route.query.type){
				this.cyTabIndex=this.$route.query.type
			}
		},
		mounted() {
			// this.$refs.firstLoading.open();
		},
		methods: {
			toNewsDetail(item) {
				this.$storage.save("newsDetail", item);
				setTimeout(() => {
					this.$toPage("/home/<USER>");
				}, 1000);
			},
			readData() {
				this.$server.post("/user/notice", {
					type: "twd"
				}).then((res) => {
					if (res.status == 1) {
						let list = res.data;
						let length = list.length
						let a
						for (a = 0; a < length; a++) {
							let read = localStorage.getItem("readMsg")
							let oldRead = JSON.parse(read)
							let hasValue = oldRead.id.includes(list[a].id.toString())
							if (!hasValue) {
								this.readNum += 1
							}
						}
					}
				});
			},
			clickCyTab(index) {
				this.categoryList = []
				this.cyTabIndex = index
				this.getCategory(this.cyTab[this.cyTabIndex].type)
			},
			changeSort(type) {
				// this.$refs.loading.open();
				this.sortIndex = type;
				if (type == 2) {
					this.show = !this.show;
					// 成交額 排序
					if (this.show) {
						this.list = this.list.sort(
							(a, b) => Number(b.volume) - Number(a.volume)
						);
					} else {
						this.list = this.list.sort(
							(a, b) => Number(a.volume) - Number(b.volume)
						);
					}
				} else if (type == 1) {
					this.list = this.list.sort(
						(a, b) => Number(b.gainValue) - Number(a.gainValue)
					);
				} else {
					this.list = this.list.sort(
						(a, b) => Number(a.gainValue) - Number(b.gainValue)
					);
				}

			},
			// 走的排行榜數據
			getList() {
				this.$server
					.post("/parameter/top", {
						type: "twd",
					})
					.then((res) => {
						this.$refs.loading && this.$refs.loading.close();
						this.isLoading = false;
						this.loading = false;
						this.loading2 = false;
						this.list = res.data;
					});
			},
			getCategory(type) {
				this.$server.post("/parameter/category", {
					category: type
				}).then((res) => {
					if (res.status == 1) {
						this.categoryList = res.data;
						this.selIndex = this.categoryList[0].sectorId
						this.getMarkListStock(this.selIndex);
					}
					res.data.forEach((item, index) => {
						if (index < 6) {
							this.$server
								.post("/parameter/stockservices", {
									category: type,
									sectorId: item.sectorId,
									offset: 1,
								})
								.then((ras) => {
									ras.list.forEach((item2, index2) => {
										if (index2 < 1) {
											this.categoryList2.push(item2);
										}
									});
								});
						}
					});
				});
			},
			// 获取分类下的股票
			getMarkListStock(Id) {
				this.$refs.loading.open()
				let cate = this.categoryList.find(item => {
					return item.sectorId == Id
				})
				this.cyName = cate.name
				this.$server.post("/parameter/stockservices", {
						category: this.cyIndex,
						sectorId: Id,
						offset: 1,
					})
					.then((res) => {
						this.$refs.loading && this.$refs.loading.close();
						this.loading = false;
						this.loading2 = false;
						this.isLoading = false;
						this.cylist = res.list || [];
						console.log(this.cylist,565656)
					});
				this.showCy = false
			},
			changeNav(type) {
				// this.currmentIndex = type;
				if(type==1){
					this.$toPage('/favorite/index')
				}else{
					this.$toPage('/favorite/indexCopy')
				}
			},
			getIndexList() {
				this.$server.post("/parameter/zhishulist", {
					type: "twd"
				}).then((res) => {
					this.loading1 = false;
					this.indexList = res.data;
				});
			},
			onRefresh() {
				this.getIndexList();
				this.getCategory(this.cyTab[this.cyTabIndex].type);
				this.getList();
			},
			changeList(type) {
				if (type == 0) {
					this.show = !this.show;

					// 成交額 排序
					if (this.show) {
						this.list = this.list.sort(
							(a, b) => Number(b.volume) - Number(a.volume)
						);
					} else {
						this.list = this.list.sort(
							(a, b) => Number(a.volume) - Number(b.volume)
						);
					}
				} else {
					this.show1 = !this.show1;

					// 漲跌排序
					if (this.show1) {
						this.list = this.list.sort(
							(a, b) => Number(b.gainValue) - Number(a.gainValue)
						);
					} else {
						this.list = this.list.sort(
							(a, b) => Number(a.gainValue) - Number(b.gainValue)
						);
					}
				}
			},
			getNews() {
				this.$server
					.post("/common/newss", {
						exchange: "tw",
						lang: "cn",
					})
					.then((res) => {
						let arr = res.data.result;
						this.newList = arr
					})
			},
		},
	};
</script>

<style scoped lang="less">
	::-webkit-scrollbar {
		display: none;
	}

	::v-deep .van-skeleton__row {
		background-color: transparent !important;
	}

	::v-deep .van-skeleton__title {
		background-color: transparent !important;
	}
	.page {
		padding: 1.2rem 0 1rem;
		min-height: 100vh;
	}
	.top-fixed {
		width: 100%;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 999;
		padding: 0.2rem 0.12rem 0.1rem;
		background-color: #18191B;
		.searchBox{
			height: 0.31rem;
			background: #434446;
			border-radius: 0.16rem;
			padding: 0 0.1rem;
			.t{
				font-family: PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
				color: #888888;
			}
		}
	}
	.nav-box {
		.nav-item {
			margin-right: 0.2rem;
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 0.16rem;
			color: #7A7DB6;
			position: relative;
			line-height: 0.34rem;
			&::after {
				content: "";
				width: 50%;
				height: 0.04rem;
				position: absolute;
				border-radius: 0.3rem;
				bottom: 0;
				left: 50%;
				transform: translateX(-50%);
				background: transparent;
			}
	
			&.active {
				color: #FFFFFF;
				&::after {
					background: #fff;
				}
			}
		}
	}
	.rm {
		background: #FFFFFF;
		border-radius: 0.12rem 0.12rem 0.12rem 0.12rem;
		margin: 0 0.12rem;
		.change {
			padding: 0.1rem 0;

			.change-item {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 0.15rem;
				color: #666666;
				padding: 0.05rem 0;
				width: 30%;
				text-align: center;

				&.active {
					font-weight: 400;
					font-size: 0.15rem;
					color: #FFFFFF;
				}
			}
		}

		.tit {
			padding: 0.12rem;
			font-family: PingFang SC, PingFang SC;
			font-weight: 600;
			font-size: 0.16rem;
			color: #171717;
		}


		.rm-list {
			border-radius: 0.09rem 0.09rem 0rem 0rem;
			padding: 0 0.12rem;

			.titles {
				padding-bottom: 0.12rem;
				div {
					font-family: PingFang SC;
					font-weight: 500;
					font-size: 0.12rem;
					color: #8B8B8B;
				}
				.icon {
					margin-left: 0.05rem;
				}
			}
			.rm-item {
				padding: 0.12rem 0;
				border-bottom: 0.01rem solid #E0E0E0;
				.name {
					font-family: PingFang SC;
					font-weight: 500;
					font-size: 0.12rem;
					color: #333333;
				}
				.code {
					font-family: PingFang SC;
					font-weight: 500;
					font-size: 0.11rem;
					color: #B4B4B4;
				}

				.price {
					height: 0.23rem;
					border-radius: 0.03rem;
					padding: 0 0.05rem;
					font-family: PingFang SC;
					font-weight: 500;
					font-size: 0.12rem;
					color: #FFFFFF;
				}
				
				.icon {
					margin-right: 0.05rem;
				}
				
				.per {
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.14rem;
					color: #FFFFFF;
				}

				.down {
					margin-right: 0.1rem;
				}

			}
		}
	}
	.cyTab {
		margin: 0.12rem auto 0;
		height: 0.46rem;
		background: #232429;
		border-radius: 0.23rem;
		padding: 0.05rem;
		.cyTabItem {
			flex: 1;
			text-align: center;
			font-family: PingFang SC;
			font-weight: 500;
			font-size: 0.15rem;
			color: #999999;
			line-height: 0.36rem;
		}
		.cyTabItemd {
			flex: 1;
			background: #8DFD99;
			border-radius: 0.19rem;
			text-align: center;
			font-family: PingFang SC;
			font-weight: 500;
			font-size: 0.15rem;
			color: #010101;
			line-height: 0.36rem;
			position: relative;
			// &::after {
			// 	position: absolute;
			// 	content: '';
			// 	bottom: 0;
			// 	left: 0;
			// 	width: 100%;
			// 	height: 0.04rem;
			// 	background-color: #E5C79F;
			// }
		}
	}
	.cy {
		position: relative;
		margin: 0 0.12rem;
		background: #232429;
		border-radius: 0.23rem;
		padding: 0.12rem;
		.tt {
			.t {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 0.16rem;
				color: #FFFFFF;
			}
		
			.t1 {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 0.12rem;
				color: #FFFFFF;
			}
		}
		.title {
			height: 0.33rem;
			padding: 0;
			div {
				font-family: PingFang SC;
				font-weight: 500;
				font-size: 0.17rem;
				color: #FFFFFF;
			}
		}

		.cySearch {
			background: #0B0F1C;
			margin: 0 0.12rem;
			padding: 0 0.12rem;

			.name {
				padding: 0.12rem 0;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.14rem;
				color: rgba(255, 255, 255, 0.9);
				border-bottom: 0.01rem solid #000000;
			}
		}

		.cy-list {
			flex-wrap: wrap;
			margin-top: 0.1rem;
			background: #434446;
			border-radius: 0.12rem;
			.cy-item01 {
				width: 33%;
				text-align: center;
				padding: 0.12rem 0;
				&:last-child {
					border-bottom: none;
				}
				.name {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 0.14rem;
					color: #fff;
				}
			
			}
			.zhuzi {
				width: 11%;
				text-align: center;
				border-radius: 0.04rem 0.04rem 0.04rem 0.04rem;
				margin-bottom: 0.1rem;
				padding: 0.1rem 0;
				display: flex;
				align-items: center;
				justify-content: center;
				flex-direction: column;

				.name {
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 0.11rem;
					color: #FFFFFF;
				}

				.price {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 0.16rem;
					padding: 0.1rem 0;
				}

				.icon {
					margin-right: 0.05rem;
				}

				.per {
					font-size: 0.12rem;
				}
			}
			
		}
		.cy-list2{
			margin: 0.12rem 0 0;
			.cy-item {
				padding: 0.1rem 0;
				.name {
					font-family: PingFang SC;
					font-weight: 500;
					font-size: 0.17rem;
					color: #999999;
				}
			
				.price {
					margin-top: 0.05rem;
					font-family: PingFang SC;
					font-weight: 500;
					font-size: 0.17rem;
				}
				.per {
					text-align: center;
					margin-top:-0.25rem;
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 0.13rem;
				}
			}
		}
	}

	.zx-cot {
		margin: 0.12rem;
		overflow-x: scroll;
		.zx-list {
			width: 280%;
			.zx-item {
				margin-right: 0.1rem;
				width: 32%;
				padding: 0.12rem;
				background: #FFFFFF;
				border-radius: 0.04rem 0.04rem 0.04rem 0.04rem;
				.name {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 0.12rem;
					color: #4C4C4C;
				}
				.price {
					margin: 0.06rem 0;
					font-family: DIN Next LT Pro, DIN Next LT Pro;
					font-weight: bold;
					font-size: 0.18rem;
				}
	
				.per {
					height: 0.26rem;
					line-height: 0.26rem;
					border-radius: 0.02rem 0.02rem 0.02rem 0.02rem;
					padding: 0 0.05rem;
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 0.12rem;
					color: #FFFFFF;
				}
	
			}
		}
	}
	.news {
		padding-top: 0.1rem;

		.newsList {
			margin: 0 0.12rem;

			.newItem {
				background: #FFFFFF;
				box-shadow: 0rem 0.02rem 0.04rem 0rem rgba(0, 0, 0, 0.17);
				border-radius: 0.04rem;
				margin-bottom: 0.1rem;
				padding: 0.12rem;

				.time {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.11rem;
					color: #999999;
				}

				.tit {
					margin-top: 0.05rem;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					-webkit-box-orient: vertical;
					overflow: hidden;
					text-overflow: ellipsis;
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.14rem;
					color: #666666;
				}
			}
		}
	}

	.navs {
		margin-top: -0.41rem;
		padding: 0 0.1rem;

		.navItem {
			width: 49%;
			height: 0.41rem;
			background: #D8D8D8;
			border-radius: 0.09rem 0.09rem 0rem 0rem;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 0.16rem;
			color: #333333;
			line-height: 0.41rem;
		}

		.active {
			color: #002F7C;
			background: #FFFFFF;
		}
	}

	.btn-box {
		padding: 0.2rem 0.1rem;
		height: 0.38rem;
		background: #8DFD99;
		border-radius: 0.19rem;
		font-family: PingFang SC;
		font-weight: 500;
		font-size: 0.15rem;
		color: #000000;
		.b-btn {
			margin: 0;
		}
	}

	.index {
		.t {
			font-weight: 500;
			color: #1e1e1e;
			padding: 0 0.1rem 0.1rem;
		}
	}

	.title {
		padding: 0 0.1rem;

		div {
			font-weight: 600;
			font-size: 0.16rem;
			color: #000000;
		}
	}

	.cot {
		.list {
			.titles {
				padding: 0.1rem 0.1rem 0;

				div {
					font-size: 0.12rem;
					color: #535353;
				}
			}

			.list-item {
				padding: 0.1rem;
				border-bottom: 0.01rem solid #f4f4f4;

				.wxz {
					margin-right: 0.05rem;
				}

				.name {
					font-size: 0.12rem;
					color: #000000;
				}

				.code {
					font-size: 0.1rem;
					color: #c4c4c4;
				}

				.price {
					font-size: 0.12rem;
					color: #0c0c0c;
					text-align: center;
				}

				.per {
					.t {
						font-size: 0.12rem;
						color: #0c0c0c;

						&.t1 {
							margin-left: 0.1rem;
						}
					}
				}
			}
		}
	}

	.btns {
		margin: 0.2rem 0.1rem;
		position: relative;

		&.bt {
			.btn {
				width: 100%;
			}

			&::after {
				display: none;
			}
		}

		&::after {
			content: "";
			position: absolute;
			width: 0.02rem;
			height: 50%;
			background-color: #888888;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
		}

		.btn {
			width: 48%;
			font-weight: 500;
			text-align: center;
			padding: 0.1rem 0;
			font-size: 0.12rem;
			color: #888888;

			.icon {
				margin-right: 0.05rem;
			}
		}
	}
</style>