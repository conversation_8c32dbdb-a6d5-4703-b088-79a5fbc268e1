<template>
	<div class="tab">
		<div class="flex flex-b">
			<div class="item" :class="{ active: current == 0 }" @click="$toPage('/home/<USER>')">
				<div class="bg">
					<div class="icon a animate__animated animate__fadeIn" :class="{ ac: current == 0 }"></div>
				</div>
				<div class="t">首頁</div>
			</div>
			<div class="item" :class="{ active: current == 3 }" @click="$toPage('/favorite/index')">
				<div class="bg">
					<div class="icon a1 animate__animated animate__fadeIn" :class="{ ac1: current == 3 }"></div>
				</div>
				<div class="t">行情</div>
			</div>
			<!-- 自选 -->
			<!-- <div class="item" :class="{ active: current == 5 }" @click="$toPage('/favorite/indexCopy')">
				<div class="icon a2 animate__animated animate__fadeIn" :class="{ ac2: current == 5 }"></div>
				<div class="t">自選</div>
			</div> -->
			<div class="item" :class="{ active: current == 5 }" @click="$toPage('/home/<USER>')">
				<div class="bg">
					<div class="icon a2 animate__animated animate__fadeIn" :class="{ ac2: current == 5 }"></div>
				</div>
				<div class="t">新股申購</div>
			</div>
			<!-- 新闻 -->
			<!-- <div class="item" :class="{ active: current == 1 }" @click="$toPage('/home/<USER>')">
				<div class="icon a3 animate__animated animate__fadeIn" :class="{ ac3: current == 1 }"></div>
				<div class="t">資訊</div>
			</div> -->
			<!-- 持仓 -->
			<div class="item" :class="{ active: current == 2 }" @click="$toPage('/trade/index')">
				<div class="bg">
					<div class="icon a3 animate__animated animate__fadeIn" :class="{ ac3: current == 2 }"></div>
				</div>
				<div class="t">交易</div>
			</div>
			<!-- <div class="item" :class="{ active: current == 4 }" @click="$toPage('/market/largeMarket')">
				<div class="icon a4 animate__animated animate__fadeIn" :class="{ ac4: current == 4 }"></div>
				<div class="t">大盤</div>
			</div> -->
			<!-- 钱包 -->
			<!-- <div class="item" :class="{ active: current == 4 }" @click="$toPage('/information/payBag')">
				<div class="icon a3 animate__animated animate__fadeIn" :class="{ ac3: current == 4 }"></div>
				<div class="t">資產</div>
			</div> -->
			<div class="item" :class="{ active: current == 4 }" @click="$toPage('/information/index')">
				<div class="bg">
					<div class="icon a4 animate__animated animate__fadeIn" :class="{ ac4: current == 4 }"></div>
				</div>
				<div class="t">我的</div>
			</div>
		</div>
	</div>
</template>

<script>
	export default {
		name: "tab-bar",
		props: {
			current: {
				type: Number,
				default: "",
			},
		},
		data() {
			return {};
		},
		components: {},
		methods: {},
		created() {},
		computed: {},
	};
</script>

<style scoped lang="less">
	.tab {
		background: #18191B;
		box-shadow: 0rem 0rem 0rem 0rem rgba(127,127,127,0.6);
		width: 100%;
		position: fixed;
		bottom: 0;
		left: 0;
		z-index: 999;
		.item {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			padding: 0.05rem 0 0.1rem;
			text-align: center;
			flex: 1;
			&.active {
				.bg{
					background: #8DFD99;
				}
				.t {
					color: #8DFD99;
				}
			}

			.icon {
				margin: 0 auto;
			}
			.bg{
				display: flex;
				align-items: center;
				justify-content: center;
				width: 0.42rem;
				height: 0.42rem;
				background: transparent;
				border-radius: 50%;
			}
			.t {
				margin-top: 0.02rem;
				font-family: PingFang SC;
				font-weight: 400;
				font-size: 0.12rem;
				color: #999999;
			}
		}
	}
</style>