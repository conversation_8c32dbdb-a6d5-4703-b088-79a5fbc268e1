<template>
	<div class="position">
		<topCom></topCom>
		<!-- <div class="header flex flex-c">
			<div class="headImg">
				<img src="../../assets/skin/home/<USER>" />
			</div>
			<div class="search flex flex-1 flex-c" @click="clickNext('/home/<USER>')">
				{{$t('header').txt1}}
			</div>
			<div class="ico" @click="clickNext('/home/<USER>')">
				<img src="../../assets/skin/home/<USER>" />
			</div>
		</div> -->
		<div class="ggimg" @click="showG = false" v-if="showG">
			<div class="icon close02"></div>
		</div>
		<div class="page-card">
			<!-- 资产 -->
			<div class="flex flex-b item">
				<div class="txt02">{{ $t("mine").money.txt1 }}</div>
				<!--        <div-->
				<!--          class="txt"-->
				<!--          v-if="-->
				<!--            (parseFloat(userInfo2.jpy) || 0 + parseFloat(sz) || 0) >-->
				<!--              100000000-->
				<!--          "-->
				<!--        >-->
				<!--          {{-->
				<!--            $formatMoney(-->
				<!--              (parseFloat(userInfo2.jpy) || 0 + parseFloat(sz) || 0) /-->
				<!--              100000000-->
				<!--            )-->
				<!--          }}{{ $t("position").txt4 }}-->
				<!--        </div>-->
				<div class="txt">
					{{ $formatMoney(parseFloat(userInfo2.jpy) + parseFloat(sz)) }}円
				</div>
			</div>
			<div class="flex flex-b item">
				<div class="txt02">{{ $t("mine").money.txt3 }}</div>
				<div class="txt">{{ $formatMoney(userInfo2.jpy) }}円</div>
			</div>
			<div class="item flex flex-b" v-if="false">
				<div class="txt02">{{ $t("mine").money.txt5 }}</div>
				<div class="txt">{{ $formatMoney(userInfo2.usd) }}円</div>
			</div>
			<div class="item flex flex-b">
				<div class="txt02">
					{{ currmentIndex === 2 ? $t("position").data4 : $t("position").data5 }}
				</div>
				<!--        <div class="txt" v-if="sz > 100000000">-->
				<!--          {{ $formatMoney(sz / 100000000) }}{{ $t("position").txt4 }}-->
				<!--        </div>-->
				<div class="txt">{{ $formatMoney(sz) }}円</div>
			</div>
			<div class="item flex flex-b">
				<div class="txt02">
					{{ currmentIndex === 2 ? $t("position").data2 : $t("position").data3 }}
				</div>
				<div v-if="currmentIndex === 2">
					<!-- 已结束 -->
					<!--          <div class="txt" v-if="Number(lsyk) > 100000000">-->
					<!--            {{ $formatMoney(Number(lsyk) / 100000000)-->
					<!--            }}{{ $t("position").txt4 }}-->
					<!--          </div>-->
					<div class="txt">
						{{ $formatMoney(Number(lsyk)) }}円
					</div>
				</div>
				<div v-else-if="currmentIndex === 1">
					<!-- 进行中 -->
					<!--          <div class="txt" v-if="Number(fdyk) > 100000000">-->
					<!--            {{ $formatMoney(Number(fdyk) / 100000000)-->
					<!--            }}{{ $t("position").txt4 }}-->
					<!--          </div>-->
					<div class="txt">
						{{ $formatMoney(Number(fdyk)) }}円
					</div>
				</div>
				<div v-else>
					<div class="txt">-</div>
				</div>
			</div>
		</div>

		<div class="pageNavBox">
			<div class="pageNav">
				<div class="nav-item" v-for="(item, idx) in navList" :key="idx"
					:class="{ active: currmentIndex === item.type }" @click="changeNav(item.type)">
					{{ item.name }}
				</div>
			</div>
		</div>
		<div class="abox" v-if="currmentIndex === 1">
			<div class="abox-list" v-for="(item, index) in positionList" :key="index">
				<div class="abox-title">
					<div class="abox-name flex">
						<div>{{ item.stock_name }}</div>
						<div class="code flex">{{ item.stock_code }}</div>
					</div>
					<div class="abox-have">{{ $t(statusStr[item.status]) }}</div>
				</div>
				<div class="abox-foot">
					<div class="foot-item">
						<div class="abox-foot-title">{{ $t("position").txt6 }}</div>
						<div class="abox-foot-list">
							{{ $t("position").txt7 }}<span>{{ $formatMoney(item.stock_num,0) }}</span>
						</div>
						<div class="abox-foot-list">
							{{ $t("position").txt8 }}<span>{{ $formatMoney(item.buy_price) }}円</span>
						</div>
						<div class="abox-foot-list">
							{{ $t("position").txt9
              }}<span>{{
                $formatMoney(fuwu(item.market_value))
              }}円</span>
						</div>
						<div class="abox-foot-list">
							{{ $t("position").txt10 }}<span>{{ $formatMoney(item.market_value) }}円</span>
						</div>
						<div class="abox-foot-list">
							{{ $t("position").txt11
              }}<span class="text-right">{{
                $formatDate("DD/MM/YY hh:mm", item.buy_time*1000)
              }}</span>
						</div>
					</div>
					<div class="foot-item">
						<div class="abox-foot-title">{{ $t("position").txt12 }}</div>
						<div class="abox-foot-list">
							{{ $t("position").txt13
              }}<span class="txt2">{{ $formatMoney(item.stock_num,0) }}</span>
						</div>
						<div class="abox-foot-list">
							{{ $t("position").txt14
              }}<span class="txt2">{{ $formatMoney(item.nowprice) }}円</span>
						</div>
						<div class="abox-foot-list">
							{{ $t("position").txt15
              }}<span class="txt2" :class="{
                  red: parseFloat(item.yingkui) < 0,
                  green: parseFloat(item.yingkui) >= 0,
                }">{{ $formatMoney(item.gain) }}%</span>
						</div>
						<div class="abox-foot-list">
							{{ $t("position").txt16
              }}<span class="txt2">{{
                $formatMoney(item.nowprice * item.stock_num)
              }}円</span>
						</div>
						<div class="abox-foot-list">
							{{ $t("position").txt17
              }}<span class="txt2">{{ $formatMoney(item.yingkui) }}円</span>
						</div>
					</div>
				</div>
				<div class="abox-bottom">
					<div class="btn btn1" @click="goItem(item)">
						{{ $t("position").txt22 }}
					</div>
					<div class="btn btn2" @click="sellItem(item)">
						{{ $t("position").txt1 }}
					</div>
					<div class="btn btn2" @click="cancelItem(item)">
						{{ $t("position").txt23 }}
					</div>
				</div>
			</div>
		</div>
		<div class="abox" v-if="currmentIndex === 2">
			<div class="abox-list" v-for="(item, index) in positionCloseList" :key="index">
				<div class="abox-title">
					<div class="abox-name">
						{{ item.stock_name }}<span>{{ item.stock_code }}</span>
					</div>
					<div class="abox-not">{{ $t(statusStr[item.status]) }}</div>
				</div>
				<div class="abox-foot">
					<div class="foot-item">
						<div class="abox-foot-title">{{ $t("position").txt6 }}</div>
						<div class="abox-foot-list">
							{{ $t("position").txt7 }}<span>{{ $formatMoney(item.stock_num) }}</span>
						</div>
						<div class="abox-foot-list">
							{{ $t("position").txt8 }}<span>{{ $formatMoney(item.buy_price) }}円</span>
						</div>
						<div class="abox-foot-list">
							{{ $t("position").txt9
              }}<span>{{
                $formatMoney(fuwu(item.market_value))
              }}円</span>
						</div>
						<div class="abox-foot-list">
							{{ $t("position").txt10 }}<span>{{ $formatMoney(item.market_value) }}円</span>
						</div>
						<div class="abox-foot-list">
							{{ $t("position").txt11
              }}<span class="text-right">{{
                $formatDate("DD/MM/YY hh:mm", item.buy_time*1000)
              }}</span>
						</div>
					</div>
					<div class="foot-item">
						<div class="abox-foot-title">{{ $t("position").txt12 }}</div>
						<div class="abox-foot-list">
							{{ $t("position").txt13
              }}<span class="txt2">{{ $formatMoney(item.zhang, 0) }}</span>
						</div>
						<div class="abox-foot-list">
							{{ $t("position").txt18
              }}<span class="txt2">{{ $formatMoney(item.sell_price) }}円</span>
						</div>
						<div class="abox-foot-list">
							{{ $t("position").txt19
              }}<span class="txt2" :class="{
                  red: parseFloat(item.yingkui) < 0,
                  green: parseFloat(item.yingkui) >= 0,
                }">{{ $formatMoney(item.gain) }}%</span>
						</div>
						<div class="abox-foot-list">
							{{ $t("position").txt20
              }}<span class="txt2">{{
                $formatMoney(item.sell_price * item.stock_num)
              }}円</span>
						</div>
						<div class="abox-foot-list">
							{{ $t("position").txt21
              }}<span class="txt2">{{ $formatMoney(item.yingkui) }}円</span>
						</div>
					</div>
				</div>
				<div class="abox-bottom">
					<div class="btn btn1" @click="goItem(item)">
						{{ $t("position").txt24 }}
					</div>
					<div class="btn btn2">{{ $t("position").txt25 }}</div>
				</div>
			</div>
		</div>
		<!-- 申购 -->
		<div class="shengou" v-if="currmentIndex === 3">
			<div class="link flex flex-c" @click="clickNext('/market/subscribe')">
				<!-- <img src="../../assets/skin/market/<EMAIL>" /> -->
				{{ $t("position").txt5 }}
			</div>

			<div class="link flex flex-c" @click="clickTips('qc')">
				{{ $t("立即参与机构抢筹") }}
			</div>
			<div class="link flex flex-c" @click="clickTips('dz')">
				{{ $t("立即参与大宗交易") }}
			</div>

			<template v-if="xinguList.length">
				<div class="list" v-for="(item, index) in xinguList" :key="index" v-if="xinguList.length">
					<div class="name flex flex-b">
						<div class="flex">
							<div>{{ item.stock_name }}</div>
							<div class="code flex">{{ item.stock_code }}</div>
						</div>
						<div class="state">{{ $t(item.xgstate) }}</div>
					</div>
					<div class="itemBox flex flex-b flex-wrap">
						<div class="item flex flex-b">
							<div>{{ $t("position").txt41 }}</div>
							<span>{{ $formatMoney(item.apply_total, 0) }}</span>
						</div>
						<div class="item flex flex-b">
							<div>{{ $t("position").txt38 }}</div>
							<span>{{ $formatMoney(item.lucky_total, 0) }}</span>
						</div>
						<div class="item flex flex-b">
							<div>{{ $t("position").txt42 }}</div>
							<span class="txt2">{{
                  $formatMoney(item.rjmoney)
              }}円</span>
						</div>
						<div class="item flex flex-b">
							<div>{{ $t("position").p1 }}</div>
							<span class="txt2">{{
                  $formatMoney(item.apply_price)
              }}円</span>
						</div>
					</div>
				</div>
			</template>
			<template v-else>
				<div class="noData text-center">{{ $t("other").txt23 }}</div>
			</template>
		</div>
		<!-- 跟单 -->
		<div class="fllow" v-if="currmentIndex === 4">
			<template v-if="xinguList.length">
				<div class="list" v-for="(item, index) in xinguList" :key="index" v-if="xinguList.length">
					<div class="name flex flex-b">
						<div class="txt1 flex">
							{{ item.name }}<span class="flex">{{ item.code }}</span>
						</div>
						<div class="txt2 flex">
							{{ $t("position").f2 }}
							<span
								:class="{ red: item.aprofit >= 0, green: item.aprofit < 0 }">{{ $formatMoney(item.aprofit) }}円</span>
						</div>
					</div>
					<div class="time flex flex-b">
						<div class="item flex flex-b">
							<div>{{ $t("position").txt11 }}</div>
							<span id="">{{
                  $formatDate("DD/MM/YY hh:mm", item.buy_time*1000)
                }}</span>
						</div>
						<div class="item flex flex-b">
							<div>{{ $t("position").f5 }}</div>
							<span>{{ item.scday }}</span>
						</div>
					</div>
				</div>
			</template>
			<template v-else>
				<div class="noData text-center">{{ $t("other").txt23 }}</div>
			</template>
		</div>
		<bottom :on="2"></bottom>
	</div>
</template>
<script type="text/javascript">
	import Vue from "vue";
	import qs from "qs";
	import axios from "axios";
	import {
		Dialog,
		Toast
	} from "vant";
	Vue.use(Dialog).use(Toast);
	import bottom from "../bar/bottomnav.vue";
	import topCom from "../bar/topCom.vue";

	export default {
		name: "position",
		data() {
			return {
				navList: [{
						name: this.$t("position").txt26,
						type: 3,
					},
					// {
					// 	name: this.$t('position').tab3,
					// 	type: 4
					// },
					/* {name: this.$t('home').top1,type: 5}, */
					{
						name: this.$t("position").tab4,
						type: 1,
					},
					{
						name: this.$t("position").tab5,
						type: 2,
					},
				],
				currmentIndex: 1,
				xinguList: [],
				positionList: [],
				positionCloseList: [],
				userInfo2: {},
				sz: 0,
				fdyk: 0,
				lsyk: 0,
				positionlistinteval: null,
				statusStr: [
					this.$t("position").txt28,
					this.$t("position").txt29,
					this.$t("position").txt31,
					this.$t("position").txt32,
				],
				statusStrCancel: [
					this.$t("position").txt28,
					this.$t("position").txt30,
					this.$t("position").txt31,
					this.$t("position").txt32,
				],
				config: {},
				popNote: "",
				confirmText: "",
				cancelText: "",
				showG: true,
			};
		},
		components: {
			bottom,
			topCom,
		},
		computed: {},
		beforeDestroy() {
			var _this = this;
			clearInterval(_this.positionlistinteval);
		},
		mounted() {
			this.getConfig();
			this.getUserInfo();
			this.changeNav(this.currmentIndex);
		},
		methods: {
			clickTips(type) {
				// 点击大宗提示
				if (type == "dz") {
					if (this.userInfo2.jpy < 10000000) {
						Toast({
							message: this.$t("资金未达到1000万日元无法完成"),
							duration: 2000,
						});
						return;
					}

					this.$router.push({
						path: "/common/dividend?type=1",
					});
					return;
				}
				// 点击抢筹提示
				if (type == "qc") {
					if (this.userInfo2.jpy < 5000000) {
						Toast({
							message: this.$t("资金未达到500万日元无法完成"),
							duration: 2000,
						});
						return;
					}

					this.$router.push({
						path: "/common/dividend3",
					});
					return;
				}
			},
			changeNav(index) {
				var _this = this;
				this.currmentIndex = index;
				if (this.currmentIndex == 1) {
					this.initData();
					clearInterval(_this.positionlistinteval);
					this.positionlistinteval = setInterval(() => {
						_this.initData();
					}, 5000);
				} else if (this.currmentIndex == 2) {
					this.getPositionCloseList();
					clearInterval(_this.positionlistinteval);
					this.positionlistinteval = setInterval(() => {
						_this.getPositionCloseList();
					}, 5000);
				} else if (this.currmentIndex == 3) {
					this.getXingu();
					clearInterval(_this.positionlistinteval);
				} else if (this.currmentIndex == 4) {
					//this.getXingu();
					this.getGengDan();
					clearInterval(_this.positionlistinteval);
				}
			},
			getXingu() {
				let type = 0;
				if (this.currmentIndex == 3) {
					type = 0;
				}
				if (this.currmentIndex == 4) {
					type = 1;
				}
				this.$server
					.post("/trade/usernewstocklist", {
						buy_type: type,
						type: 'jpy'
					})
					.then((res) => {
						if (res.data.status === 1) {
							this.xinguList = res.data.data;
						}
					});
			},
			getGengDan() {
				this.$server.post("/trade/userproductlist", {
					type: 'jpy'
				}).then((res) => {
					if (res.data.status === 1) {
						this.xinguList = res.data.data;
					}
				});
			},
			getUserInfo() {
				this.$server.post("/user/getUserinfo", {}).then((res) => {
					if (res.data.status === 1) {
						this.userInfo2 = res.data.data;
						this.initData();
					}
				});
			},
			getPositionCloseList() {
				let that = this;
				this.$server
					.post("/trade/userstocklists", {
						type: 'jpy'
					})
					.then((res) => {
						if (res.data.status == 1 || res.data.status == "1") {
							this.lsyk = 0;
							this.positionCloseList = res.data.data;
							let length = this.positionCloseList.length
							let a
							for (a = 0; a < length; a++) {
								this.lsyk += parseFloat(this.positionCloseList[a].yingkui)
							}
						}
					});
			},
			initData() {
				this.$server
					.post("/trade/userstocklist", {
						type: 'jpy'
					})
					.then((res) => {
						if (res.data.status == 1 || res.data.status == "1") {
							this.fdyk = 0
							this.sz = 0;
							this.positionList = res.data.data;
							let length = this.positionList.length
							let a
							for (a = 0; a < length; a++) {
								this.fdyk += parseFloat(this.positionList[a].yingkui)
								this.sz += parseFloat(this.positionList[a].stock_num) * parseFloat(this.positionList[a]
									.nowprice)
							}
						}
					});
			},
			fuwu(value) {
				let val = value * Number(this.config.buycharge);
				if (val < this.config.minshouxu) {
					return Number(this.config.minshouxu);
				}
				return val;
			},
			getConfig() {
				this.$server.post("/common/config", {
					type: 'jpy'
				}).then((res) => {
					let list = res.data.data;
					let listLength = list.length;
					let val = {};
					for (var a = 0; a < listLength; a++) {
						var row = list[a];
						val[row.name] = row.value;
						val[row.name + "_zh"] = row.title;
					}
					this.config = val;
				});
			},
			goItem(item) {
				if (item.nowprice) {
					this.clickNext(
						"/position/positionDetail?id=" +
						item.id +
						"&nowprice=" +
						item.nowprice +
						"&gain=" +
						item.gain +
						"&gainValue=" +
						item.yingkui
					);
				} else {
					this.clickNext(
						"/position/positionDetail?id=" +
						item.id +
						"&gain=" +
						item.gain +
						"&gainValue=" +
						item.yingkui
					);
				}
			},
			sellItem(item) {
				// 平仓
				Dialog.confirm({
						title: this.$t("position").txt36,
						message: this.$t("position").txt3,
						confirmButtonText: this.$t("position").txt1,
						cancelButtonText: this.$t("position").txt35,
						showCancelButton: true,
					})
					.then(() => {
						this.sellstrategy(item.id);
					})
					.catch(() => {
						// on cancel
					});
			},
			sellstrategy(id) {
				this.$server
					.post("/trade/sell_stock", {
						id,
						type: 'jpy'
					})
					.then((res) => {
						if (res.data.status == 1) {
              Toast({
                message: this.$t(res.data.msg),
                duration: 2000,
              });
							this.initData();
						}
					});
			},
			cancelItem(item) {
				//撤单
				Dialog.confirm({
						title: this.$t("position").txt36,
						message: this.$t("position").txt27,
						confirmButtonText: this.$t("position").txt23,
						cancelButtonText: this.$t("position").txt35,
						showCancelButton: true,
					})
					.then((res) => {
						this.cancelstrategy(item.id);
					})
					.catch(() => {
						// on cancel
					});
			},
			cancelstrategy(id) {
				this.$server
					.post("/trade/cancel_stock", {
						id,
						type: 'jpy'
					})
					.then((res) => {
						if (res.data.status === 1) {
							this.initData();
						}
						if (res.data.msg) {
							Toast({
								message: this.$t(res.data.msg),
								duration: 2000,
							});
						}
					});
			},
		},
	};
</script>
<style lang="less">
	.position {
		background: #0f161c;
		position: relative;
		height: 100vh;
		overflow: hidden;

		.header {
			width: 100%;
			height: 0.64rem;
			background: #fff;
			font-weight: 500;
			font-size: 0.18rem;
			color: #000000;

			.headImg {
				margin-left: 0.12rem;

				img {
					width: 0.38rem;
					display: block;
				}
			}

			.ico {
				margin-right: 0.12rem;

				img {
					width: 0.32rem;
					display: block;
				}
			}

			.search {
				height: 0.38rem;
				background: rgba(227, 227, 227, 0.54);
				border-radius: 0.19rem;
				font-size: 0.15rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #c1c4c4;
				margin: 0 0.18rem;
			}
		}

		.ggimg {
			position: fixed;
			bottom: 0.6rem;
			width: 100%;
			height: 0.7rem;
			background: url("../../assets/v2/xwbg.png") no-repeat center/100%;

			.close02 {
				margin-left: 3.5rem;
				margin-top: 0.1rem;
			}
		}

		.pageNavBox {
			position: sticky;
			top: 0.3rem;
			overflow: hidden;
			border-radius: 0.05rem;
			margin: 0.05rem 0.12rem;

			.pageNav {
				background: #1b232a;
				border-radius: 0.1rem;
				padding: 0.05rem;
				overflow-x: scroll;
				overflow-y: hidden;
				white-space: nowrap;
				text-align: center;

				.nav-item {
					// width: 25%;
					padding: 0.05rem 0.13rem;
					font-size: 0.14rem;
					font-family: FZLanTingHeiT-R-GB;
					font-weight: 400;
					color: #a4a4af;
					display: inline-flex;
					align-items: center;
					justify-content: center;
					flex-direction: column;
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.14rem;
					color: #b3b8b8;

					&.active {
						background: #5ed5a8;
						border-radius: 0.1rem;
						color: #333333;
					}

					&.active:after {
						background: #a8ec7a;
						border-radius: 0.02rem;
					}
				}
			}
		}

		.page-card {
			background: #ffffff;
			border-radius: 0.05rem;
			margin: 0.05rem 0.12rem;
			padding: 0 0.1rem;

			.item {
				border-bottom: 0.01rem solid rgba(0, 0, 0, 0.2);
				line-height: 0.4rem;

				&:last-child {
					border-bottom: none;
				}

				.txt02 {
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.12rem;
					color: #333333;
				}

				.txt {
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.13rem;
					color: #333333;
				}
			}
		}

		.abox {
			height: calc(100vh - 3.6rem);
			overflow: scroll;
			padding-bottom: 1.2rem;

			.abox-title {
				padding: 0.05rem 0.12rem;
				background-color: rgba(94, 213, 168, 0.2);
				display: flex;
				align-items: center;
				justify-content: space-between;
				border-bottom: 0.03rem solid #fff;
			}

			.abox-name {
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 0.14rem;
				color: #ffffff;
				align-items: flex-end;

				.txt {
					color: #ef9d1c;
					font-size: 0.14rem;
					border-radius: 0.02rem;
					border: 1px solid #ef9d1c;
					height: 0.18rem;
					padding: 0 0.04rem;
					margin-left: 0.05rem;
				}

				.code {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.12rem;
					color: #ffffff;
					margin-left: 0.05rem;
				}
			}

			.abox-have {
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 0.13rem;
				color: #5ed5a8;
			}

			.abox-not {
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 0.13rem;
				color: #999999;
			}

			.abox-foot {
				display: flex;
				// align-items: center;
				padding: 0.1rem;
				justify-content: space-between;

				.foot-item {
					width: calc(50% - 0.14rem);
				}

				.abox-foot-title {
					font-family: PingFangSC, PingFang SC;
					font-weight: 600;
					font-size: 0.12rem;
					color: #ffffff;
				}

				.abox-foot-list {
					display: flex;
					align-items: center;
					justify-content: space-between;
					width: 100%;min-height: .34rem;;
					font-family: PingFangSC, PingFang SC;
					font-weight: 600;
					font-size: 0.11rem;
					color: #718a94;
					//line-height: 0.34rem;
					border-bottom: 0.01rem solid rgba(255, 255, 255, 0.2);
					//padding: .1rem 0;
					span {
						font-family: PingFangSC, PingFang SC;
						font-weight: 600;
						font-size: 0.11rem;
						color: #ffffff;
					}

					.txt1 {
						font-size: 0.1rem;
					}

					.txt2 {
						font-size: 0.14rem;
						text-align: right;
					}

					.red {
						color: #fe0000;
					}

					.green {
						color: #5ed5a8;
					}
				}

				.foot-item:last-child {
					.abox-foot-list {
						span {
							color: #c62f1f;
						}

						.txt1 {
							font-size: 0.1rem;
						}

						.txt2 {
							font-size: 0.14rem;
						}

						.red {
							color: #fa2256;
						}

						.green {
							color: #19c09a;
						}
					}
				}
			}

			.abox-bottom {
				margin: 0.05rem 0.12rem 0.1rem;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.btn {
					width: 30%;
					height: 0.29rem;
					border-radius: 0.05rem;
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.12rem;
					color: #b3b8b8;
				}

				.btn1 {
					background: #5ed5a8;
					color: #000;
					display: flex;
					align-items: center;
					justify-content: center;
				}

				.btn2 {
					background: rgba(94, 213, 168, 0.06);
					border-radius: 0.05rem;
					border: 0.01rem solid rgba(255, 255, 255, 0.2);
					display: flex;
					align-items: center;
					justify-content: center;
				}
			}
		}

		.shengou {
			height: calc(100vh - 3.6rem);
			overflow: scroll;
			padding-bottom: 1.2rem;

			.link {
				height: 0.4rem;
				background: #5ed5a8;
				border-radius: 0.05rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 0.14rem;
				color: #333333;
				margin: 0.12rem;
				line-height: 0.4rem;

				img {
					width: 0.22rem;
					margin-right: 0.08rem;
				}
			}

			.list {
				margin-bottom: 0.15rem;

				.name {
					padding: 0.05rem 0.12rem;
					background-color: rgba(94, 213, 168, 0.2);
					display: flex;
					align-items: center;
					justify-content: space-between;
					border-bottom: 0.03rem solid #fff;
					font-family: PingFangSC, PingFang SC;
					font-weight: 600;
					font-size: 0.14rem;
					color: #ffffff;

					.code {
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 0.12rem;
						color: #ffffff;
						margin-left: 0.05rem;
					}

					.state {
						font-family: PingFangSC, PingFang SC;
						font-weight: 600;
						font-size: 0.13rem;
						color: #5ed5a8;
					}
				}

				.itemBox {
					margin: 0.05rem 0.12rem;

					.item {
						width: 48%;
						font-family: PingFangSC, PingFang SC;
						font-weight: 600;
						font-size: 0.11rem;
						color: #718a94;
						line-height: 0.34rem;
						border-top: 0.01rem solid rgba(255, 255, 255, 0.2);

						&:first-child {
							border-top: none;
						}

						&:nth-child(2) {
							border-top: none;
						}

						span {
							font-family: PingFangSC, PingFang SC;
							font-weight: 600;
							font-size: 0.11rem;
							color: #ffffff;
						}
					}
				}
			}
		}

		.fllow {
			padding-bottom: 1.2rem;

			.list {
				.name {
					padding: 0.05rem 0.12rem;
					background-color: rgba(94, 213, 168, 0.2);
					display: flex;
					align-items: center;
					justify-content: space-between;
					border-bottom: 0.03rem solid #fff;

					.txt1 {
						font-family: PingFangSC, PingFang SC;
						font-weight: 600;
						font-size: 0.14rem;
						color: #ffffff;

						span {
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							font-size: 0.12rem;
							color: #ffffff;
							margin-left: 0.05rem;
						}
					}

					.txt2 {
						font-weight: 400;
						font-size: 0.12rem;
						color: #a9a9a9;

						span {
							font-weight: 400;
							font-size: 0.16rem;
							margin-left: 0.1rem;
						}
					}
				}

				.time {
					margin: 0.1rem 0.12rem;
					font-weight: 400;
					font-size: 0.14rem;
					color: #999999;
					line-height: 0.23rem;

					.item {
						width: 48%;
						font-family: PingFangSC, PingFang SC;
						font-weight: 600;
						font-size: 0.11rem;
						color: #718a94;
						line-height: 0.34rem;
						border-bottom: 0.01rem solid rgba(255, 255, 255, 0.2);

						span {
							font-family: PingFangSC, PingFang SC;
							font-weight: 600;
							font-size: 0.11rem;
							color: #ffffff;
						}
					}
				}
			}
		}

		.noData {
			padding-top: 0.5rem;
			font-size: 0.12rem;
			color: #999;
		}

		.popNote {
			width: 90vw;
			min-height: 2.34rem;
			background: #fff;
			border-radius: 0.12rem;

			.title {
				width: 100%;
				height: 0.68rem;
				background: #a8ec7a;
				border-radius: 0.12rem 0.12rem 0 0;
				font-weight: 500;
				font-size: 0.18rem;
				color: #333333;
			}

			.itemBox {
				min-height: 1.17rem;
				font-weight: 400;
				font-size: 0.16rem;
				color: #333333;
			}

			.Btn {
				padding: 0 0.11rem 0.16rem 0.11rem;

				.link {
					width: 1.37rem;
					height: 0.42rem;
					border-radius: 0.21rem;
					font-weight: 500;
					font-size: 0.14rem;
				}

				.link1 {
					background: #f2f2f2;
					color: #999999;
				}

				.link2 {
					background: #a8ec7a;
					color: #333333;
				}
			}
		}
	}
</style>