<template>
	<div class="subscribeDetail">
		<div class="headf">
			<top :title="$t(text)"></top>
		</div>
		<div class="info">
			<div class="title">{{$t('other').txt15}}</div>
			<div class="desc flex flex-b flex_wrap">
				<div class="item flex flex-b">{{$t('other').txt16}}<span>{{info.name}}</span></div>
				<div class="item flex flex-b">{{$t('subscribe').txt1}}<span>{{$formatMoney(info.bprice)}}円</span></div>
				<div class="item flex flex-b">{{$t('subscribe').txt2}}<span>{{$formatMoney(info.price)}}円</span></div>
				<div class="item flex flex-b">{{$t('subscribe').txt3}}<span>{{$formatMoney(info.num,0)}}</span></div>
			</div>
			<div class="time">
				<div class="line"></div>
				<div class="flex">
					<div class="point flex flex-c">
						<div class="point02"></div>
					</div>
					<div class="item flex">
						<div class="tt">{{$t('subscribe').txt4}}</div>
						<span>{{$formatDate("DD/MM/YYYY",info.start*1000)}}</span>
					</div>
				</div>
				<div class="line02"></div>
				<div class="flex">
					<div class="point flex flex-c">
						<div class="point02"></div>
					</div>
					<div class="item flex">
						<div class="tt">{{$t('subscribe').txt5}}</div>
						<span>{{$formatDate("DD/MM/YYYY",info.end*1000)}}</span>
					</div>
				</div>
				<div class="line02"></div>
				<div class="flex">
					<div class="point flex flex-c">
						<div class="point02"></div>
					</div>
					<div class="item flex">
						<div class="tt">{{$t('subscribe').txt6}}</div>
						<span>{{changeDate(info.ss_date)}}</span>
					</div>
				</div>
			</div>
		</div>
		<div class="buyNum">
			<div class="title flex flex-b">
				<div>{{$t('subscribe').txt17}}</div>
				<div class="money text-right">{{$t('recharge').txt1}}：<span>{{$formatMoney(userInfo.jpy)}}円</span></div>
			</div>
			<input class="flex" v-model="handle" :placeholder="$t('subscribe').txt18" />
			<div class="btn-big" @click="clickBuy">{{$t('subscribe').txt11}}</div>
		</div>
		<div class="pop popLoad" v-if="!flagBuy"></div>
	</div>
</template>

<script>
	import Vue from "vue";
	import qs from "qs";
	import axios from "axios";
	import {
		Toast
	} from "vant";
	Vue.use(Toast);

	import top from "../bar/toper.vue";

	export default {
		name: "subscribe",
		data() {
			return {
				text: this.$t('subscribe').title,
				flagBuy: true,
				info: {},
				userInfo: {},
				handle: ''
			};
		},
		components: {
			top,
		},
		destroyed() {},
		mounted() {
			if (this.$route.query.item) {
				this.info = JSON.parse(this.$route.query.item)
				this.getInfo();
			}
			this.getUser();
		},
		methods: {
			changeDate(time) {
				if (!time) {
					return time;
				}
				let date = new Date(time.replace('-', '/').replace('-', '/'));
				let Year = date.getFullYear();
				let Moth = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1);
				let Day = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate());
				let Hour = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours());
				let Minute = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes());
				let Sechond = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds());
				let GMT = Day + '/' + Moth + '/' + Year;
				return GMT
			},
			getInfo() {
				let now = new Date().getTime();
				// 可申购
				if (this.info.start * 1000 < now && now < this.info.end * 1000) {
					this.info.time = Math.floor((this.info.end * 1000 - now) / 1000 / 60 / 60 / 24);
					this.info.isKsg = true; //是否可申购
				} else if (now < this.info.start * 1000) {
					this.info.time = Math.floor((this.info.start * 1000 - now) / 1000 / 60 / 60 / 24);
					// 待申购
					this.info.isKsg = false;
				} else {
					this.info.isKsg = null;
				}
				if (now >= new Date(this.info.ss_date).getTime()) {
					this.info.isJZ = true;
				} else {
					this.info.isJZ = false;
				}
			},
			getUser() {
				this.$server.post('/user/getUserinfo', {}).then(res => {
					if (res.data.status == 1 || res.data.status == "1") {
						this.userInfo = res.data.data;
					}
				});
			},
			clickBuy() {
				let _this = this;
				this.flagBuy = false;
				this.$server.post('/trade/buy_newstock', {
					symbol: this.info.code,
					zhang: this.handle,
          buy_type: 0,
          type: 'jpy',
					id: this.info.id
				}).then(res => {
					this.flagBuy = true;
					Toast({
						message: this.$t(res.data.msg),
						duration: 2000,
					});
					if (res.status === 1) {
						setTimeout(function() {
							_this.clickNext('/position/position');

						}, 1000)
					}
				});
			}
		},

	};
</script>

<style lang="less">
	body {
		overflow-x: hidden;
	}

	.subscribeDetail {
		padding-top: .44rem;
		min-height: 100vh;
		width: 100%;
		background: #0F161C;
		overflow-x: hidden;

		.headf {
			background: #fff;
			position: fixed;
			z-index: 888;
			top: 0;
			left: 0;
			width: 100%;
			height: .44rem;
		}

		.info {
			margin-top: 0.01rem;
			position: relative;
			z-index: 20;

			.title {
				padding: 0.05rem 0.12rem;
				background-color: rgba(94, 213, 168, 0.2);
				border-bottom: .03rem solid #fff;
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 0.14rem;
				color: #FFFFFF;
			}

			.desc {
				.item {
					width: 100%;
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.13rem;
					color: #B3B8B8;
					padding: 0.12rem;
					border-bottom: 0.01rem solid rgba(255, 255, 255, 0.16);

					span {
						font-family: PingFangSC, PingFang SC;
						font-weight: 500;
						font-size: 0.13rem;
						color: #FFFFFF;
					}
				}
			}

			.time {
				padding: .1rem .15rem;
				align-items: flex-start;

				.line {
					margin-left: 0.06rem;
					width: 0.02rem;
					height: 0.25rem;
					background: #5ED5A8;
				}

				.line02 {
					margin-left: 0.06rem;
					width: 0.02rem;
					height: 0.35rem;
					background: #5ED5A8;
				}

				.point {
					box-sizing: border-box;
					width: 0.15rem;
					height: 0.15rem;
					border: 0.01rem solid #5ED5A8;
					border-radius: 50%;

					.point02 {
						width: 70%;
						height: 70%;
						background: #5ED5A8;
						border-radius: 50%;
					}
				}

				.item {
					margin-left: 0.2rem;
					width: 100%;
					line-height: 0.1rem;

					.tt {
						width: 30%;
						font-family: PingFangSC, PingFang SC;
						font-weight: 500;
						font-size: 0.13rem;
						color: #B3B8B8;
					}

					span {
						font-family: PingFangSC, PingFang SC;
						font-weight: 500;
						font-size: 0.13rem;
						color: #FFFFFF;
						display: block;
					}
				}
			}
		}

		.buyNum {
			margin: 0.1rem 0.12rem;

			.title {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.12rem;
				color: #FFFFFF;
				margin-bottom: .1rem;
				margin-top: .05rem;

				.money {
					font-size: .12rem;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					color: #999999;

					span {
						color: #E97F88;
					}
				}
			}

			input {
				height: .48rem;
				width: 100%;
				background: #000000;
				border-radius: 0.1rem;
				padding: 0 .1rem;
				font-size: .13rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #fff;
			}

			.btn-big {
				margin: .2rem 0;
			}
		}
	}
</style>